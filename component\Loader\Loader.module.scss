.loaderContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
  z-index: 9999;
}

.loader {
  // display: flex;
  // justify-content: center;
  // align-items: center;
  position: absolute;
  top: 41%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.spinner {
  width: 30px;
  height: 30px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

.img {
  // animation: slideUpDown 4s infinite ease-in-out;

  max-width: 30px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideUpDown {
  0%,
  100% {
    transform: translateY(-30px);
  }
  50% {
    transform: translateY(
      30px
    ); /* Adjust the value to control the slide distance */
  }
}
