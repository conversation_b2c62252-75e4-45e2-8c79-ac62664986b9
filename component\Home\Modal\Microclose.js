import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { Text } from "@react-three/drei";
import Loader from "@/component/Loader/Loaders";
import * as THREE from "three";

function Microcloser({ position, scale }) {
  const ref = useRef();
  const { nodes, materials } = useGLTF("/microclosed.glb");
  const bagName = "Micro Bag";
  const [isHovered, setIsHovered] = useState(false);
  
  useFrame((state, delta) => {
    if (isHovered) {
      // Move up and rotate when hovered
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      ref.current.rotation.y -= 0.005;
    } else {
      // Return to original position
      easing.damp3(ref.current.position, position, 0.2, delta);
      // Smoothly return rotation to 0
      easing.dampE(ref.current.rotation, [0, 0, 0], 0.25, delta);
    }
  });

  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale || DEFAULT_SCALE} // Use the default scale if not provided
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        dispose={null}
      >
        <mesh
          geometry={nodes.Bottom_Plate.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Magnet_Closure.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Object003.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Cylinder002.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Box005.geometry}
          material={new THREE.MeshStandardMaterial({})}
        />
        <mesh
          geometry={nodes.Shape009.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Mirror.geometry}
          material={new THREE.MeshStandardMaterial({})}
        />
        <mesh
          geometry={nodes.Outer_Layer.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Inner_Layer.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Middle_Layer.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Object005.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Object006.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Shape010.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Shape011.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Shape012.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
        <mesh
          geometry={nodes.Shape013.geometry}
          material={
            new THREE.MeshStandardMaterial({})
          }
        />
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={2.3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}
export default Microcloser;
useGLTF.preload("/microclosed.glb");