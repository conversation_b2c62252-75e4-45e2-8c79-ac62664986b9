const createLoadingManager = () => {
  const loadingStates = new Map();
  const listeners = new Set();
  
  const manager = {
    setLoading: (key, isLoading, timeout = 10000) => {
      if (isLoading) {
        loadingStates.set(key, true);
        console.log(`🔄 Loading started: ${key}`);
        
        // Auto-timeout to prevent infinite loading
        setTimeout(() => {
          if (loadingStates.has(key)) {
            console.warn(`⏰ Loading timeout for ${key}, forcing completion`);
            manager.setLoading(key, false);
          }
        }, timeout);
      } else {
        loadingStates.delete(key);
        console.log(`✅ Loading completed: ${key}`);
      }
      
      // Notify all listeners
      const isAnyLoading = loadingStates.size > 0;
      listeners.forEach(callback => callback(isAnyLoading, key));
    },
    
    subscribe: (callback) => {
      listeners.add(callback);
      return () => listeners.delete(callback);
    },
    
    isLoading: () => loadingStates.size > 0,
    getActiveLoaders: () => Array.from(loadingStates.keys()),
    clear: () => {
      loadingStates.clear();
      listeners.forEach(callback => callback(false, 'clear'));
    }
  };
  
  return manager;
};