import { useState, useEffect, useRef } from "react";
import styles from "./Tabmanager.module.scss";
import { HiOutlinePlusCircle } from "react-icons/hi";
import { IoBagHandleOutline } from "react-icons/io5";
import { useRouter } from "next/router";
import { FaLongArrowAltLeft, FaLongArrowAltRight } from "react-icons/fa";
import ScrollContainer from "react-indiana-drag-scroll";
import { LuMoveRight, LuMoveLeft } from "react-icons/lu";
import Link from "next/link";
import Loaders from "../Loader/Loaders";
import CartComponent from "../Home/Cart/CartPopup";

const TabManagerTotem = ({
  onColorChange,
  onStrapChange,
  handlestrapcolorchange,
  handlestrap2colorchange, // Added this prop
  strapopacity,
  handlestraptab,
  sethandlestraptab,
  setanimation,
  setanimationsource,
  sethoveredsphere,
  setSelectedStrap,
  setSelectedStraps,
  selectedStraps,
  lastClickedShoulderStrap,
  setLastClickedShoulderStrap,
  lastClickedStrap,
  setLastClickedStrap,
  activeTab,
  setActiveTab,
  xrayMode,
}) => {
  const [selectedColor, setSelectedColor] = useState("beige");
  const [showColorStraps, setShowColorStraps] = useState(false);
  const [selectedColorStrap, setSelectedColorStrap] = useState("beige");
  const [showShoulderColorStraps, setShowShoulderColorStraps] = useState(false);
  const [selectedShoulderColorStrap, setSelectedShoulderColorStrap] =
    useState(null);
  const [selectStrap, setSelectStrap] = useState(null);
  const [activeStrapType, setActiveStrapType] = useState(null);
  // const [activeTab, setActiveTab] = useState(1);
  const [dynamicname, setdynamicname] = useState("Handle Straps");
  const [showloopunloop, setshowloopunloop] = useState(false);
  const [selectedLoopState, setSelectedLoopState] = useState(null);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [cartItems, setCartItems] = useState([]);

  // Add state to track individual strap colors
  const [strapColors, setStrapColors] = useState({
    handle: null,
    metal: null,
    ball: null,
    strapm: null,
    sidestrap3: null,
    logo: null,
    logo1: null,
    shoulder: null,
  });

  const [currentBagConfig, setCurrentBagConfig] = useState({
    bagType: "TOTEM BAG",
    bagColor: "beige",
    strapType: [],
    strapColor: null,
    loopState: null,
    price: 7500.0,
  });

  const cartRef = useRef();

  // Loader states
  const [showLoader, setShowLoader] = useState(false);
  const [showLoader1, setShowLoader1] = useState(false);
  const [showLoader2, setShowLoader2] = useState(false);
  const [showLoader3, setShowLoader3] = useState(false);
  const [showLoader4, setShowLoader4] = useState(false);
  const [showLoader5, setShowLoader5] = useState(false);

  const router = useRouter();
  const scrollRef = useRef(null);
  const touchStartX = useRef(0);
  const touchStartScrollLeft = useRef(0);

  useEffect(() => {
    const loadCart = () => {
      try {
        const savedCart = localStorage.getItem("cart");
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
          console.log("Loaded cart from localStorage:", parsedCart);
        }
      } catch (error) {
        console.error("Error loading cart:", error);
      }
    };
    loadCart();
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem("cart", JSON.stringify(cartItems));
      console.log("Saved cart to localStorage:", cartItems);
    } catch (error) {
      console.error("Error saving cart:", error);
    }
  }, [cartItems]);

  const tabs = [
    { id: 1, name: "Colour", icon: "" },
    { id: 2, name: dynamicname, icon: "" },
    { id: 3, name: "Shoulder Strap", icon: "" },
    { id: 4, name: "Models", icon: "" },
    // { id: 5, name: "Add To Cart", icon: <IoBagHandleOutline /> },
  ];

  // Helper function to get default color for new straps
  const getDefaultStrapColor = (strapType) => {
    if (strapColors[strapType]) {
      return strapColors[strapType];
    }
    return currentBagConfig.bagColor;
  };

  // Helper function to get color name from hex value
  const getColorNameFromHex = (hexValue) => {
    const colorMap = {
      "#000000": "black",
      "#8a0e12": "red",
      "#ffe6d4": "beige",
      "#808080": "grey",
    };
    return colorMap[hexValue] || "beige";
  };

  // Helper function to get hex value from color name
  const getHexFromColorName = (colorName) => {
    const colorMap = {
      black: "#000000",
      red: "#8a0e12",
      beige: "#ffe6d4",
      grey: "#808080",
    };
    return colorMap[colorName] || "#ffe6d4";
  };

  const generateBagId = (config) => {
    const { bagType, bagColor } = config;
    return `${bagType.toLowerCase().replace(/\s+/g, "_")}_${bagColor}`;
  };

  const handleStrapRemovedFromCart = (removedStrapType) => {
    console.log("Strap removed from cart:", removedStrapType);

    const updatedStrapType = currentBagConfig.strapType.filter(
      (strap) => strap.type !== removedStrapType
    );

    updateBagConfig({
      strapType: updatedStrapType,
    });

    if (selectedStraps.handle === removedStrapType) {
      setSelectedStraps({
        ...selectedStraps,
        handle: null,
      });
    }
    if (selectedStraps.shoulder === removedStrapType) {
      setSelectedStraps({
        ...selectedStraps,
        shoulder: null,
      });
    }

    if (lastClickedStrap === removedStrapType) {
      setLastClickedStrap(null);
    }
    if (lastClickedShoulderStrap === removedStrapType) {
      setLastClickedShoulderStrap(null);
    }

    console.log("Updated config after strap removal:", {
      strapType: updatedStrapType,
      selectedStraps: selectedStraps,
    });
  };

  const getBagImage = (config) => {
    const { bagType, bagColor } = config;
    const colorMap = {
      black: "black",
      red: "red",
      beige: "beige",
      grey: "grey",
    };

    const bagTypeMap = {
      "MICRO BAG": "microbag",
      "PHONE POUCH": "pouch",
      "TOTE BAG": "totes",
      "PANIER BAG": "panier",
      "TOTEM BAG": "totem",
      "TOTEL BAG": "totel",
    };

    const bagFolder = bagTypeMap[bagType] || "totem";
    return `/colourbag/${bagFolder}/${colorMap[bagColor] || "beige"}.png`;
  };

  const getStrapDisplayName = (strapType) => {
const strapNames = {
  handle: "Handle Strap",
  metal: "Core-Link Chain™",
  ball: "Ball-Link Chain™",
  strapm: "Strap M",
  sidestrap3: "Strap V",
  logo: "Monogram Link Chain",
  logo1: "Flowlink Chain",
  shoulder: "Shoulder Strap",
};

    return strapNames[strapType] || strapType;
  };

  const calculatePrice = (config) => {
    let basePrice = 7500.0; // Totem bag base price

    if (config.strapType && config.strapType.length > 0) {
      const strapPrices = {
        handle: 200,
        metal: 350,
        ball: 400,
        strapm: 250,
        sidestrap3: 220,
        logo: 380,
        logo1: 420,
        shoulder: 300,
      };

      config.strapType.forEach((strap) => {
        if (strap.type) {
          basePrice += strapPrices[strap.type] || 0;
        }
      });
    }

    return basePrice;
  };

  const updateBagConfig = (updates) => {
    setCurrentBagConfig((prev) => {
      const newConfig = { ...prev, ...updates };
      newConfig.price = calculatePrice(newConfig);
      console.log("Updated bag config:", newConfig);
      return newConfig;
    });
  };

  const isStrapSelected = (strapType) => {
    return currentBagConfig.strapType.some((strap) => strap.type === strapType);
  };

  const removeStrapFromConfig = (strapType) => {
    const updatedStrapType = currentBagConfig.strapType.filter(
      (strap) => strap.type !== strapType
    );

    updateBagConfig({
      strapType: updatedStrapType,
    });

    setStrapColors((prev) => ({
      ...prev,
      [strapType]: null,
    }));

    if (selectedStraps.handle === strapType) {
      setSelectedStraps({
        ...selectedStraps,
        handle: null,
      });
    }
    if (selectedStraps.shoulder === strapType) {
      setSelectedStraps({
        ...selectedStraps,
        shoulder: null,
      });
    }

    console.log("Removed strap:", strapType);
  };

  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartScrollLeft.current = scrollRef.current.scrollLeft;
  };

  const handleTouchMove = (e) => {
    if (scrollRef.current) {
      const touchX = e.touches[0].clientX;
      const touchDeltaX = touchX - touchStartX.current;
      scrollRef.current.scrollLeft = touchStartScrollLeft.current - touchDeltaX;
    }
  };

  const handleTouchEnd = () => {
    // Touch end handling if needed
  };

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("touchstart", handleTouchStart, {
        passive: true,
      });
      scrollContainer.addEventListener("touchmove", handleTouchMove, {
        passive: true,
      });
      scrollContainer.addEventListener("touchend", handleTouchEnd, {
        passive: true,
      });
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("touchstart", handleTouchStart);
        scrollContainer.removeEventListener("touchmove", handleTouchMove);
        scrollContainer.removeEventListener("touchend", handleTouchEnd);
      }
    };
  }, []);

  useEffect(() => {
    console.log("handlestraptab changed:", handlestraptab);
    if (handlestraptab === 1) {
      setActiveTab(4);
    }
  }, [handlestraptab]);

  const handleStrap = [
    {
      id: 1,
      name: "Handle Straps 1",
      image: "/handlestrap/new/hs3.png",
      type: "handle",
    },
    {
      id: 2,
      name: "Handle Straps 2",
      image: "/handlestrap/new/h3.png",
      type: "metal",
    },
    {
      id: 3,
      name: "Logo Strap",
      image: "/handlestrap/new/c3.png",
      type: "logo",
    },
    {
      id: 4,
      name: "Logo Strap 1",
      image: "/handlestrap/new/m3.png",
      type: "logo1",
    },
    {
      id: 5,
      name: "strapm",
      image: "/handlestrap/new/hs3.png",
      type: "strapm",
    },
  ];

  const handleColorStrap = [
    {
      id: 1,
      name: "Color Straps 1",
      image: "/handlestrap/new/hs4.png",
      type: "handle",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 2,
      name: "Color Straps 2",
      image: "/handlestrap/new/hs2.png",
      type: "handle",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 3,
      name: "Color Straps 3",
      image: "/handlestrap/new/hs1.png",
      type: "handle",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 4,
      name: "Color Straps 4",
      image: "/handlestrap/new/hs3.png",
      type: "handle",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 5,
      name: "Color Straps 1",
      image: "/handlestrap/new/h4.png",
      type: "metal",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 6,
      name: "Color Straps 2",
      image: "/handlestrap/new/h2.png",
      type: "metal",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 7,
      name: "Color Straps 3",
      image: "/handlestrap/new/h1.png",
      type: "metal",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 8,
      name: "Color Straps 4",
      image: "/handlestrap/new/h3.png",
      type: "metal",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 9,
      name: "Color Straps 1",
      image: "/handlestrap/new/hs4.png",
      type: "strapm",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 10,
      name: "Color Straps 2",
      image: "/handlestrap/new/hs2.png",
      type: "strapm",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 11,
      name: "Color Straps 3",
      image: "/handlestrap/new/hs1.png",
      type: "strapm",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 12,
      name: "Color Straps 4",
      image: "/handlestrap/new/hs3.png",
      type: "strapm",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 13,
      name: "Color Straps 1",
      image: "/handlestrap/new/hs4.png",
      type: "sidestrap3",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 14,
      name: "Color Straps 2",
      image: "/handlestrap/new/hs2.png",
      type: "sidestrap3",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 15,
      name: "Color Straps 3",
      image: "/handlestrap/new/hs1.png",
      type: "sidestrap3",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 16,
      name: "Color Straps 4",
      image: "/handlestrap/new/hs3.png",
      type: "sidestrap3",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 17,
      name: "Color Straps 1",
      image: "/handlestrap/new/c4.png",
      type: "logo",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 18,
      name: "Color Straps 2",
      image: "/handlestrap/new/c2.png",
      type: "logo",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 19,
      name: "Color Straps 3",
      image: "/handlestrap/new/c1.png",
      type: "logo",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 20,
      name: "Color Straps 4",
      image: "/handlestrap/new/c3.png",
      type: "logo",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 21,
      name: "Color Straps 1",
      image: "/handlestrap/new/m4.png",
      type: "logo1",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 22,
      name: "Color Straps 2",
      image: "/handlestrap/new/m2.png",
      type: "logo1",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 23,
      name: "Color Straps 3",
      image: "/handlestrap/new/m1.png",
      type: "logo1",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 24,
      name: "Color Straps 4",
      image: "/handlestrap/new/m3.png",
      type: "logo1",
      color: "beige",
      colorValue: "#ffe6d4",
    },
  ];

  const shoulderStrap = [
    {
      id: 1,
      name: "Shoulder Strap 1",
      image: "/handlestrap/shoulderStrap/s3.png",
      type: "shoulder",
    },
  ];

  const shoulderColorStrap = [
    {
      id: 1,
      name: "Color Straps 1",
      image: "/handlestrap/shoulderStrap/s4.png",
      type: "shoulder",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 2,
      name: "Color Straps 2",
      image: "/handlestrap/shoulderStrap/s2.png",
      type: "shoulder",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 3,
      name: "Color Straps 3",
      image: "/handlestrap/shoulderStrap/s1.png",
      type: "shoulder",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 4,
      name: "Color Straps 4",
      image: "/handlestrap/shoulderStrap/s3.png",
      type: "shoulder",
      color: "beige",
      colorValue: "#ffe6d4",
    },
  ];

  const handleModelSelection = (newBagType, loaderFunction) => {
    console.log(
      "Changing bag type from",
      currentBagConfig.bagType,
      "to",
      newBagType
    );

    updateBagConfig({
      bagType: newBagType,
    });

    loaderFunction(true);
  };

  const handleStrapMouseEnter = (strapType) => {
    setanimation(true);

    const strapDescriptions = {
      handle: {
        description:
          "<b>HANDLE STRAP</b><br><br>A handle strap crafted in premium Epsom leather, reinforced for daily durability and finished with custom-engineered hinged hooks.<br>Designed for a secure grip and seamless adaptability.<br><br>Available in four colourways.",
        animation: "/ball1.mp4",
        setAnimationState: false,
      },
      metal: {
        description:
          "<b>CORE-LINK CHAIN™</b><br><br>Crafted from premium Rhodoid and engineered through CNC milling and laser cutting for precision articulation.<br>Developed to balance flexibility with structural integrity, each fine hinge moves fluidly with the bag's weight while retaining a sculptural form.<br><br>Lightweight, resilient, and tactile — a modular element designed for movement and form.<br><br>Available in four colourways.",
        animation: "/plastic.mp4",
        setAnimationState: true,
      },
      ball: {
        description:
          "<b>BALL-LINK CHAIN™</b><br><br>A chain of precision-formed Rhodoid spheres, connected through a reinforced nylon core and finished with WMB's custom hinge hooks.<br>Designed for balance and flow, the WMB BALL LINK CHAIN™ offers two diameters — 6mm and 12mm — and is available in four distinctive colours.",
        animation: "/ball1.mp4",
        setAnimationState: true,
      },
      strapm: {
        description:
          "<b>STRAP M</b><br><br>Crafted in reinforced Epsom leather, the Strap M offers versatile wear — over the shoulder or looped casually for hand-carry.<br>Designed as the go-to strap for everyday, effortless, carefree movement.<br>Available in four colourways.",
        animation: null,
        setAnimationState: false,
      },
      sidestrap3: {
        description:
          "<b>STRAP V</b><br><br>Crafted in reinforced Epsom leather, the Strap V offers versatile wear — over the shoulder or looped casually for hand-carry.<br>Designed as the go-to strap for everyday, effortless, carefree movement.<br>Available in four colourways.",
        animation: null,
        setAnimationState: false,
      },
      logo: {
        description:
          "<b>MONOGRAM LINK CHAIN</b><br><br>Crafted from premium Rhodoid and precision-engineered through CNC milling and laser cutting. Each articulated segment is perforated with the WMB monogram — a subtle signature integrated into the chain structure.<br>Designed for fluid movement and sculptural form, it balances identity with engineering.<br>Available in four colourways.",
        animation: "/logochain.mp4",
        setAnimationState: true,
      },
      logo1: {
        description:
          "<b>FLOWLINK CHAIN</b><br><br>Inspired by the articulated wristbands of luxury timepieces, the FLOWLINK CHAIN is CNC-milled and laser-cut from premium Rhodoid to achieve seamless fluidity. Each segment is precision-engineered to move without resistance, forming a soft, continuous drape that adapts naturally to the bag and body.<br>Unlike the CORE LINK models, the FLOWLINK CHAIN does not retain shape — it's designed to flow.<br>Lightweight, tactile, and endlessly flexible — a sculptural element made for motion.<br>Available in four colourways.",
        animation: "/logochain1.mp4",
        setAnimationState: true,
      },
    };

    const strapInfo = strapDescriptions[strapType];
    if (strapInfo) {
      sethoveredsphere?.(strapInfo.description);
      setanimation(strapInfo.setAnimationState);
      if (strapInfo.animation) {
        setanimationsource(strapInfo.animation);
      }
    }
  };

  const handleStrapMouseLeave = () => {
    setanimation(false);
    sethoveredsphere(false);
  };

  const handleTabClick = (tabId) => {
    if (tabId === 1) {
      router.push("/");
      return;
    }

    setActiveTab(tabId);
    setShowColorStraps(false);
    setShowShoulderColorStraps(false);

    if (tabId === 3) {
      setActiveStrapType("handle");
      setSelectedStraps?.((prev) => ({ ...prev, shoulder: null }));
    } else if (tabId === 4) {
      setActiveStrapType("shoulder");
      setSelectedStraps?.((prev) => ({ ...prev, handle: null }));
    }
  };

  const handleColorChange = (color, colorName) => {
    setSelectedColor(colorName);
    updateBagConfig({ bagColor: colorName });

    const colorConfigs = {
      black: { plastic: "#060606", leather: "#232424", seams: "#1B1B1C" },
      red: { plastic: "#5F292F", leather: "#680D18", seams: "#671E23" },
      beige: { plastic: "#E1D8CF", leather: "#C7B8A9", seams: "#D0C6BB" },
      grey: { leather: "#6A7075", plastic: "#7C8287", seams: "#8E9499" },
    };

    const tempcolors = colorConfigs[colorName];
    if (tempcolors) {
      onColorChange(tempcolors);
    }
  };

  // Updated handleShoulderStrapClick - now uses handlestrap2colorchange
  const handleShoulderStrapClick = (value) => {
    if (lastClickedShoulderStrap === value) {
      // If clicking the same strap again, show color options
      setShowShoulderColorStraps(true);
      if (strapopacity) {
        strapopacity(1);
      }

      // Get the current color of this specific strap
      const currentStrap = currentBagConfig.strapType.find(
        (strap) => strap.type === value
      );
      const currentStrapColor =
        currentStrap?.color || getDefaultStrapColor(value);

      // Set the color state to this strap's current color
      setSelectedShoulderColorStrap(currentStrapColor);
      handlestrap2colorchange(currentStrapColor); // Use handlestrap2colorchange for shoulder straps
    } else {
      // For new strap selection - REPLACE any existing strap, don't add to it
      setSelectedStraps({
        ...selectedStraps,
        shoulder: value,
      });

      // CLEAR all existing straps and only add the new one
      const defaultColor = "beige";

      updateBagConfig({
        strapType: [{ type: value, color: defaultColor }], // Replace entire array with just this strap
      });

      // Update individual strap color tracking - clear others
      setStrapColors((prev) => ({
        handle: null,
        metal: null,
        ball: null,
        strapm: null,
        sidestrap3: null,
        logo: null,
        logo1: null,
        shoulder: defaultColor,
      }));

      // Set the selected color to beige and show color options immediately
      setSelectedShoulderColorStrap(defaultColor);
      handlestrap2colorchange(defaultColor); // Use handlestrap2colorchange for shoulder straps
      setShowShoulderColorStraps(true); // Show colors immediately
      if (strapopacity) {
        strapopacity(1);
      }

      onStrapChange(value);
      setLastClickedShoulderStrap(value);
    }
  };

const handleLoopChange = (loopState) => {
  const defaultColor = "beige"; // Always use beige as default

  if (loopState === "loop") {
    // First remove the current strap visually
    if (selectStrap === "strapm") {
      onStrapChange("remove_strapm"); // or however your remove function works
    }
    
    setSelectedLoopState("loop");
    updateBagConfig({
      loopState: "loop",
      strapType: [{ type: "sidestrap3", color: defaultColor }], // Replace entire array
    });
    setSelectStrap("sidestrap3");
    setSelectedColorStrap(defaultColor);
    setShowColorStraps(true);
    
    // Update the visual representation
    onStrapChange("sidestrap3");
    handlestrapcolorchange(defaultColor); // Use handlestrapcolorchange for handle straps

    // Clear other strap colors and ensure only sidestrap3 is active
    setStrapColors({
      handle: null,
      metal: null,
      ball: null,
      strapm: null,
      sidestrap3: defaultColor,
      logo: null,
      logo1: null,
      shoulder: null,
    });
    
    // Update selectedStraps to reflect only the current strap
    setSelectedStraps((prev) => ({
      ...prev,
      handle: "sidestrap3",
    }));
    
  } else if (loopState === "unloop") {
    // First remove the current strap visually
    if (selectStrap === "sidestrap3") {
      onStrapChange("remove_sidestrap3"); // or however your remove function works
    }
    
    console.log("Switching to unloop (strapm)");
    updateBagConfig({
      loopState: "unloop",
      strapType: [{ type: "strapm", color: defaultColor }], // Replace entire array
    });
    setSelectStrap("strapm");
    setSelectedLoopState("unloop");
    setSelectedColorStrap(defaultColor);
    setShowColorStraps(true);
    
    // Update the visual representation
    onStrapChange("strapm");
    handlestrapcolorchange(defaultColor); // Use handlestrapcolorchange for handle straps

    // Clear other strap colors and ensure only strapm is active
    setStrapColors({
      handle: null,
      metal: null,
      ball: null,
      strapm: defaultColor,
      sidestrap3: null,
      logo: null,
      logo1: null,
      shoulder: null,
    });
    
    // Update selectedStraps to reflect only the current strap
    setSelectedStraps((prev) => ({
      ...prev,
      handle: "strapm",
    }));
  }
};

  // Fixed handleHandleStrapClick - uses handlestrapcolorchange
  const handleHandleStrapClick = (value) => {
    if (lastClickedStrap === value) {
      // If clicking the same strap again, show color options
      setShowColorStraps(true);
      if (strapopacity) {
        strapopacity(1);
      }

      // Get the current color of this specific strap
      const currentStrap = currentBagConfig.strapType.find(
        (strap) => strap.type === value
      );
      const currentStrapColor =
        currentStrap?.color || getDefaultStrapColor(value);

      // Set the color state to this strap's current color
      setSelectedColorStrap(currentStrapColor);
      handlestrapcolorchange(currentStrapColor); // Use handlestrapcolorchange for handle straps
    } else {
      // For new strap selection - REPLACE any existing strap, don't add to it
      setSelectedStraps({
        ...selectedStraps,
        handle: value,
      });

      // CLEAR all existing straps and only add the new one
      const defaultColor = "beige";

      updateBagConfig({
        strapType: [{ type: value, color: defaultColor }], // Replace entire array with just this strap
      });

      // Update individual strap color tracking - clear others
      setStrapColors((prev) => ({
        handle: defaultColor,
        metal: null,
        ball: null,
        strapm: null,
        sidestrap3: null,
        logo: null,
        logo1: null,
        shoulder: null,
      }));

      // Set the selected color to beige and show color options immediately
      setSelectedColorStrap(defaultColor);
      handlestrapcolorchange(defaultColor); // Use handlestrapcolorchange for handle straps
      setShowColorStraps(true); // Show colors immediately
      if (strapopacity) {
        strapopacity(1);
      }

      setdynamicname(getStrapDisplayName(value));
      onStrapChange(value);
      setLastClickedStrap(value);
    }
  };

  // Updated handleStrapColorChange - uses handlestrapcolorchange for handle straps
  const handleStrapColorChange = (colorValue, colorName) => {
    const finalColorName = colorName || getColorNameFromHex(colorValue);

    handlestrapcolorchange(finalColorName); // Use handlestrapcolorchange for handle straps
    setSelectedColorStrap(finalColorName);

    const strapToUpdate = selectStrap || lastClickedStrap;

    setStrapColors((prev) => ({
      ...prev,
      [strapToUpdate]: finalColorName,
    }));

    const updatedStrapType = currentBagConfig.strapType.map((strap) => {
      if (strap.type === strapToUpdate) {
        return { ...strap, color: finalColorName };
      }
      return strap;
    });

    updateBagConfig({
      strapType: updatedStrapType,
      strapColor: finalColorName,
    });
  };

  // Updated handleShoulderStrapColorChange - uses handlestrap2colorchange for shoulder straps
  const handleShoulderStrapColorChange = (colorValue, colorName) => {
    const finalColorName = colorName || getColorNameFromHex(colorValue);

    handlestrap2colorchange(finalColorName); // Use handlestrap2colorchange for shoulder straps
    setSelectedShoulderColorStrap(finalColorName);

    const strapToUpdate = selectedStraps.shoulder || lastClickedShoulderStrap;

    setStrapColors((prev) => ({
      ...prev,
      [strapToUpdate]: finalColorName,
    }));

    const updatedStrapType = currentBagConfig.strapType.map((strap) => {
      if (strap.type === strapToUpdate) {
        return { ...strap, color: finalColorName };
      }
      return strap;
    });

    updateBagConfig({
      strapType: updatedStrapType,
      strapColor: finalColorName,
    });
  };

  const handleNextTab = () => {
    if (activeTab < tabs.length) {
      setActiveTab(activeTab + 1);
    }
  };

  const handlePrevTab = () => {
    if (showShoulderColorStraps) {
      setShowShoulderColorStraps(false);
      return;
    }
    if (showColorStraps) {
      setShowColorStraps(false);
      return;
    }
    if (activeTab > 1) {
      setActiveTab(activeTab - 1);
    }
  };

  const [isHovered, setIsHovered] = useState(false);

  const getTabDisplayName = () => {
    const currentTab = tabs[activeTab - 1];
    if (currentTab?.id === 2 && isHovered) {
      return "All Straps";
    }
    return currentTab?.name || "";
  };

  const handleAllStrapsClick = () => {
    setActiveTab(2);
    setIsHovered(false);
    setShowColorStraps(false);
  };

  const handleAddToCart = () => {
    console.log("=== ADD TO CART - ONE BAG ONLY ===");
    console.log("Current bag config:", currentBagConfig);

    if (!currentBagConfig || !currentBagConfig.bagType) {
      console.error("No valid bag configuration");
      alert("Please configure your bag first");
      return;
    }

    if (cartRef.current) {
      cartRef.current.addToCart(currentBagConfig);
    }

    setIsCartOpen(true);

    console.log("=== END ADD TO CART ===");
  };

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 4:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.modelOptions}>
              {[
                {
                  img: "/homebag/microbag/grey.png",
                  alt: "Micro Bag",
                  loader: () =>
                    handleModelSelection("MICRO BAG", setShowLoader),
                },
                {
                  img: "/homebag/pouch/grey.png",
                  alt: "Phone Pouch",
                  loader: () =>
                    handleModelSelection("PHONE POUCH", setShowLoader1),
                },
                {
                  img: "/homebag/totes/grey.png",
                  alt: "Tote Bag",
                  loader: () =>
                    handleModelSelection("TOTE BAG", setShowLoader2),
                },
                {
                  img: "/homebag/panier/grey.png",
                  alt: "Panier Bag",
                  loader: () =>
                    handleModelSelection("PANIER BAG", setShowLoader3),
                },
                {
                  img: "/homebag/totem/grey.png",
                  alt: "Totem Bag",
                  loader: () =>
                    handleModelSelection("TOTEM BAG", setShowLoader4),
                },
                {
                  img: "/homebag/totel/grey.png",
                  alt: "Totel Bag",
                  loader: () =>
                    handleModelSelection("TOTEL BAG", setShowLoader5),
                },
              ].map((model, index) => (
                <div key={index} className={styles.modelOption}>
                  <div className={styles.modelImage1} onClick={model.loader}>
                    <img src={model.img} alt={model.alt} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 1:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.modelOptions}>
              {[
                { color: "black", name: "Black model" },
                { color: "red", name: "red model" },
                { color: "beige", name: "beige model" },
                { color: "grey", name: "grey model" },
              ].map((colorOption) => (
                <div
                  key={colorOption.color}
                  className={styles.modelOption}
                  onClick={() =>
                    handleColorChange(
                      `#${
                        colorOption.color === "black"
                          ? "000000"
                          : colorOption.color === "red"
                          ? "8a0e12"
                          : colorOption.color === "beige"
                          ? "ffe6d4"
                          : "808080"
                      }`,
                      colorOption.color
                    )
                  }
                >
                  <div className={styles.modelImage}>
                    <img
                      src={`/colourbag/totem/${colorOption.color}${
                        selectedColor === colorOption.color ? "1" : ""
                      }.png`}
                      alt={colorOption.name}
                      className={
                        selectedColor === colorOption.color
                          ? styles.clicked
                          : ""
                      }
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.shoulderOption}>
              {showColorStraps&&!xrayMode  ? (
                <>
                  {selectStrap &&
                    (selectStrap === "sidestrap3" ||
                      selectStrap === "strapm") &&
                    showloopunloop && (
                      <div className={styles.loopUnloopButtons}>
                        <button
                          className={`${styles.loopButton} ${
                            selectedLoopState === "loop"
                              ? styles.activeLoopButton
                              : ""
                          }`}
                          onClick={() => {
                            const newState =
                              selectedLoopState === "loop" ? "unloop" : "loop";
                            setSelectedLoopState(newState);
                            handleLoopChange(newState);
                            setshowloopunloop(true);
                          }}
                        >
                          {selectedLoopState === "loop" ? "unloop" : "loop"}
                        </button>
                      </div>
                    )}
                  {handleColorStrap
                    .filter((e) => e.type === selectStrap)
                    .map((value) => (
                      <div
                        key={value.id}
                        className={`${styles.modelOption} ${
                          selectedColorStrap === value.color
                            ? styles.colorSelected
                            : ""
                        }`}
                        onClick={() =>
                          handleStrapColorChange(value.colorValue, value.color)
                        }
                      >
                        <div className={styles.shouderImage}>
                          <img
                            src={value.image}
                            alt={value.name}
                            className={
                              selectedColorStrap === value.color
                                ? styles.clicked
                                : ""
                            }
                          />
                        </div>
                      </div>
                    ))}
                </>
              ) : (
                handleStrap.map((val) => (
                  <div
                    key={val.id}
                    className={`${styles.modelOption} ${
                      isStrapSelected(val.type) ? styles.strapSelected : ""
                    }`}
                    onClick={() => {
                      handleHandleStrapClick(val.type);
                      setSelectStrap(val.type);
                      setShowColorStraps(true);
                      setanimation(false);
                      if (val.type === "sidestrap3" || val.type === "strapm") {
                        setshowloopunloop(true);
                      }
                    }}
                    onMouseEnter={() => handleStrapMouseEnter(val.type)}
                    onMouseLeave={handleStrapMouseLeave}
                  >
                    <div className={styles.shouderImage}>
                      <img
                        src={val.image}
                        alt={val.name}
                        className={
                          selectedStraps.handle === val.type
                            ? styles.clicked
                            : ""
                        }
                      />
                      {/* {isStrapSelected(val.type) && (
                        <div className={styles.selectedIndicator}>✓</div>
                      )} */}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.shoulderOption}>
              {showShoulderColorStraps
                ? shoulderColorStrap.map((value) => (
                    <div
                      key={value.id}
                      className={`${styles.modelOption} ${
                        selectedShoulderColorStrap === value.color
                          ? styles.colorSelected
                          : ""
                      }`}
                      onClick={() => {
                        handleShoulderStrapColorChange(
                          value.colorValue,
                          value.color
                        );
                      }}
                    >
                      <div className={styles.shouderImage}>
                        <img
                          src={value.image}
                          alt={value.name}
                          className={
                            selectedShoulderColorStrap === value.color
                              ? styles.clicked
                              : ""
                          }
                        />
                      </div>
                    </div>
                  ))
                : shoulderStrap.map((val) => (
                    <div
                      key={val.id}
                      className={styles.modelOption}
                      onClick={() => handleShoulderStrapClick(val.type)}
                      onMouseEnter={() => {
                        setanimation?.(true);
                        setanimationsource?.("shoulder_animation");
                        sethoveredsphere?.("shoulder");
                      }}
                      onMouseLeave={() => {
                        setanimation?.(false);
                        sethoveredsphere?.(false);
                      }}
                    >
                      <div className={styles.shouderImage}>
                        <img
                          src={val.image}
                          alt={val.name}
                          className={
                            selectedStraps?.shoulder === val.type
                              ? styles.clicked
                              : ""
                          }
                        />
                      </div>
                    </div>
                  ))}
            </div>
          </div>
        );

      case 5:
        return (
          <div className={styles.cartContent}>
            <div className={styles.cartSummary}>
              <div className={styles.bagConfigSummary}>
                <h3>Your Configuration</h3>
                <div className={styles.configItem}>
                  <img
                    src={getBagImage(currentBagConfig)}
                    alt={currentBagConfig.bagType}
                    className={styles.bagPreview}
                  />
                </div>
                <div className={styles.configDetails}>
                  <p>
                    <strong>Bag:</strong> {currentBagConfig.bagType}
                  </p>
                  <p>
                    <strong>Color:</strong>{" "}
                    {currentBagConfig.bagColor.toUpperCase()}
                  </p>
                  {currentBagConfig.strapType &&
                    currentBagConfig.strapType.length > 0 && (
                      <div>
                        <p>
                          <strong>
                            Straps ({currentBagConfig.strapType.length}):
                          </strong>
                        </p>
                        {currentBagConfig.strapType.map((strap, index) => (
                          <div key={index} className={styles.strapConfigItem}>
                            <div
                              style={{
                                marginLeft: "20px",
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <div>
                                <p>• {getStrapDisplayName(strap.type)}</p>
                                {strap.color && (
                                  <p
                                    style={{
                                      marginLeft: "20px",
                                      fontSize: "0.9em",
                                      color: "#666",
                                    }}
                                  >
                                    Color: {strap.color.toUpperCase()}
                                  </p>
                                )}
                              </div>
                              <button
                                onClick={() =>
                                  removeStrapFromConfig(strap.type)
                                }
                                className={styles.removeStrapBtn}
                                style={{
                                  background: "#ff4444",
                                  color: "white",
                                  border: "none",
                                  borderRadius: "3px",
                                  padding: "2px 6px",
                                  fontSize: "10px",
                                  cursor: "pointer",
                                }}
                              >
                                Remove
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  {currentBagConfig.loopState && (
                    <p>
                      <strong>Loop State:</strong>{" "}
                      {currentBagConfig.loopState.toUpperCase()}
                    </p>
                  )}
                </div>
                <div className={styles.priceDisplay}>
                  <h4>Total: ${currentBagConfig.price.toFixed(2)}</h4>
                  <small style={{ color: "#666", fontSize: "0.8em" }}>
                    {currentBagConfig.strapType.length} strap(s) included
                  </small>
                </div>
                <button
                  className={styles.addToCartButton}
                  onClick={handleAddToCart}
                >
                  Add to Cart{" "}
                  {currentBagConfig.strapType.length > 0 &&
                    `(${currentBagConfig.strapType.length} straps)`}
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {showLoader && <Loaders redirectTo="/microbag" redirectDelay={4000} />}
      {showLoader1 && <Loaders redirectTo="/phonepouch" redirectDelay={4000} />}
      {showLoader2 && <Loaders redirectTo="/totes" redirectDelay={4000} />}
      {showLoader3 && <Loaders redirectTo="/panierbag" redirectDelay={4000} />}
      {showLoader4 && <Loaders redirectTo="/totem" redirectDelay={4000} />}
      {showLoader5 && <Loaders redirectTo="/totel" redirectDelay={4000} />}

      <div className={styles.tabManagerContainer}>
        <div className={styles.subCon}>
          <div className={styles.tabNavigation}>
            <div className={styles.tabContent}>{renderTabContent()}</div>
          </div>

          <div ref={scrollRef} className={styles.scrollContainer}>
            <div className={styles.tabsContainer}>
              <p className={styles.configText}>The Parcel - Frame</p>
              <div className={styles.tabMainCon}>
                <button
                  className={styles.navButton}
                  aria-label="Previous"
                  onClick={handlePrevTab}
                >
                  <LuMoveLeft color="#d4d4d4" />
                </button>
                {activeTab > 0 && activeTab <= tabs.length && (
                  <div
                    className={`${styles.tab} ${
                      activeTab === tabs[activeTab - 1].id ? styles.active : ""
                    }`}
                    onClick={() => {
                      if (tabs[activeTab - 1]?.id === 2 && isHovered) {
                        handleAllStrapsClick();
                      } else {
                        handleTabClick(tabs[activeTab - 1].id);
                      }
                    }}
                    onMouseEnter={() => {
                      if (tabs[activeTab - 1]?.id === 2) {
                        setIsHovered(true);
                      }
                    }}
                    onMouseLeave={() => setIsHovered(false)}
                  >
                    {/* <span className={styles.tabNumber}>
                      {tabs[activeTab - 1].id}.
                    </span> */}
                    <span className={styles.tabName}>
                                                                  {tabs[activeTab - 1].id}.
                      {getTabDisplayName()}
                    </span>
                    {tabs[activeTab - 1].icon}
                  </div>
                )}
                <button
                  className={styles.navButton}
                  aria-label="Next"
                  onClick={handleNextTab}
                >
                  <LuMoveRight color="#d4d4d4" />
                </button>
              </div>
              <button className={styles.cartBtn} onClick={toggleCart}>
                Cart (
                {cartItems.reduce((total, item) => total + item.quantity, 0)})
              </button>
            </div>
          </div>
        </div>
      </div>

      <button className={styles.addcartBtn} onClick={handleAddToCart}>
        Add To Cart
      </button>

      <CartComponent
        ref={cartRef}
        isCartOpen={isCartOpen}
        setIsCartOpen={setIsCartOpen}
        currentBagConfig={currentBagConfig}
        cartItems={cartItems}
        setCartItems={setCartItems}
        onStrapRemovedFromCart={handleStrapRemovedFromCart}
      />
    </>
  );
};

export default TabManagerTotem;