@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 13%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  // Prevent touch events on overlay from interfering
  touch-action: none;
}

.container {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 96%;
  height: 97vh; // Reduced height to ensure scrollable content
  max-width: 1700px;
  padding: 34px;
  position: relative;
  animation: slideUp 0.3s ease-out;

  // Critical mobile scrolling fixes
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: contain;

  // Allow touch events for scrolling
  touch-action: pan-y;

  // Ensure the container can be scrolled
  min-height: 0;

  // Fix for iOS Safari
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.imageContainer {
  margin-bottom: 50px;
  margin-right: 50px;
}

// Optional: Hide scrollbar but keep functionality
.container::-webkit-scrollbar {
  width: 6px;
}

.container::-webkit-scrollbar-track {
  background: transparent;
}

.container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.closeButton {
  // position: absolute;
  // top: 10px;
  // right: 10px;
  position: sticky; // added by punit
  top: 0px; // added by punit
  right: 0px; // added by punit
  margin-left: auto; // added by punit
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s ease;
  z-index: 10; // Ensure it stays on top
  display: block; // added by punit

  &:hover {
    color: #000;
  }
}

.masonryContainer {
  width: 70%;
  margin: 0 auto;
  // Add some padding at the bottom for better scrolling experience
  padding-bottom: 20px;
}

@media only screen and (max-width: 768px) {
  .overlay {
    // Ensure proper touch handling on mobile
    touch-action: none;
  }

  .container {
    width: 95%;
    height: 95vh; // Even smaller on mobile to ensure scrolling
    padding: 15px;

    // Enhanced mobile scrolling
    overflow-y: scroll; // Force scrollbar on mobile
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;

    // Additional iOS fixes
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  .imageContainer {
    margin-bottom: 20px;
    margin-right: 0px;
  }

  .masonryContainer {
    // width: 100%;
    width: 85%; // added by punit
    // Ensure content is taller than container to enable scrolling
    min-height: calc(100vh + 100px);

    margin-top: 10px; // added by punit
  }

  .closeButton {
    top: 5px;
    right: 5px;
    font-size: 20px;
    // Ensure close button doesn't interfere with scrolling
    z-index: 1001;
    touch-action: manipulation;
  }
}
