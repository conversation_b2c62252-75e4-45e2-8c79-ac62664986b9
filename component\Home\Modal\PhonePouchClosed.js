import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { Text } from "@react-three/drei";
import * as THREE from "three";
function PhonepouchClose({ position, scale, setShowRotationAxes, setOpen }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/Phone_Pouch_Closed.glb");
  const bagName = "Phone Pouch";
    // Log the model's hierarchy to the console
  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });
  return (
    <>
    <group
      ref={ref}
      position={position}
      scale={scale || DEFAULT_SCALE} // Use the default scale if not provided
      rotation={[0, 0, 0]}
      onPointerOver={() => setIsHovered(true)}
      onPointerOut={() => setIsHovered(false)}
      dispose={null}
    >
      <mesh
        geometry={nodes.Node1.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node2.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node3.geometry}
        material={new THREE.MeshStandardMaterial({})}
      />
      <mesh
        geometry={nodes.Node4.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />
      <mesh
        geometry={nodes.Node5.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />
      <mesh
        geometry={nodes.Node6.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />
      <mesh
        geometry={nodes.Node7.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node8.geometry}
        material={new THREE.MeshStandardMaterial({})}
      />
      <mesh
        geometry={nodes.Node9.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />
      <mesh
        geometry={nodes.Node10.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />
      <mesh
        geometry={nodes.Node11.geometry}
        material={new THREE.MeshStandardMaterial({})}
      />
      <mesh
        geometry={nodes.Node12.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node13.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node14.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node15.geometry}
        material={new THREE.MeshStandardMaterial({  })}
      />
      <mesh
        geometry={nodes.Node16.geometry}
        material={new THREE.MeshStandardMaterial({ })}
      />

    </group>
    {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={2.3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}
export default PhonepouchClose;
useGLTF.preload("/Phone_Pouch_Closed.glb");