import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { proxy, useSnapshot } from "valtio";
import { Text } from "@react-three/drei";

const state = proxy({
  current: null,
  items: {
    Bottom_Plate: "#fcc279",
    Outer_Layer: "#fcc279",
    Inner_Layer: "#fcc279",
    Plane009: "#fcc279",
    Plane011: "#fcc279",
    Box005: "#fcc279",
    Shape009: "#fcc279",
    Cylinder002: "#fcc279",
    Object003: "#000000",
    Inner_Layer001: "#fcc279",
    Middle_Layer: "#fcc279",
    Object004: "#fcc279",
    Shape010: "#fcc279",
    Shape011: "#000000",
    Shape012: "#fcc279",
  },
});

function Totes({ position, scale }) {
  const ref = useRef();
  const snap = useSnapshot(state);
  const { nodes, materials } = useGLTF("/openwithoutchaincomp.glb");
  const [isHovered, setIsHovered] = useState(false);
  const bagName = "Tote S"; // Define the name of the bag

  const uniqueMaterials = Object.keys(state.items).reduce((acc, key) => {
    acc[key] = materials.Material.clone();
    acc[key].name = key;
    acc[key].roughness = 0.5;
    acc[key].metalness = 0.1;
    return acc;
  }, {});

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      ref.current.rotation.y -= 0.005;
      const t = state.clock.getElapsedTime();
      ref.current.rotation.y = Math.sin(t / 4) / 4;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
      easing.dampE(ref.current.rotation, [0, 0, 0], 0.2, delta);
    }
  });

  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        onPointerMissed={() => (state.current = null)}
        // onClick={(e) =>
        //   // e.stopPropagation(), (state.current = e.object.material.name)
        //   (window.location.href = "/totes")
        // }
      >
        {Object.keys(state.items).map((key) => (
          <mesh
            key={key}
            receiveShadow
            castShadow
            geometry={nodes[key].geometry}
            material={uniqueMaterials[key]}
            material-color={snap.items[key]}
          />
        ))}
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}

export default Totes;

useGLTF.preload("/openwithoutchaincomp.glb");
