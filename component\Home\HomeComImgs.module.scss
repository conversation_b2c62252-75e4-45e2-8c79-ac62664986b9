.container {
  position: relative;
  height: 90vh;
  display: flex;
  background: transparent;
  width: 65vw;
  align-items: baseline;
  justify-content: center;
  margin: 0 auto;
  gap: 100px;
  // overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.container::-webkit-scrollbar {
  display: none !important;
}

.canvasContainer {
  position: relative;
  // width: 16.66%;
  height: 93%;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
}

.productImage {
  // max-width: 59%;
  width: 100%;

  // max-height: 59%;
  // object-fit: contain;
  // transform: translateY(0);
  transition: all 0.3s ease-in-out;
}
.productImage1 {
  user-select: none;
}

// .productImage:hover {
//   transform: scale(1.05);
// }
.mainCon:hover {
  .productImage {
    transform: translateY(-12%);
  }

  p {
    visibility: visible !important;
  }
}

.mobileSlider {
  position: relative;
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  justify-content: center;
  gap: 50px;
}

.mobileCanvasContainer {
  position: relative;
  width: 100%;
  height: 50%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  // pointer-events: none; /* Prevents all pointer interactions */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.mobileCanvasContainer img {
  max-width: 80%;
  // max-height: 100%;
  object-fit: contain;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: none; /* Prevents all pointer interactions */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  transform: scale(1.5);
}

.mobileControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 20px;
  z-index: 10;
}

.controlButton {
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 34px;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.modelName {
  font-size: 14px;
  font-weight: 500;
  color: #b9b9b9;
  padding: 5px 15px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
}

.line {
  width: 130%;
  height: 1px;
  background-color: #f3f1f1;
  margin: 10px 0;
  position: absolute;
  top: 53.5%;
}
.mainCon {
  text-align: center;
  cursor: pointer;

  p {
    color: #a3a0a0;
    flex-shrink: 0;
    font-size: 12px;
    visibility: hidden;
    margin-top: 20px;
  }
}

@media (max-width: 1599px) {
  .line {
    top: 53%;
  }
  .container {
    // width: 70vw;
    gap: 60px;
  }
  .mainCon {
    p {
      font-size: 14px;
    }
  }
}

@media (max-width: 1350px) {
  .line {
    top: 53%;
  }
}

@media (max-width: 1150px) {
  .line {
    top: 50%;
    width: 110%;
  }
  .container {
    width: 76vw;
    gap: 32px;
  }
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
    height: auto;
  }

  .canvasContainer {
    width: 100%;
    height: 300px;
    margin-bottom: 20px;
  }

  .line {
    display: none;
  }
}
