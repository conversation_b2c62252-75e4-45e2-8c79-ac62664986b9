function FBXModelWithTexturesopen(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 0, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    sethandlestraptab,
    setHoveredSphere,
    xrayMode,
    envMapIntensity,
    ref
  } = props;

  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const snap = useSnapshot(state);

  // Memoize textures to avoid unnecessary reloads
  const textures = useTexture({
    // Leather 1 textures
    leather1Albedo: "/textures4k/2/Leather_albedo.jpg",
    leather1Normal: "/textures4k/2/Leather_normal.png",
    leather1Roughness: "/textures4k/2/Leather_roughness.jpg",
    leather1Metallic: "/textures4k/2/Leather_metallic.jpg",
    leather1AO: "/textures4k/2/Leather_AO.jpg",

    // Leather 2 textures
    // leather2Albedo: "/textures4k/2_LEATHER_albedo.jpg",
    // leather2Normal: "/textures4k/2_LEATHER_normal.png",
    // leather2Roughness: "/textures4k/2_LEATHER_roughness.jpg",
    // leather2Metallic: "/textures4k/2_LEATHER_metallic.jpg",
    // leather2AO: "/textures4k/2_LEATHER_AO.jpg",

    // Metal textures
    metallAlbedo: "/textures4k/2/METALL_albedo.jpg",
    metallNormal: "/textures4k/2/METALL_normal.png",
    metallRoughness: "/textures4k/2/METALL_roughness.jpg",
    metallMetallic: "/textures4k/2/METALL_metallic.jpg",
    metallAO: "/textures4k/2/METALL_AO.jpg",

    // Plastic textures
    plasticAlbedo: "/textures4k/2/Plastic_albedo.jpg",
    plasticNormal: "/textures4k/2/Plastic_normal.png",
    plasticRoughness: "/textures4k/2/Plastic_roughness.jpg",
    plasticMetallic: "/textures4k/2/Plastic_metallic.jpg",
    plasticAO: "/textures4k/2/Plastic_AO.jpg",

    // Seams textures
    seamsAlbedo: "/textures4k/2/Seams_albedo.jpg",
    seamsNormal: "/textures4k/2/Seams_normal.png",
    seamsRoughness: "/textures4k/2/Seams_roughness.jpg",
    seamsMetallic: "/textures4k/2/Seams_metallic.jpg",
    seamsAO: "/textures4k/2/Seams_AO.jpg",
  });

  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX("/open.fbx");
  const model = useMemo(() => fbx.clone(), [fbx]);
  const tempcolor = state.colors;

  // Cache material mappings
  const materialMappings = useMemo(
    () => ({
      Leather: {
        map: textures.leather1Albedo,
        normalMap: textures.leather1Normal,
        roughnessMap: textures.leather1Roughness,
        metalnessMap: textures.leather1Metallic,
        aoMap: textures.leather1AO,
        aoMapIntensity: 1.0,
        roughness: 0.9,
        metalness: 0,
        color: new THREE.Color(tempcolor.leather),
      },
      METALL: {
        map: textures.metallAlbedo,
        normalMap: textures.metallNormal,
        roughnessMap: textures.metallRoughness,
        metalnessMap: textures.metallMetallic,
        aoMap: textures.metallAO,
        aoMapIntensity: 1.0,
        metalness: 1,
      },
      Plastic: {
        map: textures.plasticAlbedo,
        normalMap: textures.plasticNormal,
        roughnessMap: textures.plasticRoughness,
        aoMap: textures.plasticAO,
        aoMapIntensity: 1.0,
        metalness: 0.4,
        color: new THREE.Color(tempcolor.plastic),
      },
      Seams: {
        map: textures.seamsAlbedo,
        normalMap: textures.seamsNormal,
        roughnessMap: textures.seamsRoughness,
        metalnessMap: textures.seamsMetallic,
        aoMap: textures.seamsAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.seams),
      },
    }),
    [textures]
  );

  // Toggle function for opening/closing the bag
  // const toggleModel = useCallback(() => {
  //   // Implement the toggle functionality here
  //   // For example:
  //   state.isOpen = !state.isOpen;
  //   if (setOpen) {
  //     setOpen(state.isOpen ? "true" : "false");
  //   }
  // }, [setOpen]);

  // Optimize hover part handling
  const handlePartHover = useCallback(
    (partName) => {
      setHoveredPart(partName);

      // Set hover information based on part name
      if (partName.includes("Magnet_Closure")) {
        setHoveredSphere?.("click on it to open and close the bag");
      } else if (partName.includes("Bottom_Plate")) {
        setHoveredSphere?.("Bottom Plate");
      } else if (partName.includes("Cylinder")) {
        setHoveredSphere?.("side fastner");
        sethandlestraptab?.(0);
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("2_LEATHER") ||
        partName.includes("Inner")
      ) {
        setHoveredSphere?.("Leather material");
      } else if (partName.includes("Object003")) {
        setHoveredSphere?.("flaps");
      }
    },
    [setHoveredSphere, sethandlestraptab]
  );

  // Handle part click with useCallback - FIXED
  const handlePartClick = useCallback((partName) => {
    console.log("partname in openfbx on click", partName);
    
    if (partName.includes("Magnet_Closure")) {
      // Toggle the bag open/close state
      toggleModel();
    } else if (partName.includes("Cylinder")) {
      // Handle cylinder clicks (side fastener)
      if (sethandlestraptab) {
        sethandlestraptab(0);
      }
    }
  }, [toggleModel, sethandlestraptab]);

  // Optimize material management - create a cache for materials
  const materialCache = useRef(new Map());

  // Get material based on hover state and xray mode
  const getPartMaterial = useCallback(
    (partName, originalMaterial) => {
      if (!originalMaterial) return new THREE.MeshStandardMaterial();

      // Create a unique key for caching
      const isHovered = hoveredPart === partName;
      const cacheKey = `${partName}-${isHovered}-${xrayMode}`;

      // Check if this material configuration is already cached
      if (materialCache.current.has(cacheKey)) {
        return materialCache.current.get(cacheKey);
      }

      // Create a new material if not in cache
      const newMaterial = originalMaterial.clone();

      // Get base color from state or use default
      let baseColor = "#ffffff";
      if (snap?.colors && snap.colors[partName]) {
        baseColor = snap.colors[partName];
      }

      // Apply hover effect or xray mode
      if (isHovered) {
        // For hover effect
        newMaterial.color = new THREE.Color(baseColor);
        newMaterial.emissive = new THREE.Color(baseColor).multiplyScalar(1.5);
        newMaterial.emissiveIntensity = 0.5;
        newMaterial.transparent = true;
        newMaterial.opacity = 0.02;
      } else if (xrayMode) {
        // For xray mode
        newMaterial.color = new THREE.Color(baseColor);
        newMaterial.transparent = true;
        newMaterial.opacity = 0.2;
        newMaterial.emissive = new THREE.Color("#000000");
        newMaterial.emissiveIntensity = 0;
      } else {
        // Normal state
        newMaterial.color = new THREE.Color(baseColor);
        newMaterial.transparent = false;
        newMaterial.opacity = 1;
        newMaterial.emissive = new THREE.Color("#000000");
        newMaterial.emissiveIntensity = 0;
      }

      // Apply environment map intensity
      newMaterial.envMapIntensity = envMapIntensity;

      newMaterial.needsUpdate = true;

      // Cache the material for future use
      materialCache.current.set(cacheKey, newMaterial);

      return newMaterial;
    },
    [hoveredPart, xrayMode, snap?.colors, envMapIntensity]
  );

  // Auto rotation effect with reduced re-renders

  // Optimized inactivity detection using refs to reduce re-renders
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);

  useEffect(() => {
    const handleInactivity = () => {
      if (!isMouseDownRef.current) {
        setShowSpheres(true);
      }
    };

    const resetInactivityTimeout = () => {
      clearTimeout(inactivityTimeoutRef.current);
      inactivityTimeoutRef.current = setTimeout(handleInactivity, 4000);
    };

    const handleUserActivity = () => {
      resetInactivityTimeout();
      setShowSpheres(false);
    };

    const handleMouseDown = () => {
      isMouseDownRef.current = true;
      resetInactivityTimeout();
      setShowSpheres(false);
            //  // if (ref.current) ref.current.rotation.y = 0;
    };

    const handleMouseUp = () => {
      isMouseDownRef.current = false;
      resetInactivityTimeout();
    };

    const handleMouseMove = () => {
      // Count all mouse movement as user activity
      handleUserActivity();

      if (isMouseDownRef.current) {
        resetInactivityTimeout();
      }
    };

    window.addEventListener("keydown", handleUserActivity);
    window.addEventListener("mousedown", handleMouseDown);
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);

    resetInactivityTimeout();

    return () => {
      clearTimeout(inactivityTimeoutRef.current);
      window.removeEventListener("keydown", handleUserActivity);
      window.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  // Auto rotation with optimized animation frame
  useFrame(() => {
    if (showSpheres && ref.current) {
      // ref.current.rotation.y -= 0.0025;
    }
  });

  // Optimization: Only update materials when necessary
  const updateMeshMaterial = useCallback(
    (mesh) => {
      if (!mesh.isMesh || !mesh.userData.originalMaterial) return;

      const newMaterial = getPartMaterial(
        mesh.userData.partName,
        mesh.userData.originalMaterial
      );

      // Only replace material if it's actually different
      if (mesh.material !== newMaterial) {
        mesh.material = newMaterial;
        mesh.material.needsUpdate = true;
      }
    },
    [getPartMaterial]
  );

  // Update materials efficiently - only when hover state or xray mode changes
  useEffect(() => {
    if (!model) return;

    // Use a more efficient traversal approach - avoid unnecessary updates
    model.traverse(updateMeshMaterial);
  }, [model, hoveredPart, xrayMode, updateMeshMaterial, envMapIntensity]);

  // Set up initial materials only once
  useEffect(() => {
    if (!model || !textures) return;

    // Set color spaces for all textures at once
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model - only called once
    model.traverse((child) => {
      if (child.isMesh) {
        // Store material name for reference
        const materialName = child.material.name;

        // Create optimized material
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
        });

        // Find and apply the correct textures
        Object.keys(materialMappings).forEach((matKey) => {
          if (materialName.includes(matKey)) {
            Object.assign(newMaterial, materialMappings[matKey]);
          }
        });

        // Setup uv2 coordinates efficiently
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Store original material
        child.material = newMaterial;
        child.userData.originalMaterial = newMaterial.clone();
        child.userData.partName = child.name;
        child.castShadow = true;
        child.material.needsUpdate = true;
      }
    });

    // Clear material cache when model changes
    return () => {
      materialCache.current.clear();
    };
  }, [model, textures, materialMappings, envMapIntensity]);

  // Event handlers with debounce behavior for better performance
  const debouncedSetHovered = useRef(null);

  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      if (e.object.userData.partName) {
        // Clear any pending timeout
        if (debouncedSetHovered.current) {
          clearTimeout(debouncedSetHovered.current);
        }

        // Set immediately for responsive UI
        handlePartHover(e.object.userData.partName);

        // Also count hovering over the bag as activity
        if (inactivityTimeoutRef.current) {
          clearTimeout(inactivityTimeoutRef.current);
          inactivityTimeoutRef.current = setTimeout(() => {
            if (!isMouseDownRef.current) {
              setShowSpheres(true);
            }
          }, 4000);
        }
        setShowSpheres(false);
      }
    },
    [handlePartHover]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();

      // Debounce the pointer out to avoid flicker
      if (debouncedSetHovered.current) {
        clearTimeout(debouncedSetHovered.current);
      }

      debouncedSetHovered.current = setTimeout(() => {
        setHoveredPart(null);
        setHoveredSphere?.(null);
      }, 50); // Short delay to prevent flickering
    },
    [setHoveredSphere]
  );

  // FIXED: properly implement click handler with useCallback and correct dependencies
  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      if (e.object.userData.partName) {
        handlePartClick(e.object.userData.partName);
      }
    },
    [handlePartClick] // Now properly including handlePartClick in dependencies
  );

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  const attachmentPoints = [
    { name: "Cylinder002", position: [1, 0, 0], rotation: [0, 0, 0] },
  ];


  return (
    <group
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      <primitive
        object={model}
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        onClick={handleClick}
        castShadow
      />
    </group>
  );
}