.container {
  display: flex;
  width: 100%;
  height: calc(100vh - 0px);
  background: linear-gradient(to top, #ebebeb, #ffffff); /* Purple to blue */
  align-items: center;
  justify-content: center;
}
.canvasContainer {
  flex: 1;
  height: 100%;
}
.controlsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin-left: 20px;
}
.select {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: white;
  font-size: 16px;
  cursor: pointer;
  width: 200px;
}
.slider {
  gap: 15px;
  align-items: center;
  position: fixed;
  bottom: 20px;
  left: 4%;
  top: 37%;
  flex-direction: column;
  display: flex;
  transform: translateY(-50%);
  width: 60px;
  height: 300px;
}
/* Updated styles for the details panel */
.detailsPanel {
  position: fixed;
  top: 48%;
  right: 50px;
  width: 60vh;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1;
  transform: translateY(-50%);
  border-radius: 10px; /* More rounded corners */
  padding: 24px; /* Even space around content */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.detailsPanel h3 {
  color: #807f7f;
  // font-family: Helvetica;
  font-size: 12px ;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin: 0 0 16px 0; /* Proper spacing */
  word-wrap: break-word; /* Ensure text stays inside */
    font-weight: 600;
}
.detailsPanel p {
  color: #807f7f !important;
  // font-family: Helvetica;
  font-size: 12px !important;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin: 0 0 16px 0; /* Proper spacing */
  word-wrap: break-word; /* Ensure text stays inside */
    font-weight: 600;
}

// .detailsPanell p {
//   position: fixed; /* Make p fixed so it can move to the top */
//   top: 25%;
//   left: 50%;
//   transform: translateX(-50%);
//   color: #807f7f !important;
//   font-family: Helvetica;
//   font-size: 13px !important;
//   font-style: normal;
//   font-weight: 400;
//   line-height: 22px;
//   margin: 0;
//   z-index: 9999; /* Make sure it's on top of other components */
//   padding: 5px 10px; /* Optional: spacing */
//   // border: 1px solid #ccc; /* Optional: border for visibility */
//   // border-radius: 6px;
//   background: #f9f9f9cc; /* Slightly transparent background */
//   border-radius: 6%;
//   color: #6e6e6e;
//   font-weight: 600;
//   font-size: 11px; /* smaller font */
// }

.xreyBtn {
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: rgb(255, 255, 255);
  color: #6e6e6e;
  font-size: 10px;
  cursor: pointer;
  width: 105px;
  font-weight: 600;

  &:hover {
    background-color: #ccc;
  }
}

.verticalZoomSlider {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem;
}

.zoomPercentage {
  font-size: 12px;
  color: #b7b7b7;
  margin-bottom: 0.5rem;
  width: 42px;
  text-align: center;
}

.sliderContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.plusIcon,
.minusIcon {
  color: #d4d4d4;
}

.plusIcon {
  margin-bottom: 0.5rem;
}

.minusIcon {
  margin-top: 0.5rem;
}

.sliderTrackContainer {
  position: relative;
  height: 18rem;
  display: flex;
  align-items: center;
}

.sliderTrack {
  position: absolute;
  width: 1px;
  height: 100%;
  background-color: #d4d4d4;
  pointer-events: none;
}

.sliderInput {
  appearance: none;
  background: transparent;
  width: 2px;
  height: 100%;
  outline: none;
  opacity: 0;
  z-index: 10;

  &::-webkit-slider-thumb {
    appearance: none;
    width: 0;
    height: 0;
  }

  &::-moz-range-thumb {
    appearance: none;
    width: 0;
    height: 0;
  }

  &::-ms-thumb {
    appearance: none;
    width: 0;
    height: 0;
  }

  &::-webkit-slider-runnable-track {
    width: 0;
    height: 0;
  }

  &::-moz-range-track {
    width: 0;
    height: 0;
  }

  &::-ms-track {
    width: 0;
    height: 0;
  }
}

.sliderThumb {
  position: absolute;
  width: 1rem;
  height: 0.15rem;
  background-color: #d4d4d4;
  pointer-events: none;
  left: -0.4rem;
}

/* For vertical slider in supported browsers */
@supports (-webkit-appearance: slider-vertical) or
  (-moz-appearance: slider-vertical) {
  .sliderInput {
    -webkit-appearance: slider-vertical !important;
    -moz-appearance: slider-vertical !important;
    writing-mode: bt-lr !important;
  }
}

@media only screen and (max-width: 768px) {
  .container {
    height: calc(100svh - 0px);
    background-color: #ebebeb;
  }
  .detailsPanel {
    width: 81vw;
    left: 41px;
  }

  .controlBtnDiv {
    position: fixed;
    bottom: 120px;
    // left: 30px;
    right: 10px;
  }

  .controlBtn {
    // padding: 6px 16px;
    // border-radius: 6px;
    // border: 1px solid #ccc;
    background-color: rgb(236, 236, 236);
    font-size: 7px;
    cursor: pointer;

    &:hover {
      background-color: #ccc;
    }
  }
}
.detailsPanel {
  /* Keep your existing styles */
  transition: all 0.3s ease;
  z-index: 100; /* Make sure panel is above the blurred background */
}
@media only screen and (max-width: 768px) {
  /* Overlay that covers the entire screen with blur */
  body.blur-background::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 50; /* Below the panel but above everything else */
    pointer-events: none; /* Let clicks pass through */
  }

  .detailsPanel {
    width: 81vw;
    left: 41px;
    background-color: rgba(
      255,
      255,
      255,
      0.8
    ); /* Slightly visible background */
    h3 {
      color: #807f7f;
      font-family: Helvetica;
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }

    p {
      color: #807f7f;
      font-family: Helvetica;
      font-size: 7px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      margin-top: 20px;
    }
  }

  .detailsPanel.active {
    /* You can add specific styles for the active panel if needed */
    background-color: rgba(255, 255, 255, 1);
  }
}
.videoContainer {
  margin-top: 16px; /* Consistent spacing */
  width: 100%;
  border-radius: 12px; /* Rounded video container */
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
@media only screen and (max-width: 768px) {
  .videoContainer {
    margin-left: auto;
    margin-right: auto;
  }
}

.resetStrapBtn,
.resetPositionBtn {
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: rgb(255, 255, 255);
  font-size: 10px;
  cursor: pointer;
  color: #6e6e6e;
  padding: 4px 8px;
  width: 105px;
  font-weight: 600;
  opacity: 0.8;

  &:hover {
    background-color: #ccc;
  }
}
.strapVideo {
  display: block;
  width: 100%;
  height: auto;
}

.environmentControls {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.environmentSelect {
  padding: 4px 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background: white;
}

.envIntensityControl {
  display: flex;
  flex-direction: column;
}

.envIntensitySlider {
  width: 100%;
  margin: 8px 0;
}

.imageAdjustments {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
  position: fixed;
  bottom: 67px;
  left: 17px;
}

.imageAdjustments h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
}

.adjustmentControl {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.adjustmentControl label {
  width: 80px;
  font-size: 12px;
}

.adjustmentSlider {
  flex-grow: 1;
  margin: 0 10px;
}

.adjustmentControl span {
  width: 30px;
  text-align: right;
  font-size: 12px;
}
.imageAdjustments {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.imageAdjustments h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.adjustmentControl {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.adjustmentControl label {
  width: 80px;
  flex-shrink: 0;
}

.adjustmentSlider {
  flex-grow: 1;
  margin: 0 8px;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: #ddd;
  outline: none;
  border-radius: 2px;
}

.adjustmentSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
  cursor: pointer;
}

.adjustmentSlider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #666;
  cursor: pointer;
  border: none;
}

.adjustmentControl span {
  width: 30px;
  text-align: right;
  font-variant-numeric: tabular-nums;
}
.controlBtnDivLeft {
  position: fixed;
  bottom: 62px;
  left: 50px; /* Default for desktop */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  z-index: 101; //added by punit
}
.controlBtnDiv {
  position: fixed;
  bottom: 62px;
  right: 50px; /* Default for desktop */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  z-index: 101; //added by punit
}

.controlBtn {
  padding: 4px 10px; /* reduced padding */
  border-radius: 4px; /* slightly tighter corners */
  border: 1px solid #ccc;
  background-color: rgb(255, 255, 255);
  color: #6e6e6e;
  font-weight: 600;
  font-size: 10px; /* smaller font */
  cursor: pointer;
  width: 105px;
  opacity: 0.8;
}

.controlBtn:hover {
  background-color: #ccc;
}
.controlBtn1 {
  padding: 4px 10px; /* reduced padding */
  border-radius: 4px; /* slightly tighter corners */
  border: 1px solid #ccc;
  background-color: rgb(255, 255, 255);
  color: #6e6e6e;
  font-weight: 600;
  font-size: 11px; /* smaller font */
  cursor: pointer;
  position: fixed;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
}

.showBtnDiv {
  display: none;
  position: fixed;
  bottom: 122px;
  right: 15px;
}

/* Mobile-specific positioning */

@media only screen and (max-width: 1024px) {
  .controlBtnDiv {
    display: none;
  }
  .controlBtnDivLeft {
    position: fixed;
    bottom: 122px;
    left: 15px;
  }
  .showBtnDiv {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    z-index: 101;

    button {
      border-radius: 6px;
      padding: 3px 6px;
      border: 1px solid #ccc;
      font-size: 10px;
      cursor: pointer;
      // color: black;

      &:hover {
        background-color: #ccc;
      }
    }
  }
  .xreyBtn {
    font-size: 10px;
    // padding: 5px 18px;
    opacity: 0.8;
  }
  .controlBtnDiv {
    position: fixed;
    bottom: 17%;
    left: 30px; /* Default for desktop */
  }
}
@media only screen and (max-width: 768px) {
  .controlBtnDiv {
    left: 15px; /* 10px from right for mobile screens */
    bottom: 115px;
  }
  .slider {
    top: 29%;
  }
  .detailsPanelCon {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    background-color: #6666668e;
    z-index: 102;
  }
}

@media only screen and (max-width: 576px) {
  .sliderTrackContainer {
    height: 13rem;
  }
  .slider {
    top: 35%;
  }
}
.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s ease;
  display: none;
  z-index: 10;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.closeButton:hover {
  color: #000;
  background-color: rgba(0, 0, 0, 0.1);
}

@media only screen and (max-width: 768px) {
  .closeButton {
    display: block;
  }
}
.loadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
@media only screen and (max-width: 768px) {
  .detailsPanelCon {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 102;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  }

  .detailsPanel {
    position: relative;
    top: auto;
    right: auto;
    left: auto;
    transform: none;
    width: 100%;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 10px; /* More rounded on mobile */
    padding: 24px;
    margin: 0;
  }

  .detailsPanel h3 {
    font-size: 14px;
    margin-bottom: 12px;
    padding-right: 40px; /* Space for close button */
  }

  .detailsPanel p {
    font-size: 12px;
    line-height: 20px;
  }

  .closeButton {
    display: flex;
    top: 12px;
    right: 12px;
  }

  .videoContainer {
    margin-top: 16px;
    border-radius: 12px;
  }
}
/* Add this CSS class to your stylesheet */
.detailsPanell {
  position: fixed;
  top: 25%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  padding: 10px 15px;
  background: rgba(249, 249, 249, 0.95);
  border-radius: 8px;
  max-width: 300px;
  text-align: center;
}

.detailsPanell p {
  color: #6e6e6e !important;
  font-family: Helvetica;
  font-size: 11px !important;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
  margin: 0;
}

/* Mobile responsive */
@media only screen and (max-width: 768px) {
  .detailsPanell {
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    max-width: 280px;
    padding: 8px 12px;
  }
  
  .detailsPanell p {
    font-size: 10px !important;
    line-height: 16px;
  }
}
/* SOLUTION 1: CSS-only custom tooltip */
.tooltipContainer {
  position: relative;
  display: inline-block;
}

.customTooltip {
  position: relative;
  margin-right: 4px;
margin-bottom: 4px;
}

.customTooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%; /* Position above the button */
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
  pointer-events: none;
}

.customTooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
}

.customTooltip:hover::before,
.customTooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Variations for different positions */

/* Tooltip on the right */
.customTooltip.tooltip-right::before {
  bottom: auto;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 10px;
}

.customTooltip.tooltip-right::after {
  bottom: auto;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-color: transparent;
  border-right-color: #333;
  margin-left: 5px;
}

/* Tooltip on the left */
.customTooltip.tooltip-left::before {
  bottom: auto;
  right: 100%;
  top: 50%;
  left: auto;
  transform: translateY(-50%);
  margin-right: 10px;
}

.customTooltip.tooltip-left::after {
  bottom: auto;
  right: 100%;
  top: 50%;
  left: auto;
  transform: translateY(-50%);
  border-color: transparent;
  border-left-color: #333;
  margin-right: 5px;
}

/* Tooltip below */
.customTooltip.tooltip-bottom::before {
  top: 100%;
  bottom: auto;
  margin-top: 10px;
}

.customTooltip.tooltip-bottom::after {
  top: 100%;
  bottom: auto;
  border-color: transparent;
  border-bottom-color: #333;
  margin-top: 5px;
}

/* SOLUTION 2: State-controlled tooltip */
.tooltipWrapper {
  position: relative;
  display: inline-block;
}

.customTooltipBox {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.customTooltipBox::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
}

/* SOLUTION 3: Follow cursor tooltip */
.followCursorTooltip {
  background-color: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  transition: opacity 0.2s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .customTooltip::before,
  .customTooltipBox,
  .followCursorTooltip {
    font-size: 11px;
    padding: 6px 10px;
  }
  
  /* On mobile, position tooltip above to avoid covering content */
  .customTooltip::before,
  .customTooltipBox {
    bottom: 100%;
    top: auto;
  }
}

/* Dark theme variations */
.dark-theme .customTooltip::before,
.dark-theme .customTooltipBox,
.dark-theme .followCursorTooltip {
  background-color: #fff;
  color: #333;
  border: 1px solid #ccc;
}

.dark-theme .customTooltip::after,
.dark-theme .customTooltipBox::after {
  border-top-color: #fff;
}