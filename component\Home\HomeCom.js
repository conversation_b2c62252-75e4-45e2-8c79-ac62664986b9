import React, { useRef, useState, useEffect, Suspense, useMemo } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
  useGLTF,
  ContactShadows,
  Environment,
  OrbitControls,
  PerspectiveCamera,
  AccumulativeShadows,
  RandomizedLight,
  useTexture,
} from "@react-three/drei";
import { HexColorPicker } from "react-colorful";
import { proxy, useSnapshot } from "valtio";
import { easing } from "maath";
import * as THREE from "three";
import { TextureLoader } from "three";
import { useLoader } from "@react-three/fiber";
import { Text } from "@react-three/drei";
import style from "./Home.module.scss";
import Microopen from "./Modal/Micropen";
import Phonepouch from "./Modal/Phonepouch";
import Totes from "./Modal/Totes";
import PanierBag from "./Modal/PanierBag";
import ToteM from "./Modal/ToteM";
import ToteL from "./Modal/ToteL";
import { Html, useProgress } from "@react-three/drei";
import Link from "next/link";
import Loaders from "../Loader/Loaders";
import ToteMcloser from "./Modal/ToteMClosed";
import ToteScloser from "./Modal/ToteSClose";
import ToteLcloser from "./Modal/ToteLClosed";
import Microcloser from "./Modal/Microclose";
import PhonepouchClose from "./Modal/PhonePouchClosed";
import Paniercloser from "./Modal/PainerBagClosed";
import useClickOrDrag from "./Hooks/useClickOrDrag";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const state = proxy({
  current: null,
  items: {
    Bottom_Plate: "#fcc279",
    Outer_Layer: "#fcc279",
    Inner_Layer: "#fcc279",
    Plane009: "#fcc279",
    Plane011: "#fcc279",
    Box005: "#fcc279",
    Shape009: "#fcc279",
    Cylinder002: "#fcc279",
    Object003: "#000000",
    Inner_Layer001: "#fcc279",
    Middle_Layer: "#fcc279",
    Object004: "#fcc279",
    Shape010: "#fcc279",
    Shape011: "#000000",
    Shape012: "#fcc279",
  },
});

function SceneSetup({ children }) {
  const controlsRef = useRef();
  const [isMoving, setIsMoving] = useState(false);
  const [shouldReset, setShouldReset] = useState(false);
  const initialPosition = new THREE.Vector3(0, 0, 2);
  const initialTarget = new THREE.Vector3(0, 0, 0);

  useFrame((state, delta) => {
    if (shouldReset && controlsRef.current && !isMoving) {
      const currentPos = controlsRef.current.object.position;
      const currentTarget = controlsRef.current.target;

      currentPos.lerp(initialPosition, 0.05);
      currentTarget.lerp(initialTarget, 0.05);

      controlsRef.current.update();

      if (
        currentPos.distanceTo(initialPosition) < 0.01 &&
        currentTarget.distanceTo(initialTarget) < 0.01
      ) {
        setShouldReset(false);
      }
    }
  });

  const handleControlStart = () => {
    setIsMoving(true);
    setShouldReset(false);
  };

  const handleControlEnd = () => {
    setIsMoving(false);
    setShouldReset(true);
  };

  return (
    <>
      {/* White table plane */}
      {/* <mesh
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, -0.15, 0]}
        receiveShadow
      >
        <planeGeometry args={[10, 10]} />
        <meshStandardMaterial color="white" />
      </mesh> */}

      {/* Adjusted lighting for better shadow definition */}
      <ambientLight intensity={0.5} />
      <directionalLight
        position={[2, 4, 2]}
        intensity={1}
        castShadow
        shadow-mapSize={[1024, 1024]}
        shadow-camera-far={10}
        shadow-camera-near={1}
        shadow-camera-top={3}
        shadow-camera-bottom={-3}
        shadow-camera-left={-3}
        shadow-camera-right={3}
      />

      {/* Modified AccumulativeShadows */}
      {/* <AccumulativeShadows
        temporal
        frames={30}
        alphaTest={0.9}
        scale={4}
        position={[0, -0.149, 0]}
        color="#000000"
        opacity={0.6}
        blend={100}>
        <RandomizedLight amount={4} radius={2} intensity={0.4} ambient={0.3} position={[2, 5, -2]} />
      </AccumulativeShadows> */}

      {/* Adjusted ContactShadows */}
      <ContactShadows
        position={[0, -0.145, 0]}
        opacity={0.4}
        scale={4}
        blur={1.5}
        far={0.4}
        resolution={256}
        color="#000000"
      />

      <Environment preset="city" />

      <OrbitControls
        ref={controlsRef}
        enableZoom={true}
        enablePan={true}
        enableRotate={true}
        makeDefault
        minDistance={0.5}
        maxDistance={10}
        onStart={handleControlStart}
        onEnd={handleControlEnd}
      />
      <PerspectiveCamera makeDefault position={[0, 0, 2]} fov={45} />
      {children}
    </>
  );
}

function Loader() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "rgba(255,255,255,0.8)",
          padding: "20px",
          borderRadius: "10px",
        }}
      >
        <div
          style={{
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #3498db",
            borderRadius: "50%",
            width: "40px",
            height: "40px",
            animation: "spin 1s linear infinite",
            marginBottom: "10px",
          }}
        />
        <p>Loading... {Math.round(progress)}%</p>
      </div>
      <style jsx global>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </Html>
  );
}

const HomeCom = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [showLoader1, setShowLoader1] = useState(false);
  const [showLoader2, setShowLoader2] = useState(false);
  const [showLoader3, setShowLoader3] = useState(false);
  const [showLoader4, setShowLoader4] = useState(false);
  const [showLoader5, setShowLoader5] = useState(false);

  const { handleMouseDown, handleMouseMove, handleMouseUp } = useClickOrDrag(
    () => setShowLoader(true)
  );
  const {
    handleMouseDown: handleMouseDown1,
    handleMouseMove: handleMouseMove1,
    handleMouseUp: handleMouseUp1,
  } = useClickOrDrag(() => setShowLoader1(true));
  const {
    handleMouseDown: handleMouseDown2,
    handleMouseMove: handleMouseMove2,
    handleMouseUp: handleMouseUp2,
  } = useClickOrDrag(() => setShowLoader2(true));
  const {
    handleMouseDown: handleMouseDown3,
    handleMouseMove: handleMouseMove3,
    handleMouseUp: handleMouseUp3,
  } = useClickOrDrag(() => setShowLoader3(true));
  const {
    handleMouseDown: handleMouseDown4,
    handleMouseMove: handleMouseMove4,
    handleMouseUp: handleMouseUp4,
  } = useClickOrDrag(() => setShowLoader4(true));
  const {
    handleMouseDown: handleMouseDown5,
    handleMouseMove: handleMouseMove5,
    handleMouseUp: handleMouseUp5,
  } = useClickOrDrag(() => setShowLoader5(true));

  const [isMobile, setIsMobile] = useState(false);
  const [currentModelIndex, setCurrentModelIndex] = useState(0);

  // Models array to track all your 3D models
  const models = [
    {
      name: "Micro Bag",
      component: Microcloser,
      position: [0, 0, 0],
      scale: 0.025,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
    },
    {
      name: "Phone Pouch",
      component: PhonepouchClose,
      position: [0, -0.1, 0],
      scale: 0.025,
      handleMouseDown: handleMouseDown1,
      handleMouseMove: handleMouseMove1,
      handleMouseUp: handleMouseUp1,
    },
    {
      name: "Tote S",
      component: ToteScloser,
      position: [0, 0.02, 0],
      scale: 0.022,
      handleMouseDown: handleMouseDown2,
      handleMouseMove: handleMouseMove2,
      handleMouseUp: handleMouseUp2,
    },
    {
      name: "Panier Bag",
      component: Paniercloser,
      position: [0, 0.01, 0],
      scale: 0.022,
      handleMouseDown: handleMouseDown3,
      handleMouseMove: handleMouseMove3,
      handleMouseUp: handleMouseUp3,
    },
    {
      name: "Tote M",
      component: ToteMcloser,
      position: [0, -0.1, 0],
      scale: 0.021,
      handleMouseDown: handleMouseDown4,
      handleMouseMove: handleMouseMove4,
      handleMouseUp: handleMouseUp4,
    },
    {
      name: "Tote L",
      component: ToteLcloser,
      position: [0, -0.1, 0],
      scale: 0.012,
      handleMouseDown: handleMouseDown5,
      handleMouseMove: handleMouseMove5,
      handleMouseUp: handleMouseUp5,
    },
  ];

  // Check if the screen is mobile size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Function to navigate between models
  const navigateModel = (direction) => {
    if (direction === "next") {
      setCurrentModelIndex((prev) =>
        prev === models.length - 1 ? 0 : prev + 1
      );
    } else {
      setCurrentModelIndex((prev) =>
        prev === 0 ? models.length - 1 : prev - 1
      );
    }
  };

  // The current model to display on mobile
  const currentModel = models[currentModelIndex];
  return (
    <>
      {showLoader && <Loaders redirectTo="/microbag" redirectDelay={4000} />}
      {showLoader1 && <Loaders redirectTo="/phonepouch" redirectDelay={4000} />}
      {showLoader2 && <Loaders redirectTo="/totes" redirectDelay={4000} />}
      {showLoader3 && <Loaders redirectTo="/panierbag" redirectDelay={4000} />}
      {showLoader4 && <Loaders redirectTo="/totem" redirectDelay={4000} />}
      {showLoader5 && <Loaders redirectTo="/totel" redirectDelay={4000} />}

      {isMobile ? (
        <div className={style.mobileSlider}>
          <div
            className={style.mobileCanvasContainer}
            onMouseDown={currentModel.handleMouseDown}
            onMouseMove={currentModel.handleMouseMove}
            onMouseUp={currentModel.handleMouseUp}
            onTouchStart={currentModel.handleMouseDown}
            onTouchMove={currentModel.handleMouseMove}
            onTouchEnd={currentModel.handleMouseUp}
          >
            <Canvas shadows gl={{ alpha: true, antialias: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  {React.createElement(currentModel.component, {
                    position: currentModel.position,
                    scale: currentModel.scale,
                  })}
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>

          <div className={style.mobileControls}>
            <button
              className={style.controlButton}
              onClick={() => navigateModel("prev")}
            >
              <FaArrowLeft />
            </button>
            <div className={style.modelName}>{currentModel.name}</div>
            <button
              className={style.controlButton}
              onClick={() => navigateModel("next")}
            >
              <FaArrowRight />
            </button>
          </div>

          {/* <div className={style.paginationDots}>
            {models.map((_, index) => (
              <div
                key={index}
                className={`${style.dot} ${
                  index === currentModelIndex ? style.activeDot : ""
                }`}
                onClick={() => setCurrentModelIndex(index)}
              />
            ))}
          </div> */}
        </div>
      ) : (
        <div className={style.container}>
          <div className={style.line}></div>
          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <Canvas shadows gl={{ alpha: true, antialias: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <Microcloser position={[0, 0, 0]} scale={0.015} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>
          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown1}
            onMouseMove={handleMouseMove1}
            onMouseUp={handleMouseUp1}
          >
            <Canvas shadows gl={{ alpha: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <PhonepouchClose position={[0, -0.1, 0]} scale={0.015} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>
          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown2}
            onMouseMove={handleMouseMove2}
            onMouseUp={handleMouseUp2}
          >
            <Canvas
              shadows
              gl={{ alpha: true, antialias: true, preserveDrawingBuffer: true }}
            >
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <ToteScloser position={[0, 0.02, 0]} scale={0.012} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>

          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown3}
            onMouseMove={handleMouseMove3}
            onMouseUp={handleMouseUp3}
          >
            <Canvas shadows gl={{ alpha: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <Paniercloser position={[0, 0.01, 0]} scale={0.012} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>

          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown4}
            onMouseMove={handleMouseMove4}
            onMouseUp={handleMouseUp4}
          >
            <Canvas shadows gl={{ alpha: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <ToteMcloser position={[0, -0.1, 0]} scale={0.013} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>
          <div
            className={style.canvasContainer}
            onMouseDown={handleMouseDown5}
            onMouseMove={handleMouseMove5}
            onMouseUp={handleMouseUp5}
          >
            <Canvas shadows gl={{ alpha: true }}>
              <Suspense fallback={<Loader />}>
                <SceneSetup>
                  <ToteLcloser position={[0, -0.1, 0]} scale={0.01} />
                </SceneSetup>
              </Suspense>
            </Canvas>
          </div>

          {/* <div className={style.canvasContainer}>
          <Canvas shadows gl={{ alpha: true }}>
            <Suspense fallback={null}>
              <SceneSetup>
                <ambientLight intensity={0.5} />
                <pointLight position={[10, 10, 10]} />
                <Mtl position={[0, 0, 0]} scale={[0.01, 0.01, 0.01]} />
                <OrbitControls /> */}
          {/* <Mtl position={[0, 0, 0]} scale={0.01} /> */}
          {/* </SceneSetup>
            </Suspense>
          </Canvas>
        </div> */}

          {/* <Picker /> */}
        </div>
      )}
    </>
  );
};

export default HomeCom;

// function Bag({ position, scale }) {
//   const ref = useRef();
//   const snap = useSnapshot(state);
//   const { nodes, materials } = useGLTF("/openwithoutchaincomp.glb");
//   const [isHovered, setIsHovered] = useState(false);
//   const bagName = "Microopen"; // Define the name of the bag

//   const uniqueMaterials = Object.keys(state.items).reduce((acc, key) => {
//     acc[key] = materials.Material.clone();
//     acc[key].name = key;
//     acc[key].roughness = 0.5;
//     acc[key].metalness = 0.1;
//     return acc;
//   }, {});

//   useFrame((state, delta) => {
//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//       const t = state.clock.getElapsedTime();
//       ref.current.rotation.y = Math.sin(t / 4) / 4;
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//       easing.dampE(ref.current.rotation, [0, 0, 0], 0.2, delta);
//     }
//   });

//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       onPointerMissed={() => (state.current = null)}
//       onClick={(e) => (
//         e.stopPropagation(), (state.current = e.object.material.name)
//       )}
//     >
//       {Object.keys(state.items).map((key) => (
//         <mesh
//           key={key}
//           receiveShadow
//           castShadow
//           geometry={nodes[key].geometry}
//           material={uniqueMaterials[key]}
//           material-color={snap.items[key]}
//         />
//       ))}
//       {isHovered && (
//         <Text
//           position={[0, -3.5, 0]} // Adjust the position as needed
//           fontSize={3}
//           color="#000"
//           anchorX="center"
//           anchorY="middle"
//         >
//           {bagName}
//         </Text>
//       )}
//     </group>
//   );
// }

// function Microopen({ position, scale }) {
//   const ref = useRef();
//   const [isHovered, setIsHovered] = useState(false);
//   const { nodes, materials } = useGLTF("/microopencomp.glb");

//   useFrame((state, delta) => {
//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//     }
//   });

//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       dispose={null}
//     >
//       <mesh
//         geometry={nodes.Bottom_Plate.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Magnet_Closure.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Object003.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Cylinder002.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape009.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Mirror.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Outer_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Middle_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Object005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Object006.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape012.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape014.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape015.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape016.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape017.geometry} material={materials.Material} />
//     </group>
//   );
// }

// function Paineropen({ position, scale }) {
//   const ref = useRef();
//   const [isHovered, setIsHovered] = useState(false);
//   const { nodes, materials } = useGLTF("/paineropencomp.glb");

//   // Check if materials exist and Material property is available
//   const customMaterial = useMemo(() => {
//     if (!materials || !materials.Material) {
//       // Return a default material if the model hasn't loaded yet
//       return new THREE.MeshStandardMaterial({
//         roughness: 0.5,
//         metalness: 0.3,
//         color: new THREE.Color("#333333"),
//       });
//     }

//     const material = materials.Material.clone();
//     material.roughness = 0.5;
//     material.metalness = 0.3;
//     material.color = new THREE.Color("#333333");
//     return material;
//   }, [materials]);

//   useFrame((state, delta) => {
//     if (!ref.current) return;

//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//     }
//   });

//   // Check if nodes exist before rendering
//   if (!nodes || !nodes.Mesh) {
//     return null;
//   }

//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       dispose={null}
//     >
//       <mesh
//         geometry={nodes.Mesh.geometry}
//         material={customMaterial}
//         castShadow
//         receiveShadow
//       />
//     </group>
//   );
// }

// function Phonepouch({ position, scale }) {
//   const ref = useRef();
//   const [isHovered, setIsHovered] = useState(false);
//   const { nodes, materials } = useGLTF("/phonepouchcomp.glb");

//   useFrame((state, delta) => {
//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//     }
//   });

//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       dispose={null}
//     >
//       <mesh
//         geometry={nodes.Bottom_Plate.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Magnet_Closure.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Object003.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Cylinder002.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape009.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Mirror.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Outer_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Middle_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Object005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape020.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape021.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape023.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape024.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape025.geometry} material={materials.Material} />
//     </group>
//   );
// }

// function Totelopen({ position, scale }) {
//   const ref = useRef();
//   const [isHovered, setIsHovered] = useState(false);
//   const { nodes, materials } = useGLTF("/totelopencomp.glb");

//   useFrame((state, delta) => {
//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//     }
//   });

//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       dispose={null}
//     >
//       <mesh
//         geometry={nodes.Cylinder002.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Outer_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Plane009.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Object003.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape009.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Plane013.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Cylinder004.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder005.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder006.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder007.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder008.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder009.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder013.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder014.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder015.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Plane014.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape010.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape011.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape012.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Inner_Layer001.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer002.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Plane015.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Box006.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes["default"].geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Torus001.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Plane016.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Inner_Layer003.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Shape013.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape014.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape015.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Cylinder016.geometry}
//         material={materials.Material}
//       />
//     </group>
//   );
// }

// function Totemopen({ position, scale }) {
//   const { nodes, materials } = useGLTF("/totemopencomp.glb");
//   const ref = useRef();
//   const [isHovered, setIsHovered] = useState(false);

//   useFrame((state, delta) => {
//     if (isHovered) {
//       easing.damp3(
//         ref.current.position,
//         [position[0], position[1] + 0.05, position[2]],
//         0.2,
//         delta
//       );
//     } else {
//       easing.damp3(ref.current.position, position, 0.2, delta);
//     }
//   });
//   return (
//     <group
//       ref={ref}
//       position={position}
//       scale={scale}
//       rotation={[0, 0, 0]}
//       onPointerOver={() => setIsHovered(true)}
//       onPointerOut={() => setIsHovered(false)}
//       dispose={null}
//     >
//       <mesh
//         geometry={nodes.Bottom_Plate.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Cylinder002.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Outer_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Plane009.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Plane011.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Object003.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape009.geometry} material={materials.Material} />
//       <mesh
//         geometry={nodes.Inner_Layer001.geometry}
//         material={materials.Material}
//       />
//       <mesh
//         geometry={nodes.Inner_Layer002.geometry}
//         material={materials.Material}
//       />
//       <mesh geometry={nodes.Shape010.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape011.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape012.geometry} material={materials.Material} />
//       <mesh geometry={nodes.Shape013.geometry} material={materials.Material} />
//     </group>
//   );
// }

function Mtl({ position, scale }) {
  const { nodes, materials: defaultMaterials } = useGLTF("/modecomp.glb");
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);

  // Load only leather texture
  const leatherTexture = useTexture("/LeatherNormalRedo.png");
  const uvGridTexture = useLoader(TextureLoader, "/LeatherNormalRedo.png");
  // Create custom leather material
  const customLeatherMaterial = new THREE.MeshStandardMaterial({
    map: uvGridTexture,
  });

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });

  return (
    <group
      ref={ref}
      position={position}
      scale={scale}
      rotation={[0, 0, 0]}
      onPointerOver={() => setIsHovered(true)}
      onPointerOut={() => setIsHovered(false)}
      dispose={null}
    >
      <mesh
        geometry={nodes.Plane009.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.Plane011.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.WLogoGrey.geometry}
        material={defaultMaterials.C4D_Logo}
      />
      <mesh
        geometry={nodes.Shape009.geometry}
        material={defaultMaterials.C4D_Stitch}
      />
      <mesh
        geometry={nodes.Cylinder002.geometry}
        material={defaultMaterials.C4D_Logo}
      />
      <mesh
        geometry={nodes.Shape010.geometry}
        material={defaultMaterials.C4D_Stitch}
      />
      <mesh
        geometry={nodes.Shape011.geometry}
        material={defaultMaterials.C4D_Stitch}
      />
      <mesh
        geometry={nodes.Bottom_Plate.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.Bottom_Plate_1.geometry}
        material={defaultMaterials.C4D_Logo}
      />
      <mesh
        geometry={nodes.Outer_Layer.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.Outer_Layer_1.geometry}
        material={customLeatherMaterial}
      />
      <mesh
        geometry={nodes.Inner_Layer.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.Inner_Layer_1.geometry}
        material={customLeatherMaterial}
      />
      <mesh
        geometry={nodes.Object003.geometry}
        material={defaultMaterials.C4D_PlasticBack}
      />
      <mesh
        geometry={nodes.Object003_1.geometry}
        material={customLeatherMaterial}
      />
    </group>
  );
}

function Picker() {
  const snap = useSnapshot(state);

  return (
    <div
      className={{
        ...style.picker,
        display: snap.current ? "block" : "none",
      }}
    >
      <HexColorPicker
        color={snap.items[snap.current]}
        onChange={(color) => (state.items[snap.current] = color)}
      />
      <h1 className={style.pickerTitle}>{snap.current}</h1>
    </div>
  );
}
