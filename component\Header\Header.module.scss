.container {
  width: 100%;
  padding-top: 16px;
  position: fixed;
  top: 0px;
  z-index: 999;
  // background-color: rgb(245, 245, 245);
}

.subCon {
  text-align: center;
  display: flex;
  justify-content: center;
  cursor: pointer; // Added for better UX
}

.logoContainer {
  position: relative;
  width: 200px; // Fixed container width
  height: 50px; // Fixed container height - adjust as needed
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  position: absolute;
  max-width: 200px;
  max-height: 100%;
  transition: opacity 0.3s ease-in-out; // Smooth transition
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.defaultLogo {
  opacity: 1;

  &.hidden {
    opacity: 0;
  }
}

.hoverLogo {
  opacity: 0;
  width: 53px; // Maintain your specified width

  &.visible {
    opacity: 1;
  }
}
