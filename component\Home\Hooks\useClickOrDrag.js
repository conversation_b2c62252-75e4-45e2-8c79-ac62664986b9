import { useState } from "react";

const useClickOrDrag = (onClickCallback) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleMouseDown = () => {
    setIsDragging(false);
  };

  const handleMouseMove = () => {
    if (!isDragging) {
      setIsDragging(true);
    }
  };

  const handleMouseUp = () => {
    if (!isDragging && onClickCallback) {
      onClickCallback();
    }
    setIsDragging(false);
  };

  return {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
  };
};

export default useClickOrDrag;
