import React, {
  useRef,
  useState,
  Suspense,
  useEffect,
  useMemo,
  useCallback,
  lazy,
  forwardRef,
} from "react";
import { Canvas, useThree, useFrame } from "@react-three/fiber";
import {
  useGLTF,
  useTexture,
  useFBX,
  Environment,
  Html,
} from "@react-three/drei";
import { proxy, useSnapshot } from "valtio";
import "rc-slider/assets/index.css";
import styles from "./SingleProduct.module.scss";
import { FaPlus, FaMinus } from "react-icons/fa";
import TabManager from "../Tabmanager/Tabmanager";
import * as THREE from "three";
import ModalVideo from "react-modal-video";
import { useInactivityDetection } from "../Home/Hooks/inactivity";
// import{createLoadingManager} from '../Home/Hooks/loadingmanager';
import ImgsPopUp from "../ImgsPopUp/ImgsPopUp";
import Loaders from "../Loader/Loaders";
import TextPopUp from "../ImgsPopUp/TextPopUp";
const DEFAULT_SCALE = 0.1;
let oscillation_count = 0;



class TexturePreloader {
  constructor() {
    this.loadedTextures = new Map();
    this.loadingPromises = new Map();
  }

  async preloadTextures(textureUrls) {
    const loader = new THREE.TextureLoader();
    const loadPromises = Object.entries(textureUrls).map(async ([key, url]) => {
      if (this.loadedTextures.has(key)) {
        return { key, texture: this.loadedTextures.get(key) };
      }

      if (this.loadingPromises.has(key)) {
        return this.loadingPromises.get(key);
      }

      const promise = new Promise((resolve, reject) => {
        loader.load(
          url,
          (texture) => {
            // Optimize texture settings immediately
            texture.colorSpace = key.includes('Albedo')
              ? THREE.SRGBColorSpace
              : THREE.NoColorSpace;
            texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
            texture.generateMipmaps = true;
            texture.minFilter = THREE.LinearMipmapLinearFilter;
            texture.magFilter = THREE.LinearFilter;

            this.loadedTextures.set(key, texture);
            resolve({ key, texture });
          },
          undefined,
          reject
        );
      });

      this.loadingPromises.set(key, promise);
      return promise;
    });

    const results = await Promise.all(loadPromises);
    return results.reduce((acc, { key, texture }) => {
      acc[key] = texture;
      return acc;
    }, {});
  } dispose() {
    this.loadedTextures.forEach(texture => texture.dispose());
    this.loadedTextures.clear();
    this.loadingPromises.clear();
  }
}
function LODModel({ distance, model, materials, onLoaded }) {
  const modelRef = useRef();
  const [isProcessed, setIsProcessed] = useState(false);

  useEffect(() => {
    if (!model || !materials || isProcessed) return;

    let processedMeshes = 0;
    let totalMeshes = 0;

    // Count total meshes first
    model.traverse((child) => {
      if (child.isMesh) totalMeshes++;
    });

    // Process meshes with LOD optimization
    model.traverse((child) => {
      if (child.isMesh) {
        // Enable frustum culling
        child.frustumCulled = true;

        // LOD-based geometry optimization
        if (distance > 50) {
          // Far LOD: Reduce geometry complexity
          if (child.geometry.attributes.position.count > 1000) {
            // Simplify geometry for distant viewing
            const simplifiedGeometry = child.geometry.clone();
            // You could implement mesh decimation here
            child.geometry = simplifiedGeometry;
          }
        }

        // Apply materials efficiently
        const materialName = child.material.name;
        if (materials[materialName]) {
          child.material = materials[materialName];
        }

        // Setup UV2 for AO maps if needed
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute('uv2', child.geometry.attributes.uv);
        }

        processedMeshes++;

        // Update loading progress
        if (processedMeshes === totalMeshes) {
          setIsProcessed(true);
          onLoaded?.();
        }
      }
    });
  }, [model, materials, distance, isProcessed, onLoaded]);

  return (
    <primitive
      ref={modelRef}
      object={model}
      castShadow
      receiveShadow
    />
  );
}



const createLoadingManager = () => {
  const loadingStates = new Map();
  const listeners = new Set();

  // Add initial loading state
  loadingStates.set('initial', true);

  const manager = {
    setLoading: (key, isLoading, timeout = 10000) => {
      if (isLoading) {
        loadingStates.set(key, true);
        console.log(`🔄 Loading started: ${key}`);

        // Auto-timeout to prevent infinite loading
        setTimeout(() => {
          if (loadingStates.has(key)) {
            console.warn(`⏰ Loading timeout for ${key}, forcing completion`);
            manager.setLoading(key, false);
          }
        }, timeout);
      } else {
        loadingStates.delete(key);
        console.log(`✅ Loading completed: ${key}`);
      }

      // Notify all listeners
      const isAnyLoading = loadingStates.size > 0;
      listeners.forEach(callback => callback(isAnyLoading, key));
    },

    subscribe: (callback) => {
      listeners.add(callback);
      return () => listeners.delete(callback);
    },

    isLoading: () => loadingStates.size > 0,
    getActiveLoaders: () => Array.from(loadingStates.keys()),
    clear: () => {
      loadingStates.clear();
      listeners.forEach(callback => callback(false, 'clear'));
    }
  };

  return manager;
};

const loadingManager = createLoadingManager();

// Loading hook
const useLoadingManager = () => {
  const [isLoading, setIsLoading] = useState(loadingManager.isLoading());
  const [activeLoaders, setActiveLoaders] = useState([]);

  useEffect(() => {
    const unsubscribe = loadingManager.subscribe((loading, key) => {
      // Add debouncing to prevent rapid state changes
      const timeoutId = setTimeout(() => {
        setIsLoading(loading);
        setActiveLoaders(loadingManager.getActiveLoaders());
      }, 0);

      return () => clearTimeout(timeoutId);
    });

    return unsubscribe;
  }, []); // Remove dependencies to prevent re-subscribing

  return {
    isLoading,
    activeLoaders,
    setLoading: useCallback((key, loading, timeout) => {
      loadingManager.setLoading(key, loading, timeout);
    }, []),
    clearAll: useCallback(() => {
      loadingManager.clear();
    }, [])
  };
};
const STRAP_TYPES = {
  none: {
    id: "none",
    name: "none",
    name: "No Strap",
    modelPath: null,
  },
  ball: {
    id: "ball",
    name: "Ball Strap",
    modelPath: "/ballstrapmicrocomp.glb",
    scale: DEFAULT_SCALE, // Use the default scale
    rotation: [0, 0, 0],
    position: [0, -0.5, 0], // Adjust these values to align the strap
  },
  handle: {
    id: "handle",
    name: "Handle Strap",
    modelPath: "/Handle_Strap_TOTE_S_M/totes.fbx",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE, // Use the default scale
  },
  metal: {
    id: "metal",
    name: "Metal Chain",
    modelPath: "/chain1microcomp.glb",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE, // Use the default scale
  },
};

const state = proxy({
  currentStrap: STRAP_TYPES.none.id,
  zoomedPart: null,
  scale: DEFAULT_SCALE,
  zoomLevel: 200,
  isOpen: true, // Add state to track if the model is open or closed
  colors: {
    plastic: "#E1D8CF",
    leather: "#C7B8A9",
    seams: "#D0C6BB", // 30% lighter than original,
  },
  strapcolors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF",
    seams: "#D0C6BB", // 30% lighter than original,
  },
  handleStrapColors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF",
    seams: "#D0C6BB",
  },

  shoulderStrapColors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF",
    seams: "#D0C6BB",
  },

  // strapcolors: "#e1e3e3",
  strapClicks: {}, // Track clicks for each strap
  strapOpacity: 0.5, // Default opacity
  strapColor: null,
  oscillation_count: 3,
  inactivityRotation: 0,
  isInactive: false,
  isLoading: true,
  firtsinteraction: 0,
});
const sharedRotationRef = { current: 0 };
const sharedInactivityRef = { current: false };
function useDebounce(callback, delay) {
  const timeoutRef = useRef(null);

  return useCallback((...args) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => callback(...args), delay);
  }, [callback, delay]);
}
function StrapWithTexturesnew({
  type,
  attachmentPoints,
  clickcount,
  position = [0, 0, 0],
  envMapIntensity = 1,
  fadeInDuration = 1000,
  xrayMode = false,
  selectedStraps,
  setLoading
}) {
  console.log("StrapWithTextures rendered with type:", type);
  const ref = useRef();
  const snap = useSnapshot(state);
  const [straprotate, setStraprotate] = useState(false);
  const [fadeProgress, setFadeProgress] = useState(0);
  const modelGroupRef = useRef();
  const modelRef = useRef();
  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});
  // Track all strap model refs for inactivity rotation
  const strapModelRefs = useRef({});

  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});
  const isInactive = useInactivityDetection(4000);

  // Define strap types and configurations
  const STRAP_TYPES = {
    NONE: {
      id: "none",
      name: "No Strap",
      modelPath: null,
    },
    HANDLE: {
      id: "handle",
      name: "Handle Strap",
      modelPath: "/handlestrapmicrou.fbx",
      position: [0, 0, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "microphonepainerhandletexu",
    },
    METAL: {
      id: "METAL",
      name: "Metal Chain",
      modelPath: "/chainmicrou2.fbx",
      position: [0, 0, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "chainmicrotexu2",
    },
    BALL: {
      id: "BALL",
      name: "BALL CHAIN",
      modelPath: "/balllchainmicrou.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "microballpainerballtexu",
    },
    SHOULDER: {
      id: "SHOULDER",
      name: "SHOULDER CHAIN",
      modelPath: "/shoulderstrapmicrobag.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "shoulderstraptexu",
    },
    STRAPM: {
      id: "STRAPM",
      name: "STRAP CHAIN",
      modelPath: "/strapmlongmicrou.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "strapmlongmicrophonetex",
    },
    SIDESTRAP3: {
      id: "SIDESTRAP3",
      name: "SIDESTRAP3",
      modelPath: "/strapmcurvemicro.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.077,
      textureFolder: "curvetexu",
    },
    LOGO: {
      id: "LOGO",
      name: "LOGO",
      modelPath: "/chainlogomicro.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.08,
      textureFolder: "chainlogomicrophonetex",
    },
    LOGO1: {
      id: "LOGO1",
      name: "LOGO1",
      modelPath: "/chainshortmicrouu.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.08,
      textureFolder: "chainlogo1microphonetex",
    },
  };

  // Function to get texture paths for a specific strap type
  const getTexturePathsForType = (strapType, textureFolder) => {
    const upperType = strapType.toUpperCase();

    switch (upperType) {
      case "HANDLE":
        return {
          leatherAlbedo: `/var2.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_1_albedo.jpg`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_1_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          metalNormal: `${textureFolder}/METAL_1_normal.png`,
          seamAlbedo: `${textureFolder}/seams_albedo.jpg`,
          seamNormal: `${textureFolder}/seams_normal.png`,
          seamRoughness: `${textureFolder}/seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/seams_metallic.jpg`,
          seamAO: `${textureFolder}/seams_AO.jpg`,
        };
      case "METAL":
        return {
          metalNormal: `${textureFolder}/METAL_1_normal.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_1_metallic.jpg`,
          plasticAlbedo: `/var2.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
        };
      case "BALL":
        return {
          plasticAlbedo: `/var1.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
          metalAlbedo: `${textureFolder}/METALL_albedo.jpg`,
          metalNormal: `${textureFolder}/METALL_normal.png`,
          metalRoughness: `${textureFolder}/METALL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METALL_metallic.jpg`,
          metalAO: `${textureFolder}/METALL_AO.jpg`,
        };
      case "SHOULDER":
        return {
          leatherAlbedo: `/var1.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          metalAlbedo: `${textureFolder}/Metal_PART1_albedo.jpg`,
          metalNormal: `${textureFolder}/Metal_PART1_normal.png`,
          metalRoughness: `${textureFolder}/Metal_PART1_roughness.jpg`,
          metalMetallic: `${textureFolder}/Metal_PART1_metallic.jpg`,
          metal2Albedo: `${textureFolder}/Metal_PART2_albedo.jpg`,
          metal2Normal: `${textureFolder}/Metal_PART2_normal.png`,
          metal2Roughness: `${textureFolder}/Metal_PART2_roughness.jpg`,
          metal2Metallic: `${textureFolder}/Metal_PART2_metallic.jpg`,
          seamAlbedo: `${textureFolder}/Seams_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_normal.png`,
          seamRoughness: `${textureFolder}/Seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_metallic.jpg`,
        };
      case "STRAPM":
        return {
          leatherAlbedo: `/var2.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_albedo.jpg`,
          metalNormal: `${textureFolder}/METAL_normal.png`,
          metalRoughness: `${textureFolder}/METAL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_AO.jpg`,
          seamAlbedo: `${textureFolder}/Seams_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_normal.png`,
          seamRoughness: `${textureFolder}/Seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_metallic.jpg`,
          seamAO: `${textureFolder}/Seams_AO.jpg`,
        };
      case "LOGO":
        return {
          metalAlbedo: `/var1.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_2_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          plasticAlbedo: `/var1.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
        };
      case "LOGO1":
        return {
          metalAlbedo: `/var1.png`,
          metalNormal: `${textureFolder}/METAL_1_normal.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_2_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          plasticAlbedo: `/var2.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
        };
      case "SIDESTRAP3":
        return {
          leatherAlbedo: `/var2.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_albedo.jpg`,
          metalNormal: `${textureFolder}/METAL_normal.png`,
          metalRoughness: `${textureFolder}/METAL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_AO.jpg`,
          seamAlbedo: `${textureFolder}/Seams_3_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_3_normal.png`,
          seamRoughness: `${textureFolder}/Seams_3_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_3_metallic.jpg`,
          seamAO: `${textureFolder}/Seams_3_AO.jpg`,
        };
      default:
        return {};
    }
  };

  // Determine which straps to render - FIXED VERSION
  const strapsToRender = (() => {
    // Use a Set to prevent duplicates
    const strapSet = new Set();

    // Add straps from selectedStraps
    if (selectedStraps) {
      if (selectedStraps.handle && selectedStraps.handle !== "none") {
        strapSet.add(selectedStraps.handle);
      }

      if (selectedStraps.shoulder && selectedStraps.shoulder !== "none") {
        strapSet.add(selectedStraps.shoulder);
      }
    }

    // Add the current type if it's not "none"
    if (type && type !== "none") {
      strapSet.add(type);
    }

    // Convert Set back to array
    let strapsArray = Array.from(strapSet);

    // Limit to maximum 2 straps
    const maxStraps = 2;
    if (strapsArray.length > maxStraps) {
      // Keep the most recent selections (prioritize current type)
      if (type && type !== "none" && strapsArray.includes(type)) {
        // Keep the current type and one other
        strapsArray = strapsArray
          .filter((strap) => strap === type)
          .concat(strapsArray.filter((strap) => strap !== type).slice(-1));
      } else {
        // Just take the last 2
        strapsArray = strapsArray.slice(-maxStraps);
      }
    }

    return strapsArray;
  })();

  console.log("Straps to render:", strapsToRender);
  console.log("Selected straps:", selectedStraps);

  // Load textures for all straps that need to be rendered
  const allTexturePaths = {};
  strapsToRender.forEach((strapType) => {
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (config && config.textureFolder) {
      const texturePaths = getTexturePathsForType(
        strapType,
        config.textureFolder
      );
      // Prefix each texture key with strap type to avoid conflicts
      Object.entries(texturePaths).forEach(([key, value]) => {
        allTexturePaths[`${strapType}_${key}`] = value;
      });
    }
  });

  // Load all textures at once
  const loadedTextures = useTexture(allTexturePaths);

  // Function to get textures for a specific strap type from loaded textures
  const getTexturesForStrap = (strapType) => {
    const strapTextures = {};
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (config && config.textureFolder) {
      const texturePaths = getTexturePathsForType(
        strapType,
        config.textureFolder
      );
      Object.keys(texturePaths).forEach((key) => {
        const textureKey = `${strapType}_${key}`;
        if (loadedTextures[textureKey]) {
          strapTextures[key] = loadedTextures[textureKey];
        }
      });
    }
    return strapTextures;
  };

  // Function to render a single strap
  const renderStrap = (strapType, key) => {
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (!config || !config.modelPath) return null;

    const textures = getTexturesForStrap(strapType);


    const getColorsForStrapType = (type) => {
      const upperType = type.toUpperCase();
      console.error("strap type", upperType)
      // Define which straps are handle straps
      const handleStrapTypes = ['HANDLE', 'BALL', 'METAL', 'STRAPM', 'SIDESTRAP3', 'LOGO', 'LOGO1'];

      // Define which straps are shoulder straps  
      const shoulderStrapTypes = ['SHOULDER'];

      if (handleStrapTypes.includes(upperType)) {
        return snap.strapcolors;
        return snap.handleStrapColors;
      } else if (shoulderStrapTypes.includes(upperType)) {
        console.error("@@@@@@")
        return snap.shoulderStrapColors;
      } else {
        // Fallback to default colors
        return snap.strapcolors;
      }
    };

    const strapColorss = getColorsForStrapType(strapType);
    console.error("!!!!", strapColorss);
    console.error("####", snap.strapcolors)
    // const strapColors = getStrapColors(strapType);
    // Setup props for the strap model
    const modelProps = {
      scale: config.scale || 1,
      rotation: config.rotation || [0, 1, 0],
      position: config.position || [0, 3, 0],
    };

    // Adjust position based on attachment points
    if (attachmentPoints && attachmentPoints.length > 0) {
      const attachmentPoint = attachmentPoints[0];
      modelProps.position = [
        attachmentPoint.position[0],
        attachmentPoint.position[1],
        attachmentPoint.position[2],
      ];

      // Apply specific rotation logic for different strap types
      if (strapType === "handle" && attachmentPoint.rotation) {
        console.log(
          "Applying attachment point rotation for handle:",
          attachmentPoint.rotation
        );
        modelProps.rotation = [...attachmentPoint.rotation];
      } else if (strapType === "ball") {
        console.log("Config rotation for ball:", config.rotation);
        modelProps.rotation = [
          config.rotation[0],
          config.rotation[1],
          config.rotation[2],
        ];
      }
    }

    return (
      <FBXStrap
        key={key}
        ref={strapType === type ? ref : undefined}
        type={strapType}
        modelPath={config.modelPath}
        textures={textures}
        position={modelProps.position}
        rotation={modelProps.rotation}
        scale={modelProps.scale}
        strapColors={strapColorss}
        opacity={1}
        envMapIntensity={envMapIntensity}
        modelGroupRef={modelGroupRef}
        modelRef={(strapRef) => {
          // Store all strap model refs for inactivity rotation
          if (strapRef) {
            strapModelRefs.current[strapType] = strapRef;
            // Also set the main modelRef if this is the primary type
            if (strapType === type) {
              modelRef.current = strapRef;
            }
          }
        }}
        xrayMode={xrayMode}
        setLoading={setLoading}
      />
    );
  };

  // Initialize strap opacity and click tracking
  useEffect(() => {
    if (!state.strapClicks[type]) {
      state.strapClicks[type] = 0;
      state.strapOpacity = 1;
    }
  }, [type]);

  // Update strap opacity based on click count
  useEffect(() => {
    if (clickcount === 1) {
      state.strapOpacity = 1;
    } else if (clickcount === 2) {
      state.strapOpacity = 1;
    }
  }, [clickcount]);

  // Updated useFrame to handle rotation for all visible straps
  useFrame(() => {
    if (sharedInactivityRef.current) {
      // Apply rotation to all visible strap models
      Object.values(strapModelRefs.current).forEach((strapRef) => {
        if (strapRef && strapRef.rotation) {
          strapRef.rotation.y = sharedRotationRef.current + 0.25;
        }
      });
    } else {
      // Reset rotation for all strap models when not inactive
      Object.values(strapModelRefs.current).forEach((strapRef) => {
        if (strapRef && strapRef.rotation) {
          strapRef.rotation.y = sharedRotationRef.current + 0.25;
        }
      });
    }
  });

  // Clean up refs when straps are no longer rendered
  useEffect(() => {
    // Remove refs for straps that are no longer being rendered
    const currentStrapTypes = strapsToRender.map((s) => s.toLowerCase());
    Object.keys(strapModelRefs.current).forEach((strapType) => {
      if (!currentStrapTypes.includes(strapType.toLowerCase())) {
        delete strapModelRefs.current[strapType];
      }
    });
  }, [strapsToRender]);

  // Return null if no straps should be rendered
  if (strapsToRender.length === 0) return null;

  // Render all straps that should be visible
  return (
    <>
      {strapsToRender.map((strapType, index) =>
        renderStrap(strapType, `${strapType}-${index}`)
      )}
    </>
  );
}
// Component to load and display the FBX strap with textures
function FBXStrap({
  type,
  modelPath,
  textures,
  position,
  rotation,
  scale,
  strapColors,
  opacity,
  envMapIntensity,
  modelGroupRef,
  modelRef,
  xrayMode,
  setLoading,
}) {
  console.log("=== LEATHER TEXTURE DEBUG ===");
  console.log("Strap colors from props:", strapColors);
  console.log("X-ray mode:", xrayMode);
  console.log("All textures received:", textures);
  console.log("Leather textures specifically:", {
    leatherAlbedo: textures?.leatherAlbedo,
    leatherNormal: textures?.leatherNormal,
    leatherRoughness: textures?.leatherRoughness,
    leatherMetallic: textures?.leatherMetallic,
    leatherAO: textures?.leatherAO,
  });

  const snap = useSnapshot(state);

  // Check if this strap type should use the special x-ray color
  const isLeatherStrapType = useMemo(() => {
    const upperType = type.toUpperCase();
    const leatherStrapTypes = ['HANDLE', 'STRAPM', 'SHOULDER', 'SIDESTRAP3'];
    return leatherStrapTypes.includes(upperType);
  }, [type]);

  // Calculate X-ray mode properties with different opacity levels
  const xrayOpacity = xrayMode ? 0.3 : opacity; // Non-metal opacity in x-ray mode
  const xrayMetalOpacity = xrayMode ? 0.7 : opacity; // Metal opacity in x-ray mode (less transparent)

  // Use #707A7C for leather strap types in x-ray mode, otherwise use purple
  const xrayColor = xrayMode ? (isLeatherStrapType ? "#707A7C" : "#8A2BE2") : null;

  console.log("X-ray color for type", type, ":", xrayColor, "(isLeatherStrapType:", isLeatherStrapType, ")");

  // Helper function to safely check and process textures
  const safeTexture = (texture, textureName) => {
    console.log(`Checking texture ${textureName}:`, texture);

    if (!texture) {
      console.warn(`${textureName} is null/undefined`);
      return null;
    }

    try {
      if (typeof texture === "string") {
        console.warn(
          `${textureName} is still a string path: "${texture}". Expected loaded texture object.`
        );
        return null;
      }

      if (typeof texture === "object") {
        console.log(`${textureName} properties:`, {
          isTexture: texture.isTexture,
          hasImage: !!texture.image,
          hasSource: !!texture.source,
          constructor: texture.constructor.name,
        });

        if (texture.isTexture || texture.image || texture.source) {
          // Set proper color space and wrapping
          texture.colorSpace = textureName.toLowerCase().includes("albedo")
            ? THREE.SRGBColorSpace
            : THREE.NoColorSpace;
          texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
          console.log(`✓ ${textureName} processed successfully`);
          return texture;
        }
      }

      console.warn(
        `${textureName} doesn't appear to be a valid texture object`
      );
      return null;
    } catch (error) {
      console.error(`Error processing ${textureName}:`, error);
      return null;
    }
  };

  // Helper function to determine if a material is metal-based
  const isMetalMaterial = (materialName) => {
    const metalKeywords = [
      'metal', 'Metal', 'METAL',
      'chain', 'Chain', 'CHAIN',
      'hook', 'Hook', 'HOOK',
      'link', 'Link', 'LINK',
      'buckle', 'Buckle', 'BUCKLE',
      'clasp', 'Clasp', 'CLASP',
      'screw', 'Screw', 'SCREW',
      'rivet', 'Rivet', 'RIVET',
      'bolt', 'Bolt', 'BOLT',
      'stud', 'Stud', 'STUD'
    ];
    return metalKeywords.some(keyword => materialName.includes(keyword));
  };

  // Cache material mappings based on type
  const materialMappings = useMemo(() => {
    const upperType = type.toUpperCase();
    console.log(`Creating material mappings for type: ${upperType}`);

    // Process leather textures with detailed logging
    const safeLeatherAlbedo = safeTexture(
      textures?.leatherAlbedo,
      "leatherAlbedo"
    );
    const safeLeatherNormal = safeTexture(
      textures?.leatherNormal,
      "leatherNormal"
    );
    const safeLeatherRoughness = safeTexture(
      textures?.leatherRoughness,
      "leatherRoughness"
    );
    const safeLeatherMetallic = safeTexture(
      textures?.leatherMetallic,
      "leatherMetallic"
    );
    const safeLeatherAO = safeTexture(textures?.leatherAO, "leatherAO");

    // Create leather mapping with extensive logging and X-ray mode support
    const leatherMapping = {
      ...(safeLeatherAlbedo && !xrayMode && { map: safeLeatherAlbedo }), // Disable textures in x-ray mode
      ...(safeLeatherNormal && !xrayMode && { normalMap: safeLeatherNormal }),
      ...(safeLeatherRoughness &&
        !xrayMode && { roughnessMap: safeLeatherRoughness }),
      ...(safeLeatherMetallic &&
        !xrayMode && { metalnessMap: safeLeatherMetallic }),
      ...(safeLeatherAO &&
        !xrayMode && { aoMap: safeLeatherAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.1 : 0.9, // Smoother in x-ray mode
      metalness: xrayMode ? 0.8 : 0, // More metallic in x-ray mode
      color: new THREE.Color(xrayColor || strapColors.leather),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      // X-ray mode depth settings for non-metals
      depthTest: true,
      depthWrite: xrayMode ? false : true, // Don't write to depth buffer in x-ray mode
      renderOrder: xrayMode ? -1 : 0, // Render behind metals in x-ray mode
    };

    console.log("Created leather mapping with color:", leatherMapping.color, "for x-ray mode:", xrayMode);

    // Process other textures
    const safeMetalAlbedo = safeTexture(textures?.metalAlbedo, "metalAlbedo");
    const safeMetalRoughness = safeTexture(
      textures?.metalRoughness,
      "metalRoughness"
    );
    const safeMetalMetallic = safeTexture(
      textures?.metalMetallic,
      "metalMetallic"
    );

    // METAL MATERIALS - Slightly transparent in x-ray mode but more visible than other parts
    const metalMapping = {
      ...(safeMetalAlbedo && { map: safeMetalAlbedo }), // Always show textures for metals
      ...(safeMetalRoughness && { roughnessMap: safeMetalRoughness }),
      ...(safeMetalMetallic && { metalnessMap: safeMetalMetallic }),
      roughness: 0.1, // Keep original roughness
      metalness: 1, // Keep fully metallic
      color: new THREE.Color(strapColors.metal), // Keep original metal color
      opacity: xrayMetalOpacity, // Use metal-specific opacity (0.7 in x-ray mode)
      transparent: xrayMode || xrayMetalOpacity < 1, // Transparent in x-ray mode
      // Metal depth settings - ensure metals render on top
      depthTest: true,
      depthWrite: true, // Write to depth buffer to occlude other materials
      renderOrder: xrayMode ? 1 : 0, // Render on top in x-ray mode
    };

    const safeMetal2Albedo = safeTexture(
      textures?.metal2Albedo,
      "metal2Albedo"
    );
    const safeMetal2Roughness = safeTexture(
      textures?.metal2Roughness,
      "metal2Roughness"
    );
    const safeMetal2Metallic = safeTexture(
      textures?.metal2Metallic,
      "metal2Metallic"
    );
    const safeMetal2AO = safeTexture(textures?.metal2AO, "metal2AO");

    const metal2Mapping = safeMetal2Albedo
      ? {
        ...(safeMetal2Albedo && { map: safeMetal2Albedo }), // Always show textures
        ...(safeMetal2Roughness && { roughnessMap: safeMetal2Roughness }),
        ...(safeMetal2Metallic && { metalnessMap: safeMetal2Metallic }),
        roughness: 0.1,
        metalness: 1,
        color: new THREE.Color(strapColors.metal),
        opacity: xrayMetalOpacity, // Use metal-specific opacity
        transparent: xrayMode || xrayMetalOpacity < 1,
        depthTest: true,
        depthWrite: true,
        renderOrder: xrayMode ? 1 : 0,
      }
      : metalMapping;

    const safePlasticAlbedo = safeTexture(
      textures?.plasticAlbedo,
      "plasticAlbedo"
    );
    const safePlasticRoughness = safeTexture(
      textures?.plasticRoughness,
      "plasticRoughness"
    );
    const safePlasticMetallic = safeTexture(
      textures?.plasticMetallic,
      "plasticMetallic"
    );
    const safePlasticAO = safeTexture(textures?.plasticAO, "plasticAO");

    const plasticMapping = {
      ...(safePlasticAlbedo && !xrayMode && { map: safePlasticAlbedo }),
      ...(safePlasticRoughness &&
        !xrayMode && { roughnessMap: safePlasticRoughness }),
      ...(safePlasticMetallic &&
        !xrayMode && { metalnessMap: safePlasticMetallic }),
      ...(safePlasticAO &&
        !xrayMode && { aoMap: safePlasticAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.15 : 0.3,
      metalness: xrayMode ? 0.9 : 0.8,
      color: new THREE.Color(xrayColor || strapColors.plastic),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      depthTest: true,
      depthWrite: xrayMode ? false : true,
      renderOrder: xrayMode ? -1 : 0,
    };

    const safeSeamAlbedo = safeTexture(textures?.seamAlbedo, "seamAlbedo");
    const safeSeamNormal = safeTexture(textures?.seamNormal, "seamNormal");
    const safeSeamRoughness = safeTexture(
      textures?.seamRoughness,
      "seamRoughness"
    );
    const safeSeamMetallic = safeTexture(
      textures?.seamMetallic,
      "seamMetallic"
    );
    const safeSeamAO = safeTexture(textures?.seamAO, "seamAO");

    const seamMapping = {
      ...(safeSeamAlbedo && !xrayMode && { map: safeSeamAlbedo }),
      ...(safeSeamNormal && !xrayMode && { normalMap: safeSeamNormal }),
      ...(safeSeamRoughness &&
        !xrayMode && { roughnessMap: safeSeamRoughness }),
      ...(safeSeamMetallic && !xrayMode && { metalnessMap: safeSeamMetallic }),
      ...(safeSeamAO &&
        !xrayMode && { aoMap: safeSeamAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.2 : 0.4,
      metalness: xrayMode ? 0.7 : 0.0,
      color: new THREE.Color(xrayColor || strapColors.seams),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      depthTest: true,
      depthWrite: xrayMode ? false : true,
      renderOrder: xrayMode ? -1 : 0,
    };

    const safeBeltAlbedo = safeTexture(textures?.beltAlbedo, "beltAlbedo");
    const safeBeltNormal = safeTexture(textures?.beltNormal, "beltNormal");
    const safeBeltRoughness = safeTexture(
      textures?.beltRoughness,
      "beltRoughness"
    );
    const safeBeltMetallic = safeTexture(
      textures?.beltMetallic,
      "beltMetallic"
    );
    const safeBeltAO = safeTexture(textures?.beltAO, "beltAO");

    const beltMapping = safeBeltAlbedo
      ? {
        ...(safeBeltAlbedo && !xrayMode && { map: safeBeltAlbedo }),
        ...(safeBeltNormal && !xrayMode && { normalMap: safeBeltNormal }),
        ...(safeBeltRoughness &&
          !xrayMode && { roughnessMap: safeBeltRoughness }),
        ...(safeBeltMetallic &&
          !xrayMode && { metalnessMap: safeBeltMetallic }),
        ...(safeBeltAO &&
          !xrayMode && { aoMap: safeBeltAO, aoMapIntensity: 1.0 }),
        roughness: xrayMode ? 0.1 : 0.9,
        metalness: xrayMode ? 0.8 : 0,
        color: new THREE.Color(strapColors.metal),
        opacity: xrayOpacity,
        transparent: xrayOpacity < 1 || xrayMode,
        depthTest: true,
        depthWrite: xrayMode ? false : true,
        renderOrder: xrayMode ? -1 : 0,
      }
      : leatherMapping;

    // Create comprehensive material mappings with more variations
    const createMappingSet = (defaultMapping) => ({
      // Leather variations - EXTENSIVE list
      Leather: leatherMapping,
      LEATHER: leatherMapping,
      leather: leatherMapping,
      Leather_01: leatherMapping,
      Leather_1: leatherMapping,
      "Leather.001": leatherMapping,
      Leather_Material: leatherMapping,
      LeatherMaterial: leatherMapping,
      leather_mat: leatherMapping,
      Belt: leatherMapping,
      BELT: leatherMapping,
      belt: leatherMapping,
      Belt2: beltMapping,
      BELT2: beltMapping,
      belt2: beltMapping,
      Strap: leatherMapping,
      STRAP: leatherMapping,
      strap: leatherMapping,

      // Metal variations - Use metal-specific opacity in x-ray mode
      Metal: metalMapping,
      METAL: metalMapping,
      metal: metalMapping,
      Metal_01: metalMapping,
      Metal_1: metalMapping,
      "Metal.001": metalMapping,
      Metal2: metal2Mapping,
      METAL2: metal2Mapping,
      metal2: metal2Mapping,
      Metal3: metalMapping,
      METAL3: metalMapping,
      metal3: metalMapping,
      Metal_PART1: metalMapping,
      Metal_PART2: metal2Mapping,
      METAL_PART1: metalMapping,
      METAL_PART2: metal2Mapping,
      HOOK_1: metalMapping,
      Chain: metalMapping,
      CHAIN: metalMapping,
      Link: metalMapping,
      LINK: metalMapping,
      Buckle: metalMapping,
      BUCKLE: metalMapping,
      buckle: metalMapping,
      Clasp: metalMapping,
      CLASP: metalMapping,
      clasp: metalMapping,

      // Plastic variations
      Plastic: plasticMapping,
      PLASTIC: plasticMapping,
      plastic: plasticMapping,
      Logo: plasticMapping,
      LOGO: plasticMapping,
      logo: plasticMapping,

      // Seam variations
      Seams: seamMapping,
      SEAMS: seamMapping,
      seams: seamMapping,
      Seams_3: seamMapping,
      SEAMS_3: seamMapping,

      // Generic material names
      Material: defaultMapping,
      material: defaultMapping,
      "Material.001": defaultMapping,
      "Material.002": metalMapping,
      "Material.003": plasticMapping,

      DEFAULT: defaultMapping,
    });

    // Return appropriate mapping based on strap type
    console.log(`Returning mappings for type: ${upperType}`);

    if (
      upperType === "HANDLE" ||
      upperType === "SHOULDER" ||
      upperType === "STRAPM" ||
      upperType === "SIDESTRAP3"
    ) {
      const mappings = createMappingSet(leatherMapping);
      console.log("Leather-based mappings created:", Object.keys(mappings));
      return mappings;
    } else if (
      upperType === "METAL" ||
      upperType === "LOGO" ||
      upperType === "LOGO1"
    ) {
      const mappings = createMappingSet(metalMapping);
      console.log("Metal-based mappings created:", Object.keys(mappings));
      return mappings;
    } else if (upperType === "BALL") {
      const mappings = createMappingSet(plasticMapping);
      console.log("Plastic-based mappings created:", Object.keys(mappings));
      return mappings;
    } else {
      const mappings = createMappingSet(leatherMapping);
      console.log("Generic mappings created:", Object.keys(mappings));
      return mappings;
    }
  }, [textures, strapColors, opacity, type, xrayMode, xrayOpacity, xrayMetalOpacity, xrayColor, isLeatherStrapType]);

  // Load FBX model
  const fbx = useFBX(modelPath);
  const model = useMemo(() => fbx.clone(), [fbx]);

  // Process the FBX model and apply materials
  // In FBXStrap component, simplify the useEffect:
  useEffect(() => {
    if (!model || !textures) {
      console.warn("Model or textures not available yet");
      return;
    }

    console.log("=== PROCESSING MODEL MATERIALS ===");

    let timeoutId;

    try {
      // Apply materials to the model
      model.traverse((child) => {
        if (child.isMesh) {
          const materialName = child.material?.name || "";
          console.log(`Processing mesh: "${child.name}" with material: "${materialName}"`);

          // Check if this is a metal material
          const isMetal = isMetalMaterial(materialName);

          // Create new material with appropriate opacity
          const newMaterial = new THREE.MeshStandardMaterial({
            name: materialName,
            opacity: isMetal && xrayMode ? xrayMetalOpacity : xrayOpacity,
            transparent: xrayMode || (isMetal ? xrayMetalOpacity < 1 : xrayOpacity < 1),
          });

          // Find and apply matching mapping
          let appliedMapping = materialMappings.DEFAULT;
          let matchedKey = "DEFAULT";

          // Try exact matches first
          Object.keys(materialMappings).forEach((matKey) => {
            if (matKey !== "DEFAULT" && materialName === matKey && materialMappings[matKey]) {
              appliedMapping = materialMappings[matKey];
              matchedKey = matKey;
            }
          });

          // Try partial matches if no exact match
          if (matchedKey === "DEFAULT") {
            Object.keys(materialMappings).forEach((matKey) => {
              if (
                matKey !== "DEFAULT" &&
                (materialName.includes(matKey) || matKey.toLowerCase().includes(materialName.toLowerCase())) &&
                materialMappings[matKey]
              ) {
                appliedMapping = materialMappings[matKey];
                matchedKey = matKey;
              }
            });
          }

          // Apply the mapping
          if (appliedMapping) {
            Object.assign(newMaterial, appliedMapping);

            // Special handling for x-ray mode
            if (isMetal && xrayMode) {
              newMaterial.opacity = xrayMetalOpacity;
              newMaterial.transparent = true;
              newMaterial.depthWrite = true;
              newMaterial.renderOrder = 1;
            } else if (xrayMode) {
              newMaterial.depthWrite = false;
              newMaterial.renderOrder = -1;
            }
          }

          // Setup UV coordinates
          if (child.geometry?.attributes?.uv && !child.geometry.attributes.uv2) {
            child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
          }

          // Apply environment map intensity
          newMaterial.envMapIntensity = isMetal && xrayMode ?
            (envMapIntensity || 1.0) :
            (xrayMode ? 0.3 : (envMapIntensity || 1.0));

          // Set the material
          child.material = newMaterial;
          child.castShadow = !xrayMode;
          child.receiveShadow = !xrayMode;
          child.material.needsUpdate = true;
        }
      });

      console.log("=== MODEL PROCESSING COMPLETE ===");

      // Set loading to false after a brief delay
      timeoutId = setTimeout(() => {
        if (setLoading) {
          setLoading('new strap', false);
        }
      }, 100);

    } catch (error) {
      console.error("Error in material processing:", error);
      if (setLoading) {
        timeoutId = setTimeout(() => {
          setLoading('new strap', false);
        }, 100);
      }
    }

    // Cleanup timeout on unmount or dependency change
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [
    model,
    textures,
    materialMappings,
    strapColors,
    opacity,
    envMapIntensity,
    type,
    xrayMode,
    xrayOpacity,
    xrayMetalOpacity,
    // setLoading removed from dependencies
  ]);

  // Compute scale as array if a number is provided
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  return (
    <group
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
      ref={modelRef}
    >
      <group ref={modelGroupRef}>
        <primitive object={model} />
      </group>
    </group>
  );
}
// Concentric Spheres Component

function CameraZoom() {
  const snap = useSnapshot(state);
  const { camera } = useThree();

  useEffect(() => {
    const minZ = 1.5; // Closest zoom
    const maxZ = 8; // Furthest zoom
    const zoomRange = maxZ - minZ;

    // Convert zoom level (130-230) to camera position
    // Inverted relationship: higher zoom level = closer camera
    const targetZ = maxZ - ((snap.zoomLevel - 130) / 100) * zoomRange;

    // Smoothly animate to the new position
    const duration = 0.3;
    const currentZ = camera.position.z;

    // Simple animation function using requestAnimationFrame
    let startTime = null;
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / (duration * 1000), 1);

      // Ease function (cubic ease-out)
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      // Update camera position
      camera.position.z = currentZ + (targetZ - currentZ) * easeProgress;

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [snap.zoomLevel, camera]);

  return null;
}
function FBXModelWithTexturesclosedh(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 0, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    setActiveTab,
    sethoveredsphere,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setTitle,
    setanimation,
    setanimationsource,
    sethighlightdata,
    setLoading,
    showInfo,
    isLoading,
    setShowTabs,
    showTabs
  } = props;
  const isTogglingModelRef = useRef(false);
  const useMobileDetection = () => useState(() => window.innerWidth <= 768)[0];
  const [modelPath, setModelPath] = useState("/microupdate.fbx");
  const [modelKey, setModelKey] = useState(Date.now());
  const [isMirrorOpen, setIsMirrorOpen] = useState(false);
  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] =
    useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(true); // Show by default
  const [fingerIconStartTime, setFingerIconStartTime] = useState(null); // Track finger icon animation start
  const [modelLoaded, setModelLoaded] = useState(false); // Track if model is loaded

  // Add state for rotation-based highlighting
  const [rotationBasedHighlight, setRotationBasedHighlight] = useState(null);
  const [lastRotationAngle, setLastRotationAngle] = useState(0);
  const [highlightInfo, setHighlightInfo] = useState(null);

  // NEW: Add permanent disable states
  const [rotationHighlightingDisabled, setRotationHighlightingDisabled] = useState(false);
  const [rotationHighlightingHasStarted, setRotationHighlightingHasStarted] = useState(false);

  const fingerIconRef = useRef(null);

  const initialAnimationDuration = 5000; // 5 seconds for initial animation
  const animationDelayTime = 3000; // 3 second delay before starting animation
  const initialRotationAmount = 0.07; // How much to rotate in each direction
  const fingerOscillationDuration = 3000; // 3 seconds for finger oscillation
  const snap = useSnapshot(state);
  const oscillationCount = snap.oscillation_count;
  const modelGroupRef = useRef();
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);
  const isInactive = useInactivityDetection(4000);
  console.log("Is mobile device:", useMobileDetection());
  const isMobile = useMobileDetection();
  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});
  const isInactiveForRotation = useInactivityDetection(100);
  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});

  // Define part pairs that should highlight together
  const partPairs = useMemo(() => {
    return {
      // LOCK_1: ["LOCK_2", "Metal"],
      LEATHER_2_1: ["LEATHER_2_1", "MIRROR_1"],
      MIRROR_1: ["LEATHER_2_1", "MIRROR_1"],
      // Add more pairs as needed
    };
  }, []);

  // Keep track of which parts are hoverable
  const hoverableParts = useMemo(
    () => [
      "Magnet_Closure",
      // "PLASTIC_2",
      "Metal",
      "Bottom_Plate",
      "LOCK_2",
      "LOCK_1",
      "WLogoGrey",
      "Mirror",
      "Object005",
      // "Object003",
      "Plane009",
      "METALL_2",
      "LEATHER_2_1",
      "MIRROR_1",
      "LOGO",
      "magnet_closer_",
      "bottom_plate_",
    ],
    []
  );

  // Load FBX model based on current state
  const fbx = useFBX(modelPath);
  const model = useMemo(() => fbx.clone(), [fbx]);

  const tempcolor = state.colors;

  // Load finger icon texture
  const fingerTexture = useTexture("/select.png");

  // Memoize textures to avoid unnecessary reloads
  const textures = useTexture({
    // Leather 1 textures
    leather1Albedo: "/microbagwithmirrortex/LEATHER_albedo.jpg",
    leather1Normal: "/microbagwithmirrortex/LEATHER_normal.png",
    leather1Roughness: "/microbagwithmirrortex/LEATHER_roughness.jpg",
    leather1Metallic: "/microbagwithmirrortex/LEATHER_metallic.jpg",
    leather1AO: "/microbagwithmirrortex/LEATHER_AO.jpg",

    // Leather 2 textures
    leather2Albedo: "/textures/2_LEATHER_albedo.jpg",
    leather2Normal: "/textures/2_LEATHER_normal.png",
    leather2Roughness: "/textures/2_LEATHER_roughness.jpg",
    leather2Metallic: "/textures/2_LEATHER_metallic.jpg",
    leather2AO: "/textures/2_LEATHER_AO.jpg",

    // Metal textures
    metallAlbedo: "/microbagwithmirrortex/Metal_albedo.jpg",
    // metallNormal: "/microbagwithmirrortex/Metal_normal.png",
    metallRoughness: "/microbagwithmirrortex/Metal_roughness.jpg",
    metallMetallic: "/microbagwithmirrortex/Metal_metallic.jpg",
    metallAO: "/microbagwithmirrortex/Metal_AO.jpg",

    // Plastic textures
    plasticAlbedo: "/microbagwithmirrortex/PLASTIC_albedo.jpg",
    // plasticNormal: "/microbagwithmirrortex/PLASTIC_normal.png",
    plasticRoughness: "/microbagwithmirrortex/PLASTIC_roughness.jpg",
    plasticMetallic: "/microbagwithmirrortex/PLASTIC_metallic.jpg",
    plasticAO: "/microbagwithmirrortex/PLASTIC_AO.jpg",

    // Seams textures
    seamsAlbedo: "/microbagwithmirrortex/seams_albedo.jpg",
    seamsNormal: "/microbagwithmirrortex/seams_normal.png",
    seamsRoughness: "/microbagwithmirrortex/seams_roughness.jpg",
    seamsMetallic: "/microbagwithmirrortex/seams_metallic.jpg",
    seamsAO: "/microbagwithmirrortex/seams_AO.jpg",
  });

  // Cache material mappings
  const materialMappings = useMemo(
    () => ({
      LEATHER: {
        map: textures.leather1Albedo,
        normalMap: textures.leather1Normal,
        roughnessMap: textures.leather1Roughness,
        metalnessMap: textures.leather1Metallic,
        aoMap: textures.leather1AO,
        aoMapIntensity: 1.0,
        roughness: 0.9,
        metalness: 0,
        color: new THREE.Color(tempcolor.leather),
      },
      "2_LEATHER": {
        map: textures.leather2Albedo,
        normalMap: textures.leather2Normal,
        roughnessMap: textures.leather2Roughness,
        metalnessMap: textures.leather2Metallic,
        aoMap: textures.leather2AO,
        aoMapIntensity: 1.0,
      },
      Metal: {
        map: textures.metallAlbedo,
        // normalMap: textures.metallNormal,
        roughnessMap: textures.metallRoughness,
        metalnessMap: textures.metallMetallic,
        // aoMap: textures.metallAO,
        aoMapIntensity: 1.0,
        metalness: 1,
      },
      PLASTIC: {
        map: textures.plasticAlbedo,
        normalMap: textures.plasticNormal,
        roughnessMap: textures.plasticRoughness,
        metalnessMap: textures.plasticMetallic,
        aoMap: textures.plasticAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.plastic),
      },
      seams: {
        map: textures.seamsAlbedo,
        normalMap: textures.seamsNormal,
        roughnessMap: textures.seamsRoughness,
        metalnessMap: textures.seamsMetallic,
        aoMap: textures.seamsAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.seams),
      },
    }),
    [textures, tempcolor]
  );

  // MODIFIED: Function to determine which part to highlight based on rotation angle
  const getPartToHighlightByRotation = useCallback(
    (rotationAngle) => {
      // If rotation highlighting has been permanently disabled, always return null
      if (rotationHighlightingDisabled) {
        console.log("Rotation highlighting permanently disabled - returning null");
        return null;
      }

      // Check if user is currently active/interacting
      if (!isInactiveForRotation) {
        // If rotation highlighting had started and now user is active, disable it permanently
        if (rotationHighlightingHasStarted) {
          console.log("User became active after rotation highlighting started - permanently disabling");
          setRotationHighlightingDisabled(true);
          sethighlightdata(null);
          setHighlightInfo(null);
          state.firtsinteraction = 1; // Mark first interaction
          return null;
        }

        // If rotation highlighting hasn't started yet, just clear current highlights
        sethighlightdata(null);
        setHighlightInfo(null);
        state.firtsinteraction = 1;
        return null;
      }

      // Normalize angle to 0-360 degrees
      console.log("mmmmmmmmmm", rotationAngle);
      const normalizedAngle =
        ((((rotationAngle * 180) / Math.PI) % 360) + 360) % 360;

      // Check for specific rotation threshold OR if user has had their first interaction
      if (rotationAngle <= -6.283185307179586 || state.firtsinteraction === 1) {
        sethighlightdata(null);
        return null;
      }

      // Mark that rotation highlighting has started
      if (!rotationHighlightingHasStarted) {
        console.log("Rotation highlighting starting for the first time");
        setRotationHighlightingHasStarted(true);
      }

      // Create pulsing effect using timestamp
      const pulseInterval = 100; // Pulse every 100ms
      const shouldHighlight = Math.floor(Date.now() / pulseInterval) % 2 === 0;

      // If we're in the "off" phase of the pulse, return null
      if (!shouldHighlight) {
        return "xyz";
      }

      // Define rotation ranges for different parts (only when highlighting is "on")
      if (normalizedAngle >= 315 || normalizedAngle < 45) {
        // Front view (0 degrees ± 45) - highlight magnet closer
        setHighlightInfo("OPEN BAG");
        sethighlightdata("PRESS HERE TO OPEN BAG");
        return "magnet_closer_";
      } else if (normalizedAngle >= 45 && normalizedAngle < 135) {
        // Right side view (90 degrees ± 45) - highlight side fasteners
        setHighlightInfo("SIDE FASTENERS");
        sethighlightdata("PRESS HERE TO ATTACH STRAPS");
        return "Metal";
      } else if (normalizedAngle >= 135 && normalizedAngle < 225) {
        // Back view (180 degrees ± 45) - could highlight back features
        setHighlightInfo(null);
        sethighlightdata("PULL OUT MIRROR");
        return "LEATHER_2_1";
      } else if (normalizedAngle >= 225 && normalizedAngle < 315) {
        // Left side view (270 degrees ± 45) - highlight side fasteners
        setHighlightInfo("SIDE FASTENERS");
        sethighlightdata("PRESS HERE TO ATTACH STRAPS");
        return "Metal";
      }

      setHighlightInfo(null);
      return null;
    },
    [setHighlightInfo, isInactiveForRotation, sethighlightdata, rotationHighlightingDisabled, rotationHighlightingHasStarted]
  );

  // Function to apply rotation-based highlighting
  const applyRotationBasedHighlight = useCallback(
    (partName) => {
      if (partName) {
        console.log("Rotation-based highlighting:", partName);
        setRotationBasedHighlight(partName);
        handlePartHover(partName);
      } else {
        setRotationBasedHighlight(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle]
  );

  // Add useEffect to initialize first interaction state
  useEffect(() => {
    // Initialize first interaction state if not already set
    if (state.firtsinteraction === undefined) {
      state.firtsinteraction = 0;
    }
  }, []);

  // Debug: Track rotation highlighting state changes
  useEffect(() => {
    console.log("Rotation highlighting states:", {
      disabled: rotationHighlightingDisabled,
      hasStarted: rotationHighlightingHasStarted,
      isInactive: isInactiveForRotation
    });
  }, [rotationHighlightingDisabled, rotationHighlightingHasStarted, isInactiveForRotation]);

  // Add useEffect to permanently disable rotation highlighting on any user activity
  useEffect(() => {
    if (rotationHighlightingHasStarted && !isInactiveForRotation && !rotationHighlightingDisabled) {
      console.log("User activity detected after rotation highlighting started - permanently disabling");
      setRotationHighlightingDisabled(true);
      setRotationBasedHighlight(null);
      sethighlightdata(null);
      setHighlightInfo(null);
      sethoveredsphere?.(null);
      setTitle?.(null);
    }
  }, [isInactiveForRotation, rotationHighlightingHasStarted, rotationHighlightingDisabled, sethighlightdata, sethoveredsphere, setTitle]);

  // Add useEffect to clear highlighting when first interaction occurs
  useEffect(() => {
    if (state.firtsinteraction === 1) {
      setRotationBasedHighlight(null);
      sethighlightdata(null);
      setHighlightInfo(null);
      sethoveredsphere?.(null);
      setTitle?.(null);
    }
  }, [state.firtsinteraction, sethighlightdata, sethoveredsphere, setTitle]);

  // MODIFIED: switchModel function with optional reset
  const switchModel = useCallback(
    (newModelPath) => {
      console.log("Switching model to:", newModelPath);

      // Clear hover state
      setHoveredPart(null);
      setRotationBasedHighlight(null);
      sethoveredsphere?.(null);
      setTitle?.(null);

      // Reset rotation highlighting states (OPTIONAL - remove these lines if you want it to stay disabled across model switches)
      console.log("Resetting rotation highlighting states for new model");
      setRotationHighlightingDisabled(false);
      setRotationHighlightingHasStarted(false);

      // Reset material references
      allMeshRefs.current = {};
      originalMaterials.current = {};

      // Reset model loaded state and finger icon
      setModelLoaded(false);
      setShowFingerIcon(true);
      setFingerIconStartTime(null);

      // Change the model path
      setModelPath(newModelPath);

      // Force component to remount completely with a new key
      setModelKey(Date.now());
    },
    [sethoveredsphere, setTitle]
  );

  // Reset state when model changes
  useEffect(() => {
    // Clear mesh references to prepare for new model
    allMeshRefs.current = {};
    originalMaterials.current = {};
    setModelLoaded(false);
    setRotationBasedHighlight(null);
  }, [sethoveredsphere, setTitle]);

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;

      // Fix: Use exact matching for parts that need it, particularly LEATHER_2_1 vs LEATHER_2
      // Check for exact match first
      if (hoverableParts.includes(partName)) {
        return true;
      }

      // Then check for partial match, but make sure we're not matching LEATHER_2_1 when checking for LEATHER_2
      return hoverableParts.some((part) => {
        // Use exact match for leather parts to prevent LEATHER_2 from matching LEATHER_2_1
        if (partName.includes("LEATHER_") || part.includes("LEATHER_")) {
          return partName === part;
        }
        // For other parts, continue using partial matching
        return partName.includes(part);
      });
    },
    [hoverableParts]
  );

  // Helper function to get all parts that should be highlighted together
  const getRelatedParts = useCallback(
    (partName) => {
      // Find the base part name that matches our pairs
      const basePart = Object.keys(partPairs).find((key) => partName === key);

      // If we found a base part that has defined pairs, return all related parts
      if (basePart && partPairs[basePart]) {
        return partPairs[basePart];
      }

      // If no pairing defined, just return the original part
      return [partName];
    },
    [partPairs]
  );
  const xyz = isMobile;
  // Handle part hover
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;
      // Set hover information based on part name
      setHoveredPart(partName);

      if (
        partName.includes("PLASTIC_2") ||
        partName.includes("Plane009") ||
        partName.includes("magnet_closer_")
      ) {
        console.log("Hovering over magnet closure in closed micro");
        sethoveredsphere?.(
          "<br><b>MAGNETIC CLASP:<br></b>The WMB MAGFRAME™ clasp is custom-crafted in Italy from Rhodoid — a sustainable cellulose-based material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a flawless fit, it closes with quiet precision and enduring strength — a refined mechanism built into every WMB bag."
        );
      } else if (
        partName.includes("Bottom_Plate") ||
        partName.includes("bottom_plate_")
      ) {
        setTitle?.("BASE PLATE:");
        sethoveredsphere?.(
          "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB's engineered precision."
        );
      } else if (partName.includes("LOGO")) {
        setanimation(true);
        setanimationsource("/logo.mp4");
        // setTitle?.("<br><br><br><br><br><br>METAL MONOGRAM");
        sethoveredsphere?.(
          "<br><br><b>METAL MONOGRAM</b> <br><br>A chromed metal plate, partially embedded into the leather and set with the WMB monogram in relief.<br><br>A restrained signature — discreet, dimensional, and unmistakable."
        );
      } else if (partName === "LEATHER_2_1" || partName.includes("MIRROR_1")) {
        setanimation(true);
        setanimationsource("/Mirror2.mp4");
        sethoveredsphere?.(
          "A modern essential. This chromed metal mirror, engraved with the WMB monogram, is concealed within the bag's architecture. Revealed by a tonal leather tab, it reflects our focus on thoughtful utility and considered form."
        );
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("Inner") ||
        partName.includes("LEATHER_2")
      ) {
        setTitle?.("");
        sethoveredsphere?.(
          "<b>The Micro - Frame</b><br><br>\
          A compact form built for essentials — cards, keys, AirPods.<br>\
          Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for lasting structure and architectural clarity.<br><br>\
          The exterior features a chromed metal mirror engraved with the WMB monogram, revealed by a tonal leather pull tab.<br>\
          A magnetic clasp, CNC-milled and laser-cut from sustainable Italian Rhodoid, secures the bag with a clean, precisely engineered close.<br>\
          Finished with a raised metal monogram and modular side fasteners that accommodate one or two straps — allowing the bag to shift seamlessly between hand, shoulder, or crossbody carry.<br><br>\
          Compact, precise, and built for movement.<br>\
          Handcrafted in Germany.<br><br>\
          • Dimensions: 12.34 cm x 4.30 cm x 8.01 cm<br>\
          • Materials: Epsom Leather / Nappa lining / Palladium plated steel<br>\
          • Strap: Adjustable leather shoulder strap and leather handle strap included<br>\
          • Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>\
          • Colour options: Charcoal, Burgundy, Sand, Petrol<br>\
          • Hardware made in Italy"
        );
      } else if (partName.includes("Metal")) {
        setanimation(true);
        setanimationsource("/side2u.mp4");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      }
    },

    [sethoveredsphere, setTitle, setTitle, setanimation, setanimationsource]
  );

  // Handle part click
  const handlePartClick = useCallback(
    (partName) => {
      console.log("Clicked on:", partName);
      if ((partName.includes("Metal") && isMobile)) {
        setShowTabs(true)
        setActiveTab(2);
        handlePartHover(partName);
      }
      // If showInfo is true, apply hover-like effects on click
      if (showInfo && isMobile) {
        handlePartHover(partName); // This will trigger the same effects as hover

        // Clear the hover state after a delay (e.g., 3 seconds)
        setTimeout(() => {
          setHoveredPart(null);
          // sethoveredsphere?.(null);
          // setTitle?.(null);
          // setanimation?.(false);
          // setanimationsource?.(null);
        }, 1000); // Adjust the delay as needed (3000ms = 3 seconds)
      } else {
        if (
          partName.includes("magnet_closer_") ||
          partName.includes("Plane009") ||
          partName.includes("Magnet_Closure")
        ) {
          // Toggle the bag open/close state
          toggleModel();
        } else if (partName.includes("Metal") || partName.includes("LOCK_2")) {
          // Handle lock parts clicks
          // console.error("Active tab is set to 3 ");
          // If the bag is closed, open it
          setShowTabs(true)
          setActiveTab?.(2);
        } else if (
          partName.includes("MIRROR_1") ||
          partName === "LEATHER_2_1"
        ) {
          // Toggle between mirror models
          const newState = !isMirrorOpen;
          console.log("Toggling mirror to:", newState ? "open" : "closed");
          const newPath =
            modelPath === "/microupdate.fbx"
              ? "/micromirror.fbx"
              : "/microupdate.fbx";
          switchModel(newPath);
          setLoading({ newPath }, true);
        }
      }

      // Handle specific part interactions

      // Hide finger icon when user interacts with the bag
      setShowFingerIcon(false);
    },
    [
      setActiveTab,
      modelPath,
      switchModel,
      showInfo,
      handlePartHover,
      isMobile,
      sethoveredsphere,
      setTitle,
      setanimation,
      setanimationsource,
    ] // Added showInfo and handlePartHover to dependencies
  );

  console.log("Inactivity status:", isInactive);
  useEffect(() => {
    sharedInactivityRef.current = isInactive;
    setShowSpheres(isInactive);
  }, [isInactive]);

  // References for initial animation

  // MODIFIED: Apply hover effects and animations in useFrame
  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      // Scale-based animation - start smaller and grow to full size
      // const scaleValue = 0.8 + fadeProgress * 0.2; // Scale from 80% to 100%
      modelGroupRef.current.scale.set(0.8, 0.8, 0.8);
      modelGroupRef.current.rotation.y = (1 - fadeProgress) * Math.PI * 0.08;
      // Add slight rotation during fade-in for more dynamic effect
    }

    // Apply initial animation
    if (1) {
      const elapsedTime = Date.now() - initialAnimationStartTime;

      if (elapsedTime <= initialAnimationDuration) {
        const progress = elapsedTime / initialAnimationDuration;
        // Use oscillation count from global state
        // const oscillation =
        //   Math.sin(progress * Math.PI * oscillationCount) * 0.3;

        // console.log(`Progress: ${progress}, Oscillation: ${oscillation}`);

        // // Try multiple approaches to apply rotation
        // if (ref?.current) {
        //   ref.current.rotation.y = oscillation;
        //   console.log(`Applied rotation to ref: ${oscillation}`);
        // }

        // if (modelGroupRef?.current) {
        //   modelGroupRef.current.rotation.y = oscillation;
        //   console.log(`Applied rotation to modelGroup: ${oscillation}`);
        // }

        // Update shared rotation ref if you have it
        if (sharedRotationRef) {
          sharedRotationRef.current = oscillation;
        }
      }
    } else if (isInactive && ref.current) {
      // ref.current.rotation.y -= 0.0025;
    }

    // modelGroupRef.current.rotation.y = oscillation;
    // Apply finger icon oscillation animation when model is loaded
    if (
      showFingerIcon &&
      modelLoaded &&
      fingerIconRef.current &&
      fingerIconStartTime
    ) {
      const elapsedTime = Date.now() - fingerIconStartTime;

      if (elapsedTime <= fingerOscillationDuration) {
        const progress = elapsedTime / fingerOscillationDuration;
        // Create oscillating movement (left-right swing)
        const oscillation = Math.sin(progress * Math.PI * 4) * 0.5; // 4 oscillations over 3 seconds

        // Apply horizontal oscillation to the finger icon
        fingerIconRef.current.position.x = oscillation;

        // Optional: Add slight vertical bounce
        const bounce = Math.abs(Math.sin(progress * Math.PI * 8)) * 0.2;
        fingerIconRef.current.position.y = bounce;

        // Fade out towards the end
        if (progress > 0.8) {
          const fadeProgress = (progress - 0.8) / 0.2;
          fingerIconRef.current.material.opacity = 0.9 * (1 - fadeProgress);
        }
      } else {
        // Animation complete, hide finger icon
        setShowFingerIcon(false);
      }
    }

    // MODIFIED: Handle inactivity rotation and rotation-based highlighting
    if (sharedInactivityRef.current && ref.current) {
      sharedRotationRef.current -= 0.0025;
      ref.current.rotation.y = sharedRotationRef.current;

      // Only do rotation-based highlighting if it hasn't been permanently disabled
      if (!rotationHighlightingDisabled) {
        // Check if rotation angle has changed significantly and update highlighting
        const currentRotation = sharedRotationRef.current;
        const rotationDifference = Math.abs(currentRotation - lastRotationAngle);

        // Update highlighting every 5 degrees (approximately 0.087 radians)
        if (rotationDifference > 0.087 && isLoading == false) {
          const partToHighlight = getPartToHighlightByRotation(currentRotation);

          // Only update if the part to highlight has changed
          if (partToHighlight !== rotationBasedHighlight) {
            applyRotationBasedHighlight(partToHighlight);
          }

          setLastRotationAngle(currentRotation);
        }
      } else {
        // If highlighting is disabled, ensure all highlight data is cleared
        if (rotationBasedHighlight) {
          setRotationBasedHighlight(null);
          sethighlightdata(null);
          setHighlightInfo(null);
          sethoveredsphere?.(null);
          setTitle?.(null);
          applyRotationBasedHighlight(null);
        }
      }
    } else if (
      !initialAnimationActive &&
      ref.current &&
      !sharedInactivityRef.current
    ) {
      ref.current.rotation.y = sharedRotationRef.current;

      // Always clear highlight data when user becomes active
      // sethighlightdata(null);

      // Clear rotation-based highlighting when not inactive
      if (rotationBasedHighlight && !hoveredPart) {
        setRotationBasedHighlight(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    }

    // Update shared inactivity state
    state.isInactive = isInactive;

    // Handle hover effects
    // Determine which part should be highlighted (user hover takes priority over rotation-based)
    const activeHighlight = hoveredPart || rotationBasedHighlight;

    // Step 1: First, clear any previous hover effects if there's no currently highlighted part
    if (!activeHighlight) {
      // Restore original materials for all previously highlighted parts
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    }

    // Step 2: Apply hover effect to currently highlighted part and related parts
    else if (activeHighlight) {
      // Get all parts that should be highlighted together
      const relatedParts = getRelatedParts(activeHighlight);

      // Highlight all related parts
      relatedParts.forEach((basePart) => {
        // Find all meshes that contain this base part name
        Object.keys(allMeshRefs.current).forEach((meshName) => {
          // Fix: For LEATHER parts, use exact matching to avoid LEATHER_2 triggering LEATHER_2_1
          let shouldHighlight = false;

          if (meshName.includes("LEATHER_") || basePart.includes("LEATHER_")) {
            // Use exact match for LEATHER parts
            shouldHighlight = meshName === basePart;
          } else {
            // For other parts, continue using includes
            shouldHighlight = meshName.includes(basePart);
          }

          if (shouldHighlight && isHoverablePart(meshName)) {
            const mesh = allMeshRefs.current[meshName];

            if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
              // Store original material if not already stored
              if (!originalMaterials.current[meshName]) {
                originalMaterials.current[meshName] = mesh.material.clone();
              }

              // Create a new hover material
              const hoverMaterial = mesh.material.clone();

              // Apply hover effect based on part type
              if (
                meshName.includes("Magnet_Closure") ||
                meshName.includes("Plane009") ||
                meshName.includes("PLASTIC_2") ||
                meshName.includes("magnet_closer_")
              ) {
                // Strong hover effect for clickable parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("LOCK_1") ||
                meshName.includes("LOCK_2")
              ) {
                // Special effect for lock parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("METALL_2") ||
                meshName.includes("Bottom_Plate") ||
                meshName.includes("Cylinder") ||
                meshName.includes("Mirror") ||
                meshName.includes("WLogoGrey") ||
                meshName.includes("Object005") ||
                meshName.includes("MIRROR_1") ||
                meshName === "LEATHER_2_1" ||
                meshName.includes("Metal")
              ) {
                // Medium hover effect for metal parts and mirror parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else {
                // Subtle hover effect for other parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
                // Keep the original color but brighten it slightly
                if (
                  originalMaterials.current[meshName] &&
                  originalMaterials.current[meshName].color
                ) {
                  const originalColor =
                    originalMaterials.current[meshName].color.clone();
                  hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
                }
              }

              // Mark this as a hover material
              hoverMaterial._isHoverMaterial = true;
              hoverMaterial.needsUpdate = true;

              // Apply the hover material
              mesh.material = hoverMaterial;
            }
          }
        });
      });
    }
  });

  // In FBXModelWithTexturesclosedh, replace the material setup useEffect:
  // In FBXModelWithTexturesclosedh, fix the useEffect:
  useEffect(() => {
    if (!model || !textures) return;

    let timeoutId;
    let fingerTimeoutId;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Store material name for reference
        const materialName = child.material.name;
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
          roughness: 0.8,
          metalness: 0.0,
          envMapIntensity: envMapIntensity,
        });

        // Apply material mappings
        if (materialMappings[materialName]) {
          Object.assign(newMaterial, materialMappings[materialName]);
        }

        // Store original material
        originalMaterials.current[child.name] = newMaterial.clone();

        // Apply material to mesh
        child.material = newMaterial;
        child.castShadow = true;
        child.receiveShadow = true;
        child.material.needsUpdate = true;
      }
    });

    // Set model as loaded
    setModelLoaded(true);

    // Set loading states with delays
    timeoutId = setTimeout(() => {
      if (setLoading) {
        setLoading('initial', false);
        setLoading('micro-closed', false);
      }
    }, 50);

    fingerTimeoutId = setTimeout(() => {
      setFingerIconStartTime(Date.now());
    }, 1000);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (fingerTimeoutId) clearTimeout(fingerTimeoutId);
    };
  }, [model, textures, materialMappings, envMapIntensity]);// Remove setLoading from dependencies

  // Fade-in effect
  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;
      if (isMobile) return;
      if (partName) {
        console.log("Hovering over:", partName);
        handlePartHover(partName);
      }
    },
    [isHoverablePart, handlePartHover]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setanimation?.(false);
      setanimationsource?.(null);

      // Only clear hover state on non-touch devices
      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle, setanimation, setanimationsource]
  );
  const toggleModel = useCallback(() => {
    console.log("Toggling model state");

    if (isTogglingModelRef.current) return;
    isTogglingModelRef.current = true;

    setTimeout(() => {
      setLoading('model-toggle', true);
      state.isOpen = !state.isOpen;
      console.log("Model state is now:", state.isOpen);
      isTogglingModelRef.current = false;
    }, 50);
  }, [setLoading]); // Remove setLoading from dependencies
  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        console.log("Clicked on:", partName);
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  // Create a finger icon sprite material
  // Calculate finger icon position

  // Debug log for component re-renders
  useEffect(() => {
    console.log(
      "Component rendered, mirror state:",
      isMirrorOpen ? "open" : "closed"
    );
  });

  return (
    <group
      key={modelKey}
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      {/* Model group with fade-in effect */}
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={(e) => {
            handlePointerOver(e);
            // handlePartHovermobile(e);
          }}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>

      {/* HTML overlay for highlight information */}
    </group>
  );
}
function FBXModelWithTexturesopenh(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 0, 0],
    scale = 1,
    setActiveTab,
    sethoveredsphere,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setTitle,
    setLoading,
    showInfo, // Add showInfo prop
    setShowTabs,
  } = props;
  const isTogglingModelRef = useRef(false);
  // Add mobile detection
  const useMobileDetection = () => useState(() => window.innerWidth <= 768)[0];
  const isMobile = useMobileDetection();

  const isInactive = useInactivityDetection(4000);
  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] =
    useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(false);
  const initialAnimationDuration = 5000; // 5 seconds for initial animation
  const animationDelayTime = 3000; // 3 second delay before starting animation
  const initialRotationAmount = 0.07; // How much to rotate in each direction
  const fingerIconRef = useRef(null);
  const snap = useSnapshot(state);
  const modelGroupRef = useRef();
  const modelRef = useRef();
  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});

  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});

  // Define part pairs that should highlight together
  const partPairs = useMemo(() => {
    return {
      LOCK_1: ["LOCK_2", "Metal"],
      Magnet_Closure: ["Magnet_Closure", "Plane009"],
      Plane009: ["Magnet_Closure", "Plane009"],
      Mirror: ["Mirror", "WLogoGrey"],
      WLogoGrey: ["Mirror", "WLogoGrey"],

      // Add more pairs as needed
    };
  }, []);

  // Keep track of which parts are hoverable
  const hoverableParts = useMemo(
    () => [
      "Magnet_Closure",
      "Plane009",
      "METALL_2",
      "Bottom_Plate",
      "LOCK_2",
      "LOCK_1",
      "WLogoGrey",
      "Mirror",
      "Object005",
      "Metal",
      "Cylinder",
    ],
    []
  );

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;
      return hoverableParts.some((part) => partName.includes(part));
    },
    [hoverableParts]
  );

  useFrame(() => {
    if (sharedInactivityRef.current && modelRef.current) {
      // Apply the same rotation as the main model
      sharedRotationRef.current -= 0.0025;
      modelRef.current.rotation.y = sharedRotationRef.current;
    } else if (modelRef.current && !sharedInactivityRef.current) {
      // Reset when not inactive
      modelRef.current.rotation.y = sharedRotationRef.current;
    }
  });

  state.isInactive = isInactive;

  // Helper function to get all parts that should be highlighted together
  const getRelatedParts = useCallback(
    (partName) => {
      // Find the base part name that matches our pairs
      const basePart = Object.keys(partPairs).find((key) =>
        partName.includes(key)
      );

      // If we found a base part that has defined pairs, return all related parts
      if (basePart && partPairs[basePart]) {
        return partPairs[basePart];
      }

      // If no pairing defined, just return the original part
      return [partName];
    },
    [partPairs]
  );
  const toggleModel = useCallback(() => {
    console.log("Toggling model state");

    if (isTogglingModelRef.current) return;
    isTogglingModelRef.current = true;

    setTimeout(() => {
      setLoading('model-toggle', true);
      state.isOpen = !state.isOpen;
      console.log("Model state is now:", state.isOpen);
      isTogglingModelRef.current = false;
    }, 50);
  }, [setLoading]);// Remove setLoading from dependencies
  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX("/Open.fbx");
  const model = useMemo(() => fbx.clone(), [fbx]);
  const tempcolor = state.colors;

  // Load finger icon texture
  const fingerTexture = useTexture("/select.png");

  // Load textures
  const textures = useTexture({
    // Leather 1 textures
    leather1Albedo: "/textures4k/2/Leather_albedo.jpg",
    leather1Normal: "/textures4k/2/Leather_normal.png",
    leather1Roughness: "/textures4k/2/Leather_roughness.jpg",
    leather1Metallic: "/textures4k/2/Leather_metallic.jpg",
    leather1AO: "/textures4k/2/Leather_AO.jpg",

    // Metal textures
    metallAlbedo: "/textures4k/2/METALL_albedo.jpg",
    metallNormal: "/textures4k/2/METALL_normal.png",
    metallRoughness: "/textures4k/2/METALL_roughness.jpg",
    metallMetallic: "/textures4k/2/METALL_metallic.jpg",
    metallAO: "/textures4k/2/METALL_AO.jpg",

    // Plastic textures
    plasticAlbedo: "/textures4k/2/Plastic_albedo.jpg",
    plasticNormal: "/textures4k/2/Plastic_normal.png",
    plasticRoughness: "textures4k/2/Plastic_roughness.jpg",
    plasticMetallic: "/textures4k/2/Plastic_metallic.jpg",
    plasticAO: "/textures4k/2/Plastic_AO.jpg",

    // Seams textures
    seamsAlbedo: "/textures4k/2/Seams_albedo.jpg",
    seamsNormal: "/textures4k/2/Seams_normal.png",
    seamsRoughness: "/textures4k/2/Seams_roughness.jpg",
    seamsMetallic: "/textures4k/2/Seams_metallic.jpg",
    seamsAO: "/textures4k/2/Seams_AO.jpg",
  });

  // Cache material mappings
  const materialMappings = useMemo(
    () => ({
      Leather: {
        map: textures.leather1Albedo,
        normalMap: textures.leather1Normal,
        roughnessMap: textures.leather1Roughness,
        metalnessMap: textures.leather1Metallic,
        aoMap: textures.leather1AO,
        aoMapIntensity: 1.0,
        roughness: 0.9,
        metalness: 0,
        color: new THREE.Color(tempcolor.leather),
      },
      METALL: {
        map: textures.metallAlbedo,
        // normalMap: textures.metallNormal,
        roughnessMap: textures.metallRoughness,
        metalnessMap: textures.metallMetallic,
        aoMap: textures.metallAO,
        aoMapIntensity: 1.0,
        metalness: 1,
      },
      Plastic: {
        map: textures.plasticAlbedo,
        normalMap: textures.plasticNormal,
        roughnessMap: textures.plasticRoughness,
        metalnessMap: textures.plasticMetallic,
        aoMap: textures.plasticAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.plastic),
      },
      Seams: {
        map: textures.seamsAlbedo,
        normalMap: textures.seamsNormal,
        roughnessMap: textures.seamsRoughness,
        metalnessMap: textures.seamsMetallic,
        aoMap: textures.seamsAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.seams),
      },
    }),
    [textures, tempcolor]
  );

  // Handle part hover
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;

      // Set hover information based on part name
      setHoveredPart(partName);

      if (
        partName.includes("Magnet_Closure") ||
        partName.includes("Plane009")
      ) {
        setTitle?.("CLOSE BAG ");
        sethoveredsphere?.(
          "The WMB MAGFRAME™ clasp is custom-crafted from Italian Rhodoid — a sustainable cellulose material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a perfect fit, it closes with smooth precision and quiet certainty. Engineered for longevity, built into every WMB bag."
        );
      } else if (
        partName.includes("Bottom_Plate")
      ) {
        setTitle?.("BASE PLATE:");
        sethoveredsphere?.(
          "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB's engineered precision."
        );
      } else if (partName.includes("Cylinder")) {
        sethoveredsphere?.("Side fastener");
      } else if (
        partName.includes("Mirror") ||
        partName.includes("WLogoGrey")
      ) {
        setTitle?.("PULL OUT MIRROR");
        sethoveredsphere?.(
          "A modern essential. This chromed metal mirror, engraved with the WMB monogram, is concealed within the bag's architecture. Revealed by a tonal leather tab, it reflects our focus on thoughtful utility and considered form."
        );
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("2_LEATHER") ||
        partName.includes("Inner")
      ) {
        setTitle?.("");
        sethoveredsphere?.(
          "<b>The Micro - Frame</b><br><br>\
          A compact form built for essentials — cards, keys, AirPods.<br>\
          Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for lasting structure and architectural clarity.<br><br>\
          The exterior features a chromed metal mirror engraved with the WMB monogram, revealed by a tonal leather pull tab.<br>\
          A magnetic clasp, CNC-milled and laser-cut from sustainable Italian Rhodoid, secures the bag with a clean, precisely engineered close.<br>\
          Finished with a raised metal monogram and modular side fasteners that accommodate one or two straps — allowing the bag to shift seamlessly between hand, shoulder, or crossbody carry.<br><br>\
          Compact, precise, and built for movement.<br>\
          Handcrafted in Germany.<br><br>\
          • Dimensions: 12.34 cm x 4.30 cm x 8.01 cm<br>\
          • Materials: Epsom Leather / Nappa lining / Palladium plated steel<br>\
          • Strap: Adjustable leather shoulder strap and leather handle strap included<br>\
          • Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>\
          • Colour options: Charcoal, Burgundy, Sand, Petrol<br>\
          • Hardware made in Italy"
        );
      } else if (partName.includes("LOCK_1") || partName.includes("LOCK_2")) {
        setTitle?.("ATTACH STRAPS");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      }
    },
    [sethoveredsphere, setTitle]
  );

  // Handle part click - ENHANCED with mobile click-as-hover functionality
  const handlePartClick = useCallback(
    (partName) => {
      console.log("partname in openfbx on click", partName);

      // Mobile-specific click-as-hover behavior
      if (partName.includes("LOCK_1") || partName.includes("LOCK_2") && isMobile) {
        setShowTabs(true);
        setActiveTab?.(3);
        handlePartHover(partName);
      }

      // If showInfo is true and on mobile, apply hover-like effects on click
      if (showInfo && isMobile) {
        handlePartHover(partName); // This will trigger the same effects as hover

        // Clear the hover state after a delay (e.g., 1 second)
        setTimeout(() => {
          setHoveredPart(null);
          // Optionally clear other states too:
          // sethoveredsphere?.(null);
          // setTitle?.(null);
        }, 1000);
      } else {
        // Regular click behavior for desktop or when showInfo is false
        if (
          partName.includes("Magnet_Closure") ||
          partName.includes("Plane009")
        ) {
          // Toggle the bag open/close state
          toggleModel();
        } else if (
          partName.includes("Metal") ||
          partName.includes("LOCK_1") ||
          partName.includes("LOCK_2")
        ) {
          setShowTabs(true);
          // Handle lock parts clicks
          setActiveTab?.(3);
        } else if (
          partName.includes("METALL_2") ||
          partName.includes("Bottom_Plate")
        ) {
          setShowTabs(true);
          // Handle metal parts clicks
          setActiveTab?.(2);
        }
      }

      // Hide finger icon when user interacts with the bag
      setShowFingerIcon(false);
    },
    [setActiveTab, showInfo, handlePartHover, isMobile]
  );

  // Optimized inactivity detection
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);

  useEffect(() => {
    sharedInactivityRef.current = isInactive;
    setShowSpheres(isInactive);
  }, [isInactive]);

  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      // Scale-based animation - start smaller and grow to full size
      const scaleValue = 0.8 + fadeProgress * 0.2; // Scale from 80% to 100%
      modelGroupRef.current.scale.set(scaleValue, scaleValue, scaleValue);

      // Add slight rotation during fade-in for more dynamic effect
      modelGroupRef.current.rotation.y = (1 - fadeProgress) * Math.PI * 0.08;
    }

    // Handle hover effects
    // Step 1: First, clear any previous hover effects if there's no currently hovered part
    if (!hoveredPart) {
      // Restore original materials for all previously hovered parts
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    }

    // Step 2: Apply hover effect to currently hovered part and related parts
    else if (hoveredPart) {
      // Get all parts that should be highlighted together
      const relatedParts = getRelatedParts(hoveredPart);

      // Highlight all related parts
      relatedParts.forEach((basePart) => {
        // Find all meshes that contain this base part name
        Object.keys(allMeshRefs.current).forEach((meshName) => {
          if (meshName.includes(basePart) && isHoverablePart(meshName)) {
            const mesh = allMeshRefs.current[meshName];

            if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
              // Store original material if not already stored
              if (!originalMaterials.current[meshName]) {
                originalMaterials.current[meshName] = mesh.material.clone();
              }

              // Create a new hover material
              const hoverMaterial = mesh.material.clone();

              // Apply hover effect based on part type
              if (
                meshName.includes("Magnet_Closure") ||
                meshName.includes("Plane009")
              ) {
                // Strong hover effect for clickable parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("LOCK_1") ||
                meshName.includes("LOCK_2")
              ) {
                // Special effect for lock parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("METALL_2") ||
                meshName.includes("Bottom_Plate") ||
                meshName.includes("Cylinder") ||
                meshName.includes("Mirror") ||
                meshName.includes("WLogoGrey")
              ) {
                // Medium hover effect for metal parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else {
                // Subtle hover effect for other parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.2;
                // Keep the original color but brighten it slightly
                if (
                  originalMaterials.current[meshName] &&
                  originalMaterials.current[meshName].color
                ) {
                  const originalColor =
                    originalMaterials.current[meshName].color.clone();
                  hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
                }
              }

              // Mark this as a hover material
              hoverMaterial._isHoverMaterial = true;
              hoverMaterial.needsUpdate = true;

              // Apply the hover material
              mesh.material = hoverMaterial;
            }
          }
        });
      });
    }
  });

  useEffect(() => {
    if (!model || !textures) return;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Store material name for reference
        const materialName = child.material.name;

        // Create optimized material
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
        });

        // Find and apply the correct textures
        Object.keys(materialMappings).forEach((matKey) => {
          if (materialName.includes(matKey)) {
            Object.assign(newMaterial, materialMappings[matKey]);
          }
        });

        // Setup uv2 coordinates efficiently
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Apply environment map intensity
        newMaterial.envMapIntensity = envMapIntensity;

        // Set the new material
        child.material = newMaterial;
        child.castShadow = true;
        child.material.needsUpdate = true;
      }
    }); // Start finger icon animation after a short delay
    setLoading('open-micro', false)// Start finger icon animation after a short delay
    // Cleanup
    return () => {
      allMeshRefs.current = {};
      originalMaterials.current = {};
    };
  }, [model, textures, materialMappings, envMapIntensity]);

  // Event handlers for pointer events - ENHANCED with mobile detection
  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      // Skip hover on mobile devices (similar to closed component)
      if (isMobile) return;

      // Set the hovered part and call the hover handler
      if (partName) {
        setHoveredPart(partName);
        handlePartHover(partName);
        console.log("Hovering over:", partName);
      }

      // Reset inactivity timer
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = setTimeout(() => {
          if (!isMouseDownRef.current) {
            setShowSpheres(true);
          }
        }, 4000);
      }

      setShowSpheres(false);
      setShowFingerIcon(false);
    },
    [handlePartHover, isMobile] // Added isMobile to dependencies
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setHoveredPart(null);
      // Only clear hover state on non-touch devices
      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle]
  );

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  // Create a finger icon sprite material
  const fingerMaterial = useMemo(() => {
    if (!fingerTexture) {
      return new THREE.SpriteMaterial({
        color: 0xffff00,
        opacity: 0.7,
        transparent: true,
      });
    }

    return new THREE.SpriteMaterial({
      map: fingerTexture,
      transparent: true,
      opacity: 0.9,
      color: 0xffff00,
    });
  }, [fingerTexture]);

  // Calculate finger icon position
  const fingerIconPosition = useMemo(() => {
    return [position[0] + 5.5, position[1] + 4.5, position[2] + 5];
  }, [position]);

  return (
    <group
      ref={modelRef}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      {/* Model group with fade-in effect */}
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>

      {/* Finger icon sprite */}
      {showFingerIcon && (
        <group position={fingerIconPosition}>
          <sprite
            ref={fingerIconRef}
            material={fingerMaterial}
            scale={[1.5, 1.5, 1.5]}
          />

          {/* Text hint */}
          <Html position={[0, -4, 0]} center>
            <div
              style={{
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px 10px",
                borderRadius: "4px",
                fontSize: "14px",
                whiteSpace: "nowrap",
                fontFamily: "Arial, sans-serif",
              }}
            >
              Drag to rotate
            </div>
          </Html>
        </group>
      )}
    </group>
  );
}


function FBXModelWithTexturesclosedd(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 1, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    setActiveTab,
    sethoveredsphere,
    setTitle,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setanimation,
    setanimationsource,
    setLoading
  } = props;

  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] = useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(false);

  const initialAnimationDuration = 5000;
  const animationDelayTime = 3000;
  const initialRotationAmount = 1;
  const fingerIconRef = useRef(null);
  const snap = useSnapshot(state);
  const modelGroupRef = useRef();
  const allMeshRefs = useRef({});
  const originalMaterials = useRef({});
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);
  const fingerPositionRef = useRef({ x: -100, y: 0 });
  const lastFrameTime = useRef(0);

  const FRAME_THROTTLE = 16; // ~60fps for performance

  // Memoize hoverable parts
  const hoverableParts = useMemo(
    () => ["Plane011", "Plane009", "METALL_2", "Bottom_Plate", "WLogoGrey"],
    []
  );

  // Load textures (keep original working texture loading)
  const fingerTexture = useTexture("/select.png");

  const textures = useTexture({
    leather1Albedo: "/var1.png",
    leather1Normal: "/totesclosed/2/Leather_normal.png",
    leather1Roughness: "/totesclosed/2/Leather_roughness.jpg",
    leather1Metallic: "/totesclosed/2/Leather_metallic.jpg",
    leather1AO: "/totesclosed/2/Leather_AO.jpg",
    leatheralpha: "/LEATHERMICROBAG.png",
    metalalpha: "/microbagxraytex/METAL_albedo.png",
    plasticAlbedo: "/totesclosed/2/Plastic_albedo.jpg",
    plasticNormal: "/totesclosed/2/Plastic_normal.png",
    plasticRoughness: "/totesclosed/2/Plastic_roughness.jpg",
    plasticMetallic: "/totesclosed/2/Plastic_metallic.jpg",
    plasticAO: "/totesclosed/2/Plastic_AO.jpg",
    plasticalpha1: "/plasticmicrou.png",
    seamsAlbedo: "/totesclosed/2/seams_albedo.jpg",
    seamsNormal: "/totesclosed/2/seams_normal.png",
    seamsRoughness: "/totesclosed/2/seams_roughness.jpg",
    seamsMetallic: "/totesclosed/2/seams_metallic.jpg",
    seamsAO: "/totesclosed/2/seams_AO.jpg",
    Mirroralpha: "/microbagxraytex/MIRROR_albedo.png",
    keysalpha: "/microbagxraytex/bunch_new_keys.png",
    usbalpha: "/microbagxraytex/usb.png",
    cardalpha: "/microbagxraytex/Card.png",
  });

  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX("/microbagxrayupdated.fbx");
  const model = useMemo(() => fbx.clone(), [fbx]);

  // Cache material mappings (keep original working mappings)
  const materialMappings = useMemo(
    () => ({
      exact: {
        "stitch": {
          color: new THREE.Color("#8F8A89"),
          opacity: 0.8,
          transparent: true,
          depthWrite: false,
        },
      },
      partial: [
        {
          key: "LEATHER",
          config: {
            color: new THREE.Color("#707A7C"),
            alphaMap: textures.leatheralpha,
            transparent: true,
            depthWrite: false,
            opacity: 0.8,
          }
        },
        {
          key: "METAL",
          config: {
            alphaMap: textures.metalalpha,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#3C3FB9"),
            opacity: 0.8,
          }
        },
        {
          key: "PLASTIC",
          config: {
            alphaMap: textures.plasticalpha1,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#A967DE"),
            opacity: 0.8,
          }
        },
        {
          key: "MIRROR",
          config: {
            alphaMap: textures.Mirroralpha,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#3C3FB9"),
            opacity: 0.8,
          }
        },
        {
          key: "keys",
          config: {
            alphaMap: textures.keysalpha,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#5D6382"),
            metalness: 1.0,
            roughness: 0.1,
            opacity: 0.8,
          }
        },
        {
          key: "seams",
          config: {
            color: new THREE.Color("#8F8A89"),
            opacity: 0.8,
            transparent: true,
            depthWrite: false,
          }
        },
        {
          key: "usb",
          config: {
            alphaMap: textures.usbalpha,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#5e649f"),
            opacity: 0.8,
          }
        },
        {
          key: "Card",
          config: {
            alphaMap: textures.cardalpha,
            transparent: true,
            depthWrite: false,
            color: new THREE.Color("#8c63b3"),
            opacity: 0.8,
          }
        }
      ]
    }),
    [textures]
  );

  // Helper function to apply material mapping (keep original working logic)
  const applyMaterialMapping = useCallback((materialName, newMaterial) => {
    if (materialMappings.exact[materialName]) {
      Object.assign(newMaterial, materialMappings.exact[materialName]);
      return true;
    }

    for (const mapping of materialMappings.partial) {
      if (materialName.includes(mapping.key)) {
        Object.assign(newMaterial, mapping.config);
        return true;
      }
    }

    return false;
  }, [materialMappings]);

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;
      return hoverableParts.some((part) => partName.includes(part));
    },
    [hoverableParts]
  );

  // Handle part hover (optimized with useCallback)
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;

      setHoveredPart(partName);

      // Use switch for better performance than multiple if-else
      switch (true) {
        case partName.includes("Plane009") || partName.includes("Plane011"):
          setTitle?.("OPEN BAG");
          sethoveredsphere?.(
            "<br><b>MAGNETIC CLASP:</b><br>The WMB MAGFRAME™ clasp is custom-crafted in Italy from Rhodoid — a sustainable cellulose-based material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a flawless fit, it closes with quiet precision and enduring strength — a refined mechanism built into every WMB bag."
          );
          break;
        case partName.includes("Bottom_Plate"):
          setTitle?.("BASE PLATE:");
          sethoveredsphere?.(
            "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB's engineered precision."
          );
          break;
        case partName.includes("Cylinder"):
          setTitle?.(" ATTACH STRAPS ™");
          sethoveredsphere?.(
            "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
          );
          break;
        case partName.includes("WLogoGrey"):
          setanimation?.(true);
          setanimationsource?.("/logo.mp4");
          setTitle?.("METAL MONOGRAM");
          sethoveredsphere?.(
            "A chromed metal plate, partially embedded into the leather and set with the WMB monogram in relief.<br><br>A restrained signature — discreet, dimensional, and unmistakable."
          );
          break;
        case partName.includes("METALL_2"):
          setanimation?.(true);
          setanimationsource?.("/side2u.mp4");
          setTitle?.("ATTACH STRAPS");
          sethoveredsphere?.(
            "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
          );
          break;
        default:
          if (partName.includes("Outer") || partName.includes("1_LEATHER") ||
            partName.includes("Middle") || partName.includes("2_LEATHER") ||
            partName.includes("Inner")) {
            sethoveredsphere?.(
              "<b>The Micro - Frame</b><br><br>Sleek and versatile, the Tote S transitions effortlessly from day to night.<br>It carries just enough — balancing function with an understated allure.<br><br>Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for lasting structure and architectural clarity.<br><br>Inside, a chromed metal mirror is discreetly housed within a dedicated compartment.<br>Engraved with the WMB monogram and revealed by a tonal leather pull tab, it reflects our approach to refined utility.<br>The opening is secured with the WMB MAGFRAME™ clasp — CNC-milled and laser-cut from Italian Rhodoid for a precise and quiet magnetic close.<br>Finished with a raised metal monogram and WMB MODULAR LOCK SYSTEM™ — our custom palladium mechanism allowing for one or two straps or chains, to carry by hand, shoulder, or crossbody.<br><br>Compact, elegant, from day to night.<br>Handcrafted in Germany.<br><br>• Dimensions: 21.1 cm x 7.6 cm x 11.5 cm<br>• Materials: Epsom Leather / Nappa lining / Palladium plated steel / Rhodoid / Chromed Mirror<br>• Strap: Adjustable leather shoulder strap and leather handle strap included<br>• Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>• Colour options: Charcoal, Burgundy, Sand, Petrol<br>• Hardware made in Italy"
            );
          }
      }
    },
    [sethoveredsphere, setActiveTab, setTitle, setanimation, setanimationsource]
  );

  // Handle part click
  const handlePartClick = useCallback(
    (partName) => {
      console.log("partname in closefbx on click", partName);

      if (partName.includes("Plane009") || partName.includes("Plane011")) {
        toggleModel();
      } else if (partName.includes("METALL_2")) {
        setActiveTab?.(2);
      }

      setShowFingerIcon(false);
    },
    [setActiveTab]
  );

  const isInactive = useInactivityDetection(4000);

  useEffect(() => {
    if (typeof sharedInactivityRef !== 'undefined') {
      sharedInactivityRef.current = isInactive;
    }
    setShowSpheres(isInactive);
  }, [isInactive]);

  // Optimized frame updates with throttling
  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Throttle frame updates for performance
    if (currentTime - lastFrameTime.current < FRAME_THROTTLE) {
      return;
    }
    lastFrameTime.current = currentTime;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      const scaleValue = 0.8 + fadeProgress * 0.2;
      modelGroupRef.current.scale.set(scaleValue, scaleValue, scaleValue);
      modelGroupRef.current.rotation.y = (1 - fadeProgress) * Math.PI * 0.08;
    }

    // Apply initial animation
    if (initialAnimationActive && ref.current) {
      const elapsedTime = Date.now() - initialAnimationStartTime;

      if (elapsedTime <= initialAnimationDuration) {
        const progress = elapsedTime / initialAnimationDuration;
        const oscillation = Math.sin(progress * Math.PI * 4) * initialRotationAmount;
        ref.current.rotation.y = oscillation;

        if (fingerIconRef.current) {
          fingerPositionRef.current.x = -oscillation * 7;
          fingerIconRef.current.position.x = fingerPositionRef.current.x;
        }
      }
    }

    // Shared rotation handling
    if (typeof sharedInactivityRef !== 'undefined' && typeof sharedRotationRef !== 'undefined') {
      if (sharedInactivityRef.current && ref.current) {
        sharedRotationRef.current -= 0.0025;
        ref.current.rotation.y = sharedRotationRef.current;
      } else if (!initialAnimationActive && ref.current && !sharedInactivityRef.current) {
        ref.current.rotation.y = sharedRotationRef.current;
      }
    }

    if (state.isInactive !== undefined) {
      state.isInactive = isInactive;
    }

    // Handle hover effects (keep original logic)
    if (!hoveredPart) {
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    } else if (hoveredPart && isHoverablePart(hoveredPart) && allMeshRefs.current[hoveredPart]) {
      const mesh = allMeshRefs.current[hoveredPart];
      if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
        if (!originalMaterials.current[hoveredPart]) {
          originalMaterials.current[hoveredPart] = mesh.material.clone();
        }

        const hoverMaterial = mesh.material.clone();

        if (hoveredPart.includes("Plane009") || hoveredPart.includes("Plane011")) {
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
        } else if (hoveredPart.includes("METALL_2") || hoveredPart.includes("Cylinder") || hoveredPart.includes("WLogoGrey")) {
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
        } else {
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
          if (originalMaterials.current[hoveredPart].color) {
            const originalColor = originalMaterials.current[hoveredPart].color.clone();
            hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
          }
        }

        hoverMaterial._isHoverMaterial = true;
        hoverMaterial.needsUpdate = true;
        mesh.material = hoverMaterial;
      }
    }
  });

  // Set up initial materials and store references to all meshes (keep original working logic)
  useEffect(() => {
    if (!model || !textures) return;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Enable frustum culling for performance
        child.frustumCulled = true;

        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Store material name for reference
        const materialName = child.material.name;

        // Create optimized material
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
        });

        // Apply material mapping with conflict resolution
        applyMaterialMapping(materialName, newMaterial);

        // Setup uv2 coordinates efficiently
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Apply environment map intensity
        newMaterial.envMapIntensity = envMapIntensity;

        // Set the new material
        child.material = newMaterial;
        child.castShadow = true;
        child.material.needsUpdate = true;
      }
    });

    setLoading?.('xray-micro', false);

    // Cleanup
    return () => {
      allMeshRefs.current = {};
      originalMaterials.current = {};
    };
  }, [model, textures, applyMaterialMapping, envMapIntensity, setLoading]);

  // Event handlers (optimized with useCallback)
  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        setHoveredPart(partName);
        handlePartHover(partName);
      }

      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = setTimeout(() => {
          if (!isMouseDownRef.current) {
            setShowSpheres(true);
          }
        }, 4000);
      }

      setShowSpheres(false);
      setShowFingerIcon(false);
    },
    [handlePartHover]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setanimation?.(false);
      setanimationsource?.(null);

      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle, setanimation, setanimationsource]
  );

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Memoized computed values
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  const fingerMaterial = useMemo(() => {
    if (!fingerTexture) {
      return new THREE.SpriteMaterial({
        color: 0xffff00,
        opacity: 0.7,
        transparent: true,
      });
    }

    return new THREE.SpriteMaterial({
      map: fingerTexture,
      transparent: true,
      opacity: 0.9,
      color: 0xffff00,
    });
  }, [fingerTexture]);

  const fingerIconPosition = useMemo(() => {
    return [position[0] + 5.5, position[1] + 4.5, position[2] + 5];
  }, [position]);

  return (
    <group
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>

      {showFingerIcon && (
        <group position={fingerIconPosition}>
          <sprite
            ref={fingerIconRef}
            material={fingerMaterial}
            scale={[1.5, 1.5, 1.5]}
          />

          <Html position={[0, -4, 0]} center>
            <div
              style={{
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px 10px",
                borderRadius: "4px",
                fontSize: "14px",
                whiteSpace: "nowrap",
                fontFamily: "Arial, sans-serif",
              }}
            >
              Drag to rotate
            </div>
          </Html>
        </group>
      )}
    </group>
  );
}



const MicroBagProduct = () => {
  const isTogglingRef = useRef(false);
  const debounceTimerRef = useRef(null);
  const isTogglingXrayRef = useRef(false);


  const { isLoading, setLoading, clearAll } = useLoadingManager();
  const [isUserAdjustingSlider, setIsUserAdjustingSlider] = useState(false);
  const snap = useSnapshot(state);
  const controlsRef = useRef();
  const lightRef = useRef();
  const fbxref = useRef();
  const [isOpen, setOpen] = useState(false);
  const [hoveredsphere, sethoveredsphere] = useState(null);
  const [xrayMode, setXrayMode] = useState(false);
  const bagGroupRef = useRef();
  const [bagRotation, setBagRotation] = useState({ x: 0, y: 0 });
  const [initialFbxRotation, setInitialFbxRotation] = useState({
    x: 0,
    y: -0.27,
    z: 0,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });
  const [showStrapVideo, setShowStrapVideo] = useState(false);
  const videoRef = useRef(null);
  const [clickcount, setClickCount] = useState(0);
  const [showAxes, setShowAxes] = useState(false);
  const [showRotationAxes, setShowRotationAxes] = useState(true);
  const [axesRadius, setAxesRadius] = useState(1);
  const [handlestraptab, sethandlestraptab] = useState(0);
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [touchDistance, setTouchDistance] = useState(null);
  const [isTwoFingerDrag, setIsTwoFingerDrag] = useState(false);
  const [lastTouchCenter, setLastTouchCenter] = useState({ x: 0, y: 0 });
  const [saturation, setSaturation] = useState(1.0);
  const [contrast, setContrast] = useState(1.0);
  const [modelOpacity, setModelOpacity] = useState(0);
  const [modelHasLoadedOnce, setModelHasLoadedOnce] = useState(false);
  const [activeTab, setActiveTab] = useState(1);
  const [title, setTitle] = useState(null);
  const [animationsource, setanimationsource] = useState(null);
  const [animation, setanimation] = useState(false);
  const [hoveredstrap, sethoveredstrap] = useState(false);
  const [strapanimation, setstrapanimation] = useState(false);
  const [showTabs, setShowTabs] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [selectedStraps, setSelectedStraps] = useState({
    shoulder: "none",
    handle: "none",
  });
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 576);
  const [selectedColor, setSelectedColor] = useState(null);
  const [lastClickedStrap, setLastClickedStrap] = useState(null);
  const [showColorStraps, setShowColorStraps] = useState(false);
  const [showShoulderColorStraps, setShowShoulderColorStraps] = useState(false);
  // Use the inactivity hook with 4 second timeout
  const isInactive = useInactivityDetection(4000);
  const [highlightData, sethighlightdata] = useState(null);
  const [lastClickedShoulderStrap, setLastClickedShoulderStrap] =
    useState(null);
  // Clear hover states when inactive
  const toggleModel = useCallback(() => {
    console.log("Toggling model state");

    if (isTogglingRef.current) return;
    isTogglingRef.current = true;

    setTimeout(() => {
      setLoading('model-toggle', true);
      state.isOpen = !state.isOpen;
      console.log("Model state is now:", state.isOpen);
      isTogglingRef.current = false;
    }, 0);
  }, [setLoading]);
  useEffect(() => {
    if (snap.isOpen) {
      // Start with opacity 0
      setModelOpacity(0);

      // Animate to full opacity
      const fadeIn = setTimeout(() => {
        setModelOpacity(1);
      }, 100);

      return () => clearTimeout(fadeIn);
    }
  }, [snap.isOpen]);
  // setLoading('strap-change', true);
  const ensureBagAboveShadowPlane = (newPosition) => {
    // Find the shadow plane Y position - your shadow plane is at -1.5
    let shadowPlaneY = -1.5; // Default value based on your plane setup

    // Search for the shadow plane in the scene if needed
    if (bagGroupRef.current && bagGroupRef.current.parent) {
      bagGroupRef.current.parent.traverse((object) => {
        if (
          object.isMesh &&
          object.material &&
          object.material.type === "ShadowMaterial"
        ) {
          shadowPlaneY = object.position.y;
        }
      });
    }

    // Calculate current zoom scale
    const zoomFactor = snap.zoomLevel / 100;
    const zoomScale = Math.pow(1.5, zoomFactor);

    // Calculate bag's bottom based on its scale and center position
    const bagHeight = 0.5 * zoomScale; // Approximate half-height of bag
    const bagBottomY = newPosition.y - bagHeight + 0.25;

    // Minimum distance to maintain above shadow plane
    const minBuffer = 0.05 * zoomScale; // Increased buffer for visibility

    return newPosition;
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const deltaX = e.clientX - lastMousePosition.x;
      const deltaY = e.clientY - lastMousePosition.y;

      if (isShiftPressed) {
        // Move the bag position with limits
        const newPosX = bagGroupRef.current.position.x + deltaX * 0.005;
        const newPosY = bagGroupRef.current.position.y - deltaY * 0.005;

        // Create position object with limits
        const newPosition = clampPosition({
          x: newPosX,
          y: newPosY,
          z: bagGroupRef.current.position.z,
        });

        // Ensure bag stays above shadow plane
        const adjustedPosition = ensureBagAboveShadowPlane(newPosition);

        // Apply the adjusted position
        bagGroupRef.current.position.x = adjustedPosition.x;
        bagGroupRef.current.position.y = adjustedPosition.y;
        bagGroupRef.current.position.z = adjustedPosition.z;
      } else {
        // Rotate the bag
        setBagRotation({
          x: bagRotation.x + deltaY * 0.005,
          y: bagRotation.y + deltaX * 0.005,
        });
      }

      setLastMousePosition({
        x: e.clientX,
        y: e.clientY,
      });
    }
  };

  // Position limits for bag movement
  const positionLimits = {
    minX: -2,
    maxX: 2,
    minY: -0.43,
    maxY: 1.5,
  };

  useEffect(() => {
    if (bagGroupRef.current) {
      bagGroupRef.current.traverse((child) => {
        if (child.isMesh && child.material) {
          const updateMaterial = (material) => {
            // Only update if the material has a map/texture
            if (material.map) {
              // Store original color if not already saved
              if (!material.userData.originalColor && material.color) {
                material.userData.originalColor = material.color.clone();
              }

              // Apply contrast and saturation via color adjustment
              if (material.userData.originalColor) {
                const originalColor = material.userData.originalColor;

                // Create a new color based on the original
                const newColor = originalColor.clone();

                // Apply contrast (moving color away from or toward gray)
                const contrastFactor = contrast;
                const gray = 0.5;
                newColor.r = Math.max(
                  0,
                  Math.min(1, gray + (newColor.r - gray) * contrastFactor)
                );
                newColor.g = Math.max(
                  0,
                  Math.min(1, gray + (newColor.g - gray) * contrastFactor)
                );
                newColor.b = Math.max(
                  0,
                  Math.min(1, gray + (newColor.b - gray) * contrastFactor)
                );

                // Apply saturation (mix between grayscale and color)
                if (saturation !== 1.0) {
                  const luminance =
                    0.299 * newColor.r +
                    0.587 * newColor.g +
                    0.114 * newColor.b;
                  newColor.r = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.r - luminance) * saturation
                    )
                  );
                  newColor.g = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.g - luminance) * saturation
                    )
                  );
                  newColor.b = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.b - luminance) * saturation
                    )
                  );
                }

                // Apply the new color
                material.color.copy(newColor);
                material.needsUpdate = true;
              }
            }
          };

          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => updateMaterial(mat));
          } else {
            updateMaterial(child.material);
          }
        }
      });
    }
  }, [saturation, contrast]);

  const handleZoomChange = useCallback(
    (value) => {
      const numericValue = Number(value);
      if (numericValue !== snap.zoomLevel) {
        // Inverting the zoom logic by using a different value range
        state.zoomLevel = numericValue;

        // Immediate slider feedback
        setIsUserAdjustingSlider(true);
        const timer = setTimeout(() => {
          setIsUserAdjustingSlider(false);
        }, 200);

        return () => clearTimeout(timer);
      }
    },
    [snap.zoomLevel]
  );

  // Refined wheel zoom handler
  const handleWheel = useCallback(
    (e) => {
      e.preventDefault();
      const zoomSpeed = 5;
      const currentZoom = snap.zoomLevel;

      // Invert the direction so scrolling down zooms out, up zooms in
      const zoomDelta = -Math.sign(e.deltaY) * zoomSpeed;

      // Constrain zoom between 130 (min) and 230 (max) instead of -25 and 185
      const newZoom = Math.max(Math.min(230, currentZoom + zoomDelta), 130);

      if (newZoom !== currentZoom) {
        state.zoomLevel = newZoom;
        setIsUserAdjustingSlider(true);

        const timer = setTimeout(() => {
          setIsUserAdjustingSlider(false);
        }, 200);

        return () => clearTimeout(timer);
      }
    },
    [snap.zoomLevel]
  );

  // Clamps position within the defined limits
  const clampPosition = (position) => {
    return {
      x: Math.max(
        positionLimits.minX,
        Math.min(positionLimits.maxX, position.x)
      ),
      y: Math.max(
        positionLimits.minY,
        Math.min(positionLimits.maxY, position.y)
      ),
      z: position.z,
    };
  };

  // Mouse interaction handlers
  const handleMouseDown = (e) => {
    e.preventDefault(); // Preventem default behavior
    setIsDragging(true);
    setLastMousePosition({
      x: e.clientX,
      y: e.clientY,
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Calculate touch center point
  const getTouchCenter = (touches) => {
    if (touches.length === 2) {
      return {
        x: (touches[0].clientX + touches[1].clientX) / 2,
        y: (touches[0].clientY + touches[1].clientY) / 2,
      };
    }
    return { x: touches[0].clientX, y: touches[0].clientY };
  };

  // Touch event handlers for mobile with two-finger bag movement
  const handleTouchStart = (e) => {
    e.preventDefault(); // Prevent default behavior

    if (e.touches.length === 2) {
      // Two finger interaction - can be either zoom or movement
      const dx = e.touches[0].clientX - e.touches[1].clientX;
      const dy = e.touches[0].clientY - e.touches[1].clientY;
      setTouchDistance(Math.sqrt(dx * dx + dy * dy));

      // Track the center point between the two fingers for potential bag movement
      const center = getTouchCenter(e.touches);
      setLastTouchCenter(center);
      setIsTwoFingerDrag(true);

      // Disable OrbitControls when doing two-finger bag movement
      if (controlsRef.current) {
        controlsRef.current.enabled = false;
      }
    } else if (e.touches.length === 1) {
      // Single finger will rotate the bag (existing functionality)
      setIsDragging(true);
      setIsTwoFingerDrag(false);
      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });
    }
  };

  const handleTouchMove = (e) => {
    e.preventDefault(); // Prevent scrolling while interacting with the model

    if (e.touches.length === 2 && isTwoFingerDrag) {
      const currentCenter = getTouchCenter(e.touches);

      // Calculate current finger distance for potential pinch zoom
      const dx = e.touches[0].clientX - e.touches[1].clientX;
      const dy = e.touches[0].clientY - e.touches[1].clientY;
      const currentDistance = Math.sqrt(dx * dx + dy * dy);

      // Check if this is primarily a zoom gesture or a movement gesture
      if (touchDistance !== null) {
        const distanceDelta = Math.abs(currentDistance - touchDistance);
        const centerDelta = Math.sqrt(
          Math.pow(currentCenter.x - lastTouchCenter.x, 2) +
          Math.pow(currentCenter.y - lastTouchCenter.y, 2)
        );

        // If the distance between fingers changed significantly more than the center position,
        // treat as a zoom gesture
        if (distanceDelta > centerDelta * 1.5) {
          const delta = currentDistance - touchDistance;

          // Apply a sensitivity multiplier
          const zoomSensitivity = 0.2;
          const zoomDelta = delta * zoomSensitivity;

          // Match zoom limits with your slider range (130-230)
          const newZoom = Math.max(
            130,
            Math.min(230, snap.zoomLevel + zoomDelta)
          );

          if (newZoom !== snap.zoomLevel) {
            state.zoomLevel = newZoom;
            setIsUserAdjustingSlider(true);

            const timer = setTimeout(() => {
              setIsUserAdjustingSlider(false);
            }, 200);
          }
        } else {
          // Otherwise, treat as a movement gesture
          const deltaX = currentCenter.x - lastTouchCenter.x;
          const deltaY = currentCenter.y - lastTouchCenter.y;

          // Move the bag position
          if (bagGroupRef.current) {
            const newPosX = bagGroupRef.current.position.x + deltaX * 0.01;
            const newPosY = bagGroupRef.current.position.y - deltaY * 0.01;

            // Apply position limits
            const newPosition = clampPosition({
              x: newPosX,
              y: newPosY,
              z: bagGroupRef.current.position.z,
            });

            // Ensure bag stays above shadow plane
            const adjustedPosition = ensureBagAboveShadowPlane(newPosition);

            // Apply the adjusted position
            bagGroupRef.current.position.x = adjustedPosition.x;
            bagGroupRef.current.position.y = adjustedPosition.y;
            bagGroupRef.current.position.z = adjustedPosition.z;
          }
        }
      }

      // Update touch tracking state
      setTouchDistance(currentDistance);
      setLastTouchCenter(currentCenter);
    } else if (e.touches.length === 1 && isDragging) {
      // Single finger rotation (existing functionality)
      const deltaX = e.touches[0].clientX - lastMousePosition.x;
      const deltaY = e.touches[0].clientY - lastMousePosition.y;

      // Rotate the bag
      setBagRotation({
        x: bagRotation.x + deltaY * 0.005,
        y: bagRotation.y + deltaX * 0.005,
      });

      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });
    }
  };

  const handleTouchEnd = (e) => {
    // Reset all touch interactions if no fingers are left on screen
    if (e.touches.length === 0) {
      setIsDragging(false);
      setIsTwoFingerDrag(false);
      setTouchDistance(null);

      // Re-enable OrbitControls when not interacting
      if (controlsRef.current) {
        controlsRef.current.enabled = true;
      }
    }
    // If we still have one finger, maintain single-finger rotation
    else if (e.touches.length === 1) {
      setIsTwoFingerDrag(false);
      setIsDragging(true);
      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });

      // Re-enable OrbitControls when switching to single finger
      if (controlsRef.current) {
        controlsRef.current.enabled = true;
      }
    }
  };

  // Reset bag position button handler
  const resetBagPosition = () => {
    if (bagGroupRef.current) {
      // Find the shadow plane's Y position
      let shadowPlaneY = -1.5; // Default value

      // Search for shadow plane
      if (bagGroupRef.current.parent) {
        bagGroupRef.current.parent.traverse((object) => {
          if (
            object.isMesh &&
            object.material &&
            object.material.type === "ShadowMaterial"
          ) {
            shadowPlaneY = object.position.y;
          }
        });
      }

      // Calculate current zoom scale
      const zoomFactor = snap.zoomLevel / 100;
      const zoomScale = Math.pow(1.5, zoomFactor);

      // Calculate minimum y position to stay above shadow plane
      const bagHeight = 0.5 * zoomScale; // Approximate half-height of bag
      const minBuffer = 0.01 * zoomScale;
      const minY = shadowPlaneY + minBuffer + bagHeight;

      // Reset bag position while respecting the shadow plane constraint
      bagGroupRef.current.position.x = 0;
      bagGroupRef.current.position.y = Math.max(0, minY);
      bagGroupRef.current.position.z = 0;
      setBagRotation({ x: 0, y: 0 });
    }
  };

  const attachmentPoints = [{ name: "Cylinder002", position: [0, 0, 0] }];

  const handleColorChange = (newColors) => {
    console.log("New colors: for the fbx", newColors);
    state.colors = newColors;
  };

  // For handle straps (keep existing function name)
  const handlestrapcolorchange = (strapcolor) => {
    setClickCount(clickcount + 1);
    if (strapcolor === "beige") {
      state.strapcolors.leather = "#D7CCBF";
      state.strapcolors.plastic = "#F2EEE8";
      state.strapcolors.seams = "#D0C6BB";
    }
    if (strapcolor === "black") {
      state.strapcolors.leather = "#232424";
      state.strapcolors.plastic = "#060606";
      state.strapcolors.seams = "#1B1B1C";
    }
    if (strapcolor === "red") {
      state.strapcolors.plastic = "#7A3E45";
      state.strapcolors.leather = "#680D18";
      state.strapcolors.seams = "#671E23";
    }
    if (strapcolor === "grey") {
      state.strapcolors.leather = "#6A7075";
      state.strapcolors.plastic = "#A1A7AC";
      state.strapcolors.seams = "#505457";
    }
  };

  const handlestrap2colorchange = (strapcolor) => {
    setClickCount(clickcount + 1);
    // console.error("setting the color of the shoulder strap",state.shoulderStrapColors);
    if (strapcolor === "beige") {
      state.shoulderStrapColors.leather = "#D7CCBF";
      state.shoulderStrapColors.plastic = "#F2EEE8";
      state.shoulderStrapColors.seams = "#D0C6BB";
    }
    if (strapcolor === "black") {
      state.shoulderStrapColors.leather = "#232424";
      state.shoulderStrapColors.plastic = "#060606";
      state.shoulderStrapColors.seams = "#1B1B1C";
    }
    if (strapcolor === "red") {
      state.shoulderStrapColors.plastic = "#7A3E45";
      state.shoulderStrapColors.leather = "#680D18";
      state.shoulderStrapColors.seams = "#671E23";
    }
    if (strapcolor === "grey") {
      state.shoulderStrapColors.leather = "#6A7075";
      state.shoulderStrapColors.plastic = "#2E2E2E";
      state.shoulderStrapColors.seams = "#505457";
    }
  };


  const handlestrapopacity = (opacity) => {
    state.strapOpacity = opacity;
    console.log("strap opacity", state.strapOpacity);
  };

  const handleStrapChange = useCallback((strapType) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      setLoading('strap-change', true);
      state.currentStrap = strapType;
    }, 100);
  }, [setLoading]); // Remove setLoading from dependencies
  const handleXrayToggle = useCallback(() => {
    if (isTogglingXrayRef.current) return;
    isTogglingXrayRef.current = true;

    setTimeout(() => {
      setLoading('xray-toggle', true);
      setXrayMode(!xrayMode);
      isTogglingXrayRef.current = false;
    }, 0);
  }, [xrayMode, setLoading]); // Remove setLoading from dependencies

  // Add both mouse and touch event listeners
  useEffect(() => {
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("touchend", handleTouchEnd);
    window.addEventListener("touchmove", handleTouchMove, { passive: false });

    return () => {
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("touchend", handleTouchEnd);
      window.removeEventListener("touchmove", handleTouchMove);
    };
  }, [
    isDragging,
    isTwoFingerDrag,
    lastMousePosition,
    lastTouchCenter,
    bagRotation,
    touchDistance,
  ]);

  useEffect(() => {
    if (snap.isOpen) {
      // Once the model has been loaded/opened at least once
      setModelHasLoadedOnce(true);
    }
  }, [snap.isOpen]);

  useEffect(() => {
    const canvasContainer = document.querySelector(
      `.${styles.canvasContainer}`
    );
    if (canvasContainer) {
      canvasContainer.addEventListener("wheel", handleWheel, {
        passive: false,
      });
      return () => {
        canvasContainer.removeEventListener("wheel", handleWheel);
      };
    }
  }, [handleWheel]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Shift") {
        setIsShiftPressed(true);
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === "Shift") {
        setIsShiftPressed(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 576);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Then use isMobile state to calculate thumb position
  const trackHeight = isMobile ? 208 : 288;
  const thumbOffset = isMobile ? 104 : 144;
  const thumbPosition = -(
    ((snap.zoomLevel - 130) / 100) * trackHeight -
    thumbOffset
  );

  const resetStrapTab = () => {
    sethandlestraptab(0); // Resets the strap tab to 0
  };

  const toggleTabManagerVisibility = () => {
    setShowTabs(!showTabs);
  };
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const openPopup = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  return (
    <>
      {/* {state.isLoading && (
        <div className={styles.loadingOverlay}>
          <img src="/loader.gif" alt="Loading..." />
        </div>
      )} */}

      {/* {isLoading && (
        // <div className={styles.loadingOverlay}>
          <Loaders redirectDelay={2000} />
        // </div>
      )} */}

      <div className={styles.container}>
        <div
          className={styles.canvasContainer}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          style={{
            cursor: isDragging ? "grabbing" : "grab",
            touchAction: "none",
          }}
        >
          <Canvas
            shadows
            gl={{
              physicallyCorrectLights: true,
              outputEncoding: THREE.sRGBEncoding,
              toneMapping: THREE.ACESFilmicToneMapping,
              toneMappingExposure: 0.4,
              shadowMap: {
                enabled: true,
                type: THREE.PCFSoftShadowMap,
              },
              antialias: true,
              // antialias: false,
              // powerPreference: "high-performance",
              // logarithmicDepthBuffer: true
            }}
          // dpr={Math.min(window.devicePixelRatio, 2)}
          >
            <Suspense fallback={null}>
              {/* Environment lighting from EXR */}
              <Environment
                preset="studio"
                background={false}
                intensity={0.05}
                opacity={0.1}
                envMapIntensity={0.5}
              />

              {/* Additional lights for better visualization */}
              <directionalLight
                ref={lightRef}
                position={[2.5, 1.3, 1]}
                intensity={1}
                castShadow
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
                shadow-bias={-0.0005}
                shadow-radius={2}
              />
              <directionalLight
                position={[4.6, 0.5, 2]}
                intensity={0.11}
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
                shadow-bias={-0.0005}
                shadow-radius={2}
              />
              {/* bottom light */}
              <directionalLight
                ref={lightRef}
                position={[0.2, -32, 1]}
                intensity={2}
              />
              <directionalLight
                ref={lightRef}
                position={[0.2, +5, 0]}
                intensity={2}
              />
              <mesh
                receiveShadow
                position={[0, -1.2, 0]}
                rotation={[-Math.PI / 2, 0, 0]}
              >
                <planeGeometry args={[60, 60]} />
                <shadowMaterial
                  color="#878787"
                  transparent={true}
                  opacity={0.7}
                />
              </mesh>

              <group
                ref={bagGroupRef}
                rotation={[
                  bagRotation.x,
                  bagRotation.y + initialFbxRotation.y,
                  snap.isOpen ? initialFbxRotation.z : 0,
                ]}
              >
                {!xrayMode ? (
                !snap.isOpen ? (
                  <FBXModelWithTexturesopenh
                    position={[0, 0, 0]}
                    scale={snap.scale}
                    setOpen={setOpen}
                    sethandlestraptab={sethandlestraptab}
                    sethoveredsphere={sethoveredsphere}
                    xrayMode={xrayMode}
                    setXrayMode={setXrayMode}
                    castShadow
                    receiveShadow
                    ref={fbxref}
                    opacity={modelOpacity}
                    setActiveTab={setActiveTab}
                    setTitle={setTitle}
                    setanimationsource={setanimationsource}
                    setanimation={setanimation}
                    isInactive={isInactive}
                    showInfo={showInfo}
                    setLoading={setLoading}
                    setShowTabs={setShowTabs}
                    showTabs={showTabs}
                  />
                ) : (
                  <FBXModelWithTexturesclosedh
                    position={[0, 0, 0]}
                    scale={snap.scale}
                    setOpen={setOpen}
                    sethandlestraptab={sethandlestraptab}
                    sethoveredsphere={sethoveredsphere}
                    xrayMode={xrayMode}
                    setXrayMode={setXrayMode}
                    castShadow
                    receiveShadow
                    ref={fbxref}
                    opacity={modelOpacity}
                    skipInitialAnimation={modelHasLoadedOnce}
                    key="closed-model"
                    oscillation_count={oscillation_count}
                    setActiveTab={setActiveTab}
                    setTitle={setTitle}
                    setanimationsource={setanimationsource}
                    setanimation={setanimation}
                    isInactive={isInactive}
                    sethighlightdata={sethighlightdata}
                    setLoading={setLoading}
                    showInfo={showInfo}
                    isLoading={isLoading}
                    setShowTabs={setShowTabs}
                    showTabs={showTabs}
                  />
                )
              ) : (
                <FBXModelWithTexturesclosedd
                  position={[0, 0, 0]}
                  rotation={[0, 2, 0]}
                  scale={snap.scale}
                  setShowRotationAxes={setShowRotationAxes}
                  setOpen={setOpen}
                  setActiveTab={setActiveTab}
                  sethoveredsphere={sethoveredsphere}
                  setTitle={setTitle}
                  xrayMode={xrayMode}
                  setXrayMode={setXrayMode}
                  castShadow
                  receiveShadow
                  ref={fbxref}
                  opacity={modelOpacity}
                  // skipInitialAnimation={modelHasLoadedOnce}
                  key="closed-model"
                  // oscillation_count={oscillation_count}
                  setanimationsource={setanimationsource}
                  setanimation={setanimation}
                  setLoading={setLoading}
                  setShowTabs={setShowTabs}
                  showTabs={showTabs}
                />
              )}

                <StrapWithTexturesnew
                  type={snap.currentStrap}
                  attachmentPoints={attachmentPoints}
                  clickcount={clickcount}
                  sethoveredsphere={sethoveredsphere}
                  castShadow
                  receiveShadow
                  isInactive={isInactive}
                  xrayMode={xrayMode}
                  selectedStraps={selectedStraps}
                  setLoading={setLoading}

                />
                {/* <StrapWithTexturesnew
                  type={snap.currentStrap}
                  attachmentPoints={attachmentPoints}
                  clickcount={clickcount}
                  sethoveredsphere={sethoveredsphere}
                  castShadow
                  receiveShadow
                  isInactive={isInactive}
                /> */}
              </group>
              <CameraZoom bagGroupRef={bagGroupRef} isDragging={isDragging} />
            </Suspense>
          </Canvas>
        </div>
        {showTabs && (
          <div className={styles.slider}>
            <div className={styles.verticalZoomSlider}>
              <p className={styles.zoomPercentage}>
                {`${Math.round(snap.zoomLevel - 130)} %`}
              </p>

              <div className={styles.sliderContainer}>
                <FaPlus size={14} className={styles.plusIcon} />

                <div className={styles.sliderTrackContainer}>
                  <div className={styles.sliderTrack}></div>

                  <input
                    type="range"
                    min="130"
                    max="230"
                    value={-(snap.zoomLevel - 185)}
                    onChange={(e) => handleZoomChange(Number(e.target.value))}
                    className={styles.sliderInput}
                    aria-label="Zoom level"
                  />
                  <div
                    className={styles.sliderThumb}
                    style={{ transform: `translateY(${thumbPosition}px)` }}
                  />
                </div>

                <FaMinus size={14} className={styles.minusIcon} />
              </div>
            </div>

            {/* <div className={styles.controlButtons}>
            Control buttons remain unchanged
          </div> */}
          </div>
        )}
      </div>
      {showTabs && (
        <div className={styles.controlBtnDivLeft}>
          <button
            className={styles.resetPositionBtn}
            onClick={toggleTabManagerVisibility}
          >
            Show/Hide
          </button>
          <button
            className={styles.xreyBtn}
            onClick={handleXrayToggle}
          >
            Xray
          </button>
        </div>
      )}
      {!showTabs && (
        <button
          className={styles.controlBtn1}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button>
      )}
      <div className={styles.controlBtnDiv}>
        {showTabs && (
          <>
            {/* <div>
              <button
                className={styles.resetPositionBtn}
                onClick={resetBagPosition}
              >
                Reset
              </button>
            </div>

            <div>
              <button
                className={styles.xreyBtn}
                onClick={() => {
                  setXrayMode(!xrayMode);
                }}
              >
                Xray
              </button>
            </div> */}

            <div className={styles.tooltipContainer}>
              <button
                onClick={() => setShowInfo(!showInfo)}
                className={`${styles.controlBtn} ${styles.customTooltip}`}
                data-tooltip="Hover over objects for details"
              >
                {showInfo ? "Hide Info" : "Show Info"}
              </button>
            </div>
            <div>
              <button className={styles.controlBtn} onClick={openPopup}>
                Product Images
              </button>
            </div>
          </>
        )}
        {/* <button
          className={styles.controlBtn}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button> */}
      </div>
      <div className={styles.showBtnDiv}>
        {showTabs && (
          <>
            <div>
              <button
                className={styles.resetStrapBtn}
                onClick={() => {
                  // Determine which strap to remove based on priority
                  if (selectedStraps.handle !== "none") {
                    // Remove handle strap first
                    handleStrapChange("none");
                    setSelectedStraps((prev) => ({ ...prev, handle: "none" }));
                    setLastClickedStrap(null);
                  } else if (selectedStraps.shoulder !== "none") {
                    // Remove shoulder strap if handle is already removed
                    setSelectedStraps((prev) => ({
                      ...prev,
                      shoulder: "none",
                    }));
                    setLastClickedShoulderStrap(null);
                  } else {
                    setLastClickedStrap(null);
                    setLastClickedShoulderStrap(null);
                    handleStrapChange("none");
                    setSelectedStraps({ shoulder: "none", handle: "none" });
                  }

                  // Reset color strap views
                  setShowColorStraps(false);
                  setShowShoulderColorStraps(false);
                }}
              >
                {(() => {
                  if (selectedStraps.handle !== "none") {
                    return "Remove handle strap";
                  } else if (selectedStraps.shoulder !== "none") {
                    return "Remove shoulder strap";
                  } else {
                    return "Remove strap";
                  }
                })()}
              </button>
            </div>
            <div>
              <button
                onClick={() => setShowInfo(!showInfo)}
                className={styles.controlBtn}
              >
                {showInfo ? "Hide Info" : "Show Info"}
              </button>
            </div>
            <div>
              <button className={styles.controlBtn} onClick={openPopup}>
                Product Images
              </button>
            </div>
          </>
        )}
        {/* <button
          className={styles.controlBtn}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button> */}
      </div>
      {showInfo && hoveredsphere && (
        <div className={styles.detailsPanelCon}>
          <div className={styles.detailsPanel}>
            <button
              className={styles.closeButton}
              onClick={() => {
                sethoveredsphere?.(null);
                // setShowInfo(!showInfo);
              }}
            >
              &times;
            </button>
            <h3 dangerouslySetInnerHTML={{ __html: title }}></h3>
            <p dangerouslySetInnerHTML={{ __html: hoveredsphere }} />
            {animation && (
              <div className={styles.videoContainer}>
                <video
                  ref={videoRef}
                  className={styles.strapVideo}
                  width="100%"
                  autoPlay={true}
                  loop
                  muted
                  style={{ opacity: 0.7 }}
                >
                  <source src={animationsource} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
            )}
          </div>
        </div>
      )}
      {/* {console.error('highlightData:', highlightData)} */}
      {highlightData && (
        <div className={styles.detailsPanell}>
          <p dangerouslySetInnerHTML={{ __html: highlightData }} />
        </div>
      )}

      {/* <TextPopUp
        onClose={closePopup}
        title={"PULL OUT MIRROR"}
        hoveredsphere={
          "A modern essential. This chromed metal mirror, engraved with the WMB monogram, is concealed within the bag's architecture. Revealed by a tonal leather tab, it reflects our focus on thoughtful utility and considered form."
        }
        animation={""}
        videoRef={videoRef}
      /> */}

      {/* <div className={styles.detailsPanel}>
          {/* <p>Debug: {hoveredsphere}</p>  */}
      {/* {strap && (
            <div className={styles.videoContainer}>
              <video
                ref={videoRef}
                className={styles.strapVideo}
                width="50%"
                autoPlay={true}
                loop
                muted
              >
                <source src={animationsource} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          )} */}

      {showTabs && (
        <TabManager
          onColorChange={handleColorChange}
          onStrapChange={handleStrapChange}
          handlestrapcolorchange={handlestrapcolorchange}
          handlestrap2colorchange={handlestrap2colorchange}
          strapopacity={handlestrapopacity}
          handlestraptab={handlestraptab}
          sethandlestraptab={sethandlestraptab}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          setanimation={setanimation}
          setanimationsource={setanimationsource}
          sethoveredsphere={sethoveredsphere}
          setstrapanimation={setstrapanimation}
          setSelectedStraps={setSelectedStraps}
          selectedStraps={selectedStraps}
          setLastClickedStrap={setLastClickedStrap}
          lastClickedStrap={lastClickedStrap}
          setLastClickedShoulderStrap={setLastClickedShoulderStrap}
          xrayMode={xrayMode}
        />
      )}
      {isPopupOpen && <ImgsPopUp name={"micro"} onClose={closePopup} />}
      <ModalVideo
        channel="youtube"
        youtube={{ autoplay: 0 }}
        isOpen={isOpen}
        videoId="L61p2uyiMSo"
        onClose={() => setOpen(false)}
      />
    </>
  );
};

export default MicroBagProduct;
