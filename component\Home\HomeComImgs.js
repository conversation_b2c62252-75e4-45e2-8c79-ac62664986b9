import React, { useState, useEffect, useCallback } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";
import Loaders from "../Loader/Loaders";
import style from "./HomeComImgs.module.scss";
import Link from "next/link";

// Move static data outside component (performance optimization)
const MODELS = [
  {
    id: "micro",
    name: "Micro - Frame",
    baseImagePath: "/homebag/micro1.png",
    mobileImagePath: "/homebag/micromobile.webp",
    hoverImgePath: "/homebag/micro2.png",
    link: "/microbag",
  },
  {
    id: "cell",
    name: "Cell - Frame",
    baseImagePath: "/homebag/pouch1.png",
    mobileImagePath: "/homebag/pouchmobile.webp",
    hoverImgePath: "/homebag/pouch2.png",
    link: "/phonepouch",
  },
  {
    id: "mono",
    name: "Mono - Frame",
    baseImagePath: "/homebag/totes1.png",
    mobileImagePath: "/homebag/totesmobile.webp",
    hoverImgePath: "/homebag/totes2.png",
    link: "/totes",
  },
  {
    id: "hunar",
    name: "<PERSON><PERSON> - Frame",
    baseImagePath: "/homebag/panier1.png",
    mobileImagePath: "/homebag/paniermobile.webp",
    hoverImgePath: "/homebag/panier2.png",
    link: "/panierbag",
  },
  {
    id: "parcel",
    name: "Parcel - Frame",
    baseImagePath: "/homebag/totem1.png",
    mobileImagePath: "/homebag/totemmobile.webp",
    hoverImgePath: "/homebag/totem2.png",
    link: "/totem",
  },
  {
    id: "maxi",
    name: "Maxi - Frame",
    baseImagePath: "/homebag/totel1.png",
    mobileImagePath: "/homebag/totelmobile.webp",
    hoverImgePath: "/homebag/totel1.png",
    link: "/totel",
  },
];

const LOADER_ROUTES = {
  micro: "/microbag",
  cell: "/phonepouch",
  mono: "/totes",
  hunar: "/panierbag",
  parcel: "/totem",
  maxi: "/totel",
};

const HomeComImgs = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [currentModelIndex, setCurrentModelIndex] = useState(0);
  const [activeLoader, setActiveLoader] = useState(null);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  // Preload critical images for LCP optimization
  useEffect(() => {
    // Preload the first image (LCP image)
    const lcpImg = new window.Image();
    lcpImg.src = MODELS[0].baseImagePath;

    // Preload all hover images for smooth transitions
    MODELS.forEach((model) => {
      const img = new window.Image();
      img.src = model.hoverImgePath;
    });
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const navigateModel = useCallback((direction) => {
    if (direction === "next") {
      setCurrentModelIndex((prev) =>
        prev === MODELS.length - 1 ? 0 : prev + 1
      );
    } else {
      setCurrentModelIndex((prev) =>
        prev === 0 ? MODELS.length - 1 : prev - 1
      );
    }
  }, []);

  const currentModel = MODELS[currentModelIndex];

  const handleImageClick = useCallback((modelId) => {
    setActiveLoader(modelId);
  }, []);

  const handleMouseEnter = useCallback(
    (index) => {
      if (!isMobile) {
        setHoveredIndex(index);
      }
    },
    [isMobile]
  );

  const handleMouseLeave = useCallback(() => {
    if (!isMobile) {
      setHoveredIndex(null);
    }
  }, [isMobile]);

  return (
    <>
      {/* Simplified loader management */}
      {activeLoader && (
        <Loaders
          redirectTo={LOADER_ROUTES[activeLoader]}
          redirectDelay={4000}
        />
      )}

      {isMobile ? (
        <div className={style.mobileSlider}>
          <div className={style.mobileCanvasContainer}>
            {/* <a
              href={currentModel.link}
              onClick={() => handleImageClick(currentModel.id)}
              style={{ textAlign: "center" }}
            > */}
              {/* Back to original img tag - keeps your exact CSS */}
              {/* <img
                src={currentModel.mobileImagePath}
                alt={currentModel.name}
                className={style.productImage1}
              />
            </a> */}
            <Link
              prefetch
              href={currentModel.link}
              onClick={() => handleImageClick(currentModel.id)}
              style={{ textAlign: "center" }}
            >
              {/* Back to original img tag - keeps your exact CSS */}
              <img
                src={currentModel.mobileImagePath}
                alt={currentModel.name}
                className={style.productImage1}
              />
            </Link>
          </div>
          <div className={style.mobileControls}>
            <button
              className={style.controlButton}
              onClick={() => navigateModel("prev")}
              aria-label="Previous product"
            >
              <FaArrowLeft color="#b9b9b9" size={15} />
            </button>
            <div
              className={style.modelName}
              onClick={() => handleImageClick(currentModel.id)}
            >
              {currentModel.name}
            </div>
            <button
              className={style.controlButton}
              onClick={() => navigateModel("next")}
              aria-label="Next product"
            >
              <FaArrowRight color="#b9b9b9" size={15} />
            </button>
          </div>
        </div>
      ) : (
        <div className={style.container}>
          {MODELS.map((model, index) => (
            <div key={model.id} className={style.canvasContainer}>
              <div
                onClick={() => handleImageClick(model.id)}
                className={style.mainCon}
                onMouseEnter={() => handleMouseEnter(index)}
                onMouseLeave={handleMouseLeave}
              >
                {/* Back to original img tag - keeps your exact CSS */}
                <img
                  src={
                    hoveredIndex === index
                      ? model.hoverImgePath
                      : model.baseImagePath
                  }
                  alt={model.name}
                  className={style.productImage}
                />
                <p>{model.name}</p>
              </div>
            </div>
          ))}
          <div className={style.line}></div>
        </div>
      )}
    </>
  );
};

export default HomeComImgs;
