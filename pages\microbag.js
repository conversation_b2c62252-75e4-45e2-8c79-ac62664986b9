import Header from "@/component/Header/Header";
// import MicroBagProduct from "@/component/SingleProduct/MicroBagProduct";
import React from "react";

import dynamic from "next/dynamic";

const MicroBagProduct = dynamic(
  () => {
    return import("@/component/SingleProduct/MicroBagProduct");
  },
  { ssr: false }
);

const microbag = () => {
  return (
    <>
      <Header />
      <MicroBagProduct />
    </>
  );
};

export default microbag;
