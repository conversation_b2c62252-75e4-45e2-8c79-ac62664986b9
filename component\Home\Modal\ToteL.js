import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { proxy, useSnapshot } from "valtio";
import { Text } from "@react-three/drei";

function ToteL({ position, scale }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/totelopencomp.glb");

  const bagName = "Tote L";

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });

  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        dispose={null}
        // onClick={() => {
        //   window.location.href = "/totel";
        // }}
      >
        <mesh
          geometry={nodes.Cylinder002.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Outer_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Plane009.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object003.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
        <mesh
          geometry={nodes.Shape009.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Plane013.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder004.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder005.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder006.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder007.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder008.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder009.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder013.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder014.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder015.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Plane014.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape010.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape011.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape012.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer001.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer002.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Plane015.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Box006.geometry} material={materials.Material} />
        <mesh
          geometry={nodes["default"].geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Torus001.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Plane016.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer003.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape013.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape014.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape015.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder016.geometry}
          material={materials.Material}
        />
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={3.9}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}

export default ToteL;
useGLTF.preload("/totelopencomp.glb");
