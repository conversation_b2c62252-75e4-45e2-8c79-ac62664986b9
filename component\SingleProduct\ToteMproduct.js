import React, {
  useRef,
  useState,
  Suspense,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { <PERSON><PERSON>, useThree, use<PERSON>rame, useLoader } from "@react-three/fiber";
import {
  useGLTF,
  useFBX,
  Environment,
  OrbitControls,
  useTexture,
  Html,
} from "@react-three/drei";
import { proxy, useSnapshot } from "valtio";
import "rc-slider/assets/index.css";
import styles from "./SingleProduct.module.scss";
import { FaPlus, FaMinus } from "react-icons/fa";
import * as THREE from "three";
import ModalVideo from "react-modal-video";
import TabmanagerTotem from "../Tabmanager/TabmanagerTotem";
import { DirectionalLightHelper, SpotLightHelper } from "three";
import { useInactivityDetection } from "../Home/Hooks/inactivity";
import ImgsPopUp from "../ImgsPopUp/ImgsPopUp";
import Loaders from "../Loader/Loaders";
const createLoadingManager = () => {
  const loadingStates = new Map();
  const listeners = new Set();
  
  // Add initial loading state
  loadingStates.set('initial', true);
  
  const manager = {
    setLoading: (key, isLoading, timeout = 10000) => {
      if (isLoading) {
        loadingStates.set(key, true);
        console.log(`🔄 Loading started: ${key}`);
        
        // Auto-timeout to prevent infinite loading
        setTimeout(() => {
          if (loadingStates.has(key)) {
            console.warn(`⏰ Loading timeout for ${key}, forcing completion`);
            manager.setLoading(key, false);
          }
        }, timeout);
      } else {
        loadingStates.delete(key);
        console.log(`✅ Loading completed: ${key}`);
      }
      
      // Notify all listeners
      const isAnyLoading = loadingStates.size > 0;
      listeners.forEach(callback => callback(isAnyLoading, key));
    },
    
    subscribe: (callback) => {
      listeners.add(callback);
      return () => listeners.delete(callback);
    },
    
    isLoading: () => loadingStates.size > 0,
    getActiveLoaders: () => Array.from(loadingStates.keys()),
    clear: () => {
      loadingStates.clear();
      listeners.forEach(callback => callback(false, 'clear'));
    }
  };
  
  return manager;
};

const loadingManager = createLoadingManager();

// Loading hook
const useLoadingManager = () => {
  const [isLoading, setIsLoading] = useState(loadingManager.isLoading());
  const [activeLoaders, setActiveLoaders] = useState([]);
  
  useEffect(() => {
    const unsubscribe = loadingManager.subscribe((loading, key) => {
      // Add debouncing to prevent rapid state changes
      const timeoutId = setTimeout(() => {
        setIsLoading(loading);
        setActiveLoaders(loadingManager.getActiveLoaders());
      }, 0);
      
      return () => clearTimeout(timeoutId);
    });
    
    return unsubscribe;
  }, []); // Remove dependencies to prevent re-subscribing
  
  return {
    isLoading,
    activeLoaders,
    setLoading: useCallback((key, loading, timeout) => {
      loadingManager.setLoading(key, loading, timeout);
    }, []),
    clearAll: useCallback(() => {
      loadingManager.clear();
    }, [])
  };
};
// import { useInactivityDetection } from "../Home/Hooks/inactivity";
const DEFAULT_SCALE = 0.15;

// Strap configurations
const STRAP_TYPES = {
  NONE: {
    id: "none",
    name: "No Strap",
    modelPath: null,
  },
  BALL: {
    id: "ball",
    name: "Ball Strap",
    modelPath: "/ballstrapmicrocomp.glb",
    scale: DEFAULT_SCALE, // Use the default scale
    rotation: [0, 0, 0],
    position: [0, -0.5, 0], // Adjust these values to align the strap
  },
  HANDLE: {
    id: "handle",
    name: "Handle Strap",
    modelPath: "/totemhandle.glb",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE, // Use the default scale
  },
  METAL: {
    id: "metal",
    name: "Metal Chain",
    modelPath: "/chain1microcomp.glb",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE, // Use the default scale
  },
};

// State management
const state = proxy({
  currentStrap: STRAP_TYPES.NONE.id,
  zoomedPart: null,
  scale: DEFAULT_SCALE,
  zoomLevel: 40,
  isOpen: true, // Add state to track if the model is open or closed
  colors: {
    plastic: "#E1D8CF",
    leather: "#C7B8A9",
    seams: "#D0C6BB", // 30% lighter than original,
  },
  strapcolors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF",
    seams: "#D0C6BB", // 30% lighter than original,
  },
    handleStrapColors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF", 
    seams: "#D0C6BB",
  },
  
  shoulderStrapColors: {
    plastic: "#F4EBE2",
    leather: "#D7CCBF",
    seams: "#D0C6BB", 
  },
  strapClicks: {}, // Track clicks for each strap
  strapOpacity: 1, // Default opacity
  strapColor: null,
  oscillation_count: 3,
  inactivityRotation: 0,
  isInactive: false,
  isLoading: true, // Track loading state
});
const sharedRotationRef = { current: 0 };
const sharedInactivityRef = { current: false };

function StrapWithTextures({
  type,
  attachmentPoints,
  clickcount,
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  envMapIntensity = 1,
  fadeInDuration = 1000,
  xrayMode = false,
  selectedStraps,
    setLoading
}) {
  console.log("StrapWithTextures rendered with type:", type);
  const ref = useRef();
  const snap = useSnapshot(state);
  const [straprotate, setStraprotate] = useState(false);
  const [fadeProgress, setFadeProgress] = useState(0);
  const modelGroupRef = useRef();
  const modelRef = useRef();
  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});
  // Track all strap model refs for inactivity rotation
  const strapModelRefs = useRef({});

  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});
  const isInactive = useInactivityDetection(4000);

  // Define strap types and configurations
  const STRAP_TYPES = {
    NONE: {
      id: "none",
      name: "No Strap",
      modelPath: null,
    },
    HANDLE: {
      id: "handle",
      name: "Handle Strap",
      modelPath: "/handlestraptotemu.fbx",
      position: [0, 0, 0],
      rotation: [0, 0.253, 0],
      scale: 0.12,
      textureFolder: "handlestraptotemtexu",
    },
    METAL: {
      id: "METAL",
      name: "Metal Chain",
      modelPath: "/chaintotemu.fbx",
      position: [0, 0, 0],
      rotation: [0, 0.253, 0],
      scale: 0.12,
      textureFolder: "chaintotes2texu",
    },
    BALL: {
      id: "BALL",
      name: "BALL CHAIN",
      modelPath: "/ballpainer.fbx",
      position: [0, 4, 0],
      rotation: [0, 0, 0],
      scale: 0.04,
      textureFolder: "microphonepainerballtex",
    },
    SHOULDER: {
      id: "SHOULDER",
      name: "SHOULDER CHAIN",
      modelPath: "/shoulderstraptotem.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.257, 0],
      scale: 0.12,
      textureFolder: "shoulderstraptex",
    },
    STRAPM: {
      id: "STRAPM",
      name: "STRAP CHAIN",
      modelPath: "/strapmlongtotem.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.252, 0],
      scale: 0.12,
      textureFolder: "longstraptextotesm",
    },
    SIDESTRAP3: {
      id: "SIDESTRAP3",
      name: "SIDESTRAP3",
      modelPath: "/strapmcurvetotem.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.253, 0],
      scale: 0.12,
      textureFolder: "curvetexu",
    },
    LOGO: {
      id: "LOGO",
      name: "LOGO",
      modelPath: "/chainlogolongtotem.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.12,
      textureFolder: "chainlogolongtex",
    },
    LOGO1: {
      id: "LOGO1",
      name: "LOGO1",
      modelPath: "/chaintotemlong2.fbx",
      position: [0, 4, 0],
      rotation: [0, 0.25, 0],
      scale: 0.12,
      textureFolder: "chainlogo1tex",
    },
  };

  // Function to get texture paths for a specific strap type
  const getTexturePathsForType = (strapType, textureFolder) => {
    const upperType = strapType.toUpperCase();

    switch (upperType) {
      case "HANDLE":
        return {
          leatherAlbedo: `/var1.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_1_albedo.jpg`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_1_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          metalNormal: `${textureFolder}/METAL_1_normal.png`,
          seamAlbedo: `${textureFolder}/Seams_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_normal.png`,
          seamRoughness: `${textureFolder}/Seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_metallic.jpg`,
          seamAO: `${textureFolder}/Seams_AO.jpg`,
        };
      case "METAL":
        return {
          plasticAlbedo: `/var2.png`,
          metalNormal: `${textureFolder}/METAL_1_normal.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_1_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          plasticAlbedo: `/var1.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
        };
      case "BALL":
        return {
          plasticAlbedo: `/var2.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
          metalAlbedo: `${textureFolder}/METALL_albedo.jpg`,
          metalRoughness: `${textureFolder}/METALL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METALL_metallic.jpg`,
          metalAO: `${textureFolder}/METALL_AO.jpg`,
        };
      case "SHOULDER":
        return {
          leatherAlbedo: `/var1.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          metalAlbedo: `${textureFolder}/Metal_PART1_albedo.jpg`,
          metalNormal: `${textureFolder}/Metal_PART1_normal.png`,
          metalRoughness: `${textureFolder}/Metal_PART1_roughness.jpg`,
          metalMetallic: `${textureFolder}/Metal_PART1_metallic.jpg`,
          metal2Albedo: `${textureFolder}/Metal_PART2_albedo.jpg`,
          metal2Normal: `${textureFolder}/Metal_PART2_normal.png`,
          metal2Roughness: `${textureFolder}/Metal_PART2_roughness.jpg`,
          metal2Metallic: `${textureFolder}/Metal_PART2_metallic.jpg`,
          seamAlbedo: `${textureFolder}/Seams_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_normal.png`,
          seamRoughness: `${textureFolder}/Seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_metallic.jpg`,
        };
      case "STRAPM":
        return {
          leatherAlbedo: `/var2.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_albedo.jpg`,
          metalNormal: `${textureFolder}/METAL_normal.png`,
          metalRoughness: `${textureFolder}/METAL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_AO.jpg`,
          seamAlbedo: `${textureFolder}/Seams_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_normal.png`,
          seamRoughness: `${textureFolder}/Seams_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_metallic.jpg`,
          seamAO: `${textureFolder}/Seams_AO.jpg`,
        };
      case "LOGO":
        return {
          metalAlbedo: `/var1.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_2_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          plasticAlbedo: `/var1.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
        };
      case "LOGO1":
        return {
          metalAlbedo: `/var1.png`,
          metalRoughness: `${textureFolder}/METAL_1_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_2_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_1_AO.jpg`,
          plasticAlbedo: `/var1.png`,
          plasticNormal: `${textureFolder}/PLASTIC_normal.png`,
          plasticRoughness: `${textureFolder}/PLASTIC_roughness.jpg`,
          plasticMetallic: `${textureFolder}/PLASTIC_metallic.jpg`,
          plasticAO: `${textureFolder}/PLASTIC_AO.jpg`,
        };
      case "SIDESTRAP3":
        return {
          leatherAlbedo: `/var2.png`,
          leatherNormal: `${textureFolder}/LEATHER_normal.png`,
          leatherRoughness: `${textureFolder}/LEATHER_roughness.jpg`,
          leatherMetallic: `${textureFolder}/LEATHER_metallic.jpg`,
          leatherAO: `${textureFolder}/LEATHER_AO.jpg`,
          metalAlbedo: `${textureFolder}/METAL_albedo.jpg`,
          metalNormal: `${textureFolder}/METAL_normal.png`,
          metalRoughness: `${textureFolder}/METAL_roughness.jpg`,
          metalMetallic: `${textureFolder}/METAL_metallic.jpg`,
          metalAO: `${textureFolder}/METAL_AO.jpg`,
          seamAlbedo: `${textureFolder}/Seams_3_albedo.jpg`,
          seamNormal: `${textureFolder}/Seams_3_normal.png`,
          seamRoughness: `${textureFolder}/Seams_3_roughness.jpg`,
          seamMetallic: `${textureFolder}/Seams_3_metallic.jpg`,
          seamAO: `${textureFolder}/Seams_3_AO.jpg`,
        };
      default:
        return {};
    }
  };

  // Determine which straps to render - FIXED VERSION
  const strapsToRender = (() => {
    // Use a Set to prevent duplicates
    const strapSet = new Set();

    // Add straps from selectedStraps
    if (selectedStraps) {
      if (selectedStraps.handle && selectedStraps.handle !== "none") {
        strapSet.add(selectedStraps.handle);
      }

      if (selectedStraps.shoulder && selectedStraps.shoulder !== "none") {
        strapSet.add(selectedStraps.shoulder);
      }
    }

    // Add the current type if it's not "none"
    if (type && type !== "none") {
      strapSet.add(type);
    }

    // Convert Set back to array
    let strapsArray = Array.from(strapSet);

    // Limit to maximum 2 straps
    const maxStraps = 2;
    if (strapsArray.length > maxStraps) {
      // Keep the most recent selections (prioritize current type)
      if (type && type !== "none" && strapsArray.includes(type)) {
        // Keep the current type and one other
        strapsArray = strapsArray
          .filter((strap) => strap === type)
          .concat(strapsArray.filter((strap) => strap !== type).slice(-1));
      } else {
        // Just take the last 2
        strapsArray = strapsArray.slice(-maxStraps);
      }
    }

    return strapsArray;
  })();

  console.log("Straps to render:", strapsToRender);
  console.log("Selected straps:", selectedStraps);

  // Load textures for all straps that need to be rendered
  const allTexturePaths = {};
  strapsToRender.forEach((strapType) => {
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (config && config.textureFolder) {
      const texturePaths = getTexturePathsForType(
        strapType,
        config.textureFolder
      );
      // Prefix each texture key with strap type to avoid conflicts
      Object.entries(texturePaths).forEach(([key, value]) => {
        allTexturePaths[`${strapType}_${key}`] = value;
      });
    }
  });

  // Load all textures at once
  const loadedTextures = useTexture(allTexturePaths);

  // Function to get textures for a specific strap type from loaded textures
  const getTexturesForStrap = (strapType) => {
    const strapTextures = {};
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (config && config.textureFolder) {
      const texturePaths = getTexturePathsForType(
        strapType,
        config.textureFolder
      );
      Object.keys(texturePaths).forEach((key) => {
        const textureKey = `${strapType}_${key}`;
        if (loadedTextures[textureKey]) {
          strapTextures[key] = loadedTextures[textureKey];
        }
      });
    }
    return strapTextures;
  };

  // Function to render a single strap
  const renderStrap = (strapType, key) => {
    const config = STRAP_TYPES[strapType.toUpperCase()];
    if (!config || !config.modelPath) return null;

    const textures = getTexturesForStrap(strapType);
  
  
    const getColorsForStrapType = (type) => {
    const upperType = type.toUpperCase();
    console.error("strap type",upperType)
    // Define which straps are handle straps
    const handleStrapTypes = ['HANDLE', 'BALL', 'METAL', 'STRAPM', 'SIDESTRAP3', 'LOGO', 'LOGO1'];
    
    // Define which straps are shoulder straps  
    const shoulderStrapTypes = ['SHOULDER'];
    
    if (handleStrapTypes.includes(upperType)) {
      return snap.strapcolors;
      return snap.handleStrapColors;
    } else if (shoulderStrapTypes.includes(upperType)) {
      console.error("@@@@@@")
      return snap.shoulderStrapColors;
    } else {
      // Fallback to default colors
      return snap.strapcolors;
    }
  };

  const strapColorss = getColorsForStrapType(strapType);
  console.error("!!!!",strapColorss);
  console.error("####",snap.strapcolors)
    // Setup props for the strap model
    const modelProps = {
      scale: config.scale || 1,
      rotation: config.rotation || [0, 1, 0],
      position: config.position || [0, 3, 0],
    };

    // Apply explicit rotation if provided
    if (
      rotation &&
      (rotation[0] !== 0 || rotation[1] !== 0 || rotation[2] !== 0)
    ) {
      console.log("Applying explicit rotation:", rotation);
      modelProps.rotation = [...rotation];
    }

    // Adjust position based on attachment points
    if (attachmentPoints && attachmentPoints.length > 0) {
      const attachmentPoint = attachmentPoints[0];

      if (strapType === "metal") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
      } else if (strapType === "handle") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
      } else if (strapType === "shoulder") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.75,
          attachmentPoint.position[2],
        ];
      } else if (strapType === "strapm") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
      } else if (strapType === "sidestrap3") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
      } else if (strapType === "logo") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
        console.log("Config rotation for logo:", config.rotation);
        modelProps.rotation = [
          config.rotation[0],
          config.rotation[1],
          config.rotation[2],
        ];
      } else if (strapType === "logo1") {
        modelProps.position = [
          attachmentPoint.position[0],
          attachmentPoint.position[1] - 1.7,
          attachmentPoint.position[2],
        ];
        console.log("Config rotation for logo1:", config.rotation);
        modelProps.rotation = [
          config.rotation[0],
          config.rotation[1],
          config.rotation[2],
        ];
      }
    }

    return (
      <FBXStrap
        key={key}
        ref={strapType === type ? ref : undefined}
        type={strapType}
        modelPath={config.modelPath}
        textures={textures}
        position={modelProps.position}
        rotation={modelProps.rotation}
        scale={modelProps.scale}
        strapColors={strapColorss}
        opacity={1}
        envMapIntensity={envMapIntensity}
        modelGroupRef={modelGroupRef}
        modelRef={(strapRef) => {
          // Store all strap model refs for inactivity rotation
          if (strapRef) {
            strapModelRefs.current[strapType] = strapRef;
            // Also set the main modelRef if this is the primary type
            if (strapType === type) {
              modelRef.current = strapRef;
            }
          }
        }}
        xrayMode={xrayMode}
                setLoading={setLoading}
      />
    );
  };

  // Initialize strap opacity and click tracking
  useEffect(() => {
    if (!state.strapClicks[type]) {
      state.strapClicks[type] = 0;
      state.strapOpacity = 1;
    }
  }, [type]);

  // Update strap opacity based on click count
  useEffect(() => {
    if (clickcount === 1) {
      state.strapOpacity = 1;
    } else if (clickcount === 2) {
      state.strapOpacity = 1;
    }
  }, [clickcount]);

  // Updated useFrame to handle rotation for all visible straps
  useFrame(() => {
    if (sharedInactivityRef.current) {
      // Apply rotation to all visible strap models
      Object.values(strapModelRefs.current).forEach((strapRef) => {
        if (strapRef && strapRef.rotation) {
          strapRef.rotation.y = sharedRotationRef.current + 0.25;
        }
      });
    } else {
      // Reset rotation for all strap models when not inactive
      Object.values(strapModelRefs.current).forEach((strapRef) => {
        if (strapRef && strapRef.rotation) {
          strapRef.rotation.y = sharedRotationRef.current + 0.25;
        }
      });
    }
  });

  // Clean up refs when straps are no longer rendered
  useEffect(() => {
    // Remove refs for straps that are no longer being rendered
    const currentStrapTypes = strapsToRender.map((s) => s.toLowerCase());
    Object.keys(strapModelRefs.current).forEach((strapType) => {
      if (!currentStrapTypes.includes(strapType.toLowerCase())) {
        delete strapModelRefs.current[strapType];
      }
    });
  }, [strapsToRender]);

  // Return null if no straps should be rendered
  if (strapsToRender.length === 0) return null;

  // Render all straps that should be visible
  return (
    <>
      {strapsToRender.map((strapType, index) =>
        renderStrap(strapType, `${strapType}-${index}`)
      )}
    </>
  );
}

// Component to load and display the FBX strap with textures
function FBXStrap({
  type,
  modelPath,
  textures,
  position,
  rotation,
  scale,
  strapColors,
  opacity,
  envMapIntensity,
  modelGroupRef,
  modelRef,
    setLoading,
  xrayMode,
}) {
  console.log("=== LEATHER TEXTURE DEBUG ===");
  console.log("Strap colors from props:", strapColors);
  console.log("X-ray mode:", xrayMode);
  console.log("All textures received:", textures);
  console.log("Leather textures specifically:", {
    leatherAlbedo: textures?.leatherAlbedo,
    leatherNormal: textures?.leatherNormal,
    leatherRoughness: textures?.leatherRoughness,
    leatherMetallic: textures?.leatherMetallic,
    leatherAO: textures?.leatherAO,
  });

  const snap = useSnapshot(state);

  // Check if this strap type should use the special x-ray color
  const isLeatherStrapType = useMemo(() => {
    const upperType = type.toUpperCase();
    const leatherStrapTypes = ['HANDLE', 'STRAPM', 'SHOULDER', 'SIDESTRAP3'];
    return leatherStrapTypes.includes(upperType);
  }, [type]);

  // Calculate X-ray mode properties with different opacity levels
  const xrayOpacity = xrayMode ? 0.3 : opacity; // Non-metal opacity in x-ray mode
  const xrayMetalOpacity = xrayMode ? 0.7 : opacity; // Metal opacity in x-ray mode (less transparent)
  
  // Use #707A7C for leather strap types in x-ray mode, otherwise use purple
  const xrayColor = xrayMode ? (isLeatherStrapType ? "#707A7C" : "#8A2BE2") : null;

  console.log("X-ray color for type", type, ":", xrayColor, "(isLeatherStrapType:", isLeatherStrapType, ")");

  // Helper function to safely check and process textures
  const safeTexture = (texture, textureName) => {
    console.log(`Checking texture ${textureName}:`, texture);

    if (!texture) {
      console.warn(`${textureName} is null/undefined`);
      return null;
    }

    try {
      if (typeof texture === "string") {
        console.warn(
          `${textureName} is still a string path: "${texture}". Expected loaded texture object.`
        );
        return null;
      }

      if (typeof texture === "object") {
        console.log(`${textureName} properties:`, {
          isTexture: texture.isTexture,
          hasImage: !!texture.image,
          hasSource: !!texture.source,
          constructor: texture.constructor.name,
        });

        if (texture.isTexture || texture.image || texture.source) {
          // Set proper color space and wrapping
          texture.colorSpace = textureName.toLowerCase().includes("albedo")
            ? THREE.SRGBColorSpace
            : THREE.NoColorSpace;
          texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
          console.log(`✓ ${textureName} processed successfully`);
          return texture;
        }
      }

      console.warn(
        `${textureName} doesn't appear to be a valid texture object`
      );
      return null;
    } catch (error) {
      console.error(`Error processing ${textureName}:`, error);
      return null;
    }
  };

  // Helper function to determine if a material is metal-based
  const isMetalMaterial = (materialName) => {
    const metalKeywords = [
      'metal', 'Metal', 'METAL',
      'chain', 'Chain', 'CHAIN',
      'hook', 'Hook', 'HOOK',
      'link', 'Link', 'LINK',
      'buckle', 'Buckle', 'BUCKLE',
      'clasp', 'Clasp', 'CLASP',
      'screw', 'Screw', 'SCREW',
      'rivet', 'Rivet', 'RIVET',
      'bolt', 'Bolt', 'BOLT',
      'stud', 'Stud', 'STUD'
    ];
    return metalKeywords.some(keyword => materialName.includes(keyword));
  };

  // Cache material mappings based on type
  const materialMappings = useMemo(() => {
    const upperType = type.toUpperCase();
    console.log(`Creating material mappings for type: ${upperType}`);

    // Process leather textures with detailed logging
    const safeLeatherAlbedo = safeTexture(
      textures?.leatherAlbedo,
      "leatherAlbedo"
    );
    const safeLeatherNormal = safeTexture(
      textures?.leatherNormal,
      "leatherNormal"
    );
    const safeLeatherRoughness = safeTexture(
      textures?.leatherRoughness,
      "leatherRoughness"
    );
    const safeLeatherMetallic = safeTexture(
      textures?.leatherMetallic,
      "leatherMetallic"
    );
    const safeLeatherAO = safeTexture(textures?.leatherAO, "leatherAO");

    // Create leather mapping with extensive logging and X-ray mode support
    const leatherMapping = {
      ...(safeLeatherAlbedo && !xrayMode && { map: safeLeatherAlbedo }), // Disable textures in x-ray mode
      ...(safeLeatherNormal && !xrayMode && { normalMap: safeLeatherNormal }),
      ...(safeLeatherRoughness &&
        !xrayMode && { roughnessMap: safeLeatherRoughness }),
      ...(safeLeatherMetallic &&
        !xrayMode && { metalnessMap: safeLeatherMetallic }),
      ...(safeLeatherAO &&
        !xrayMode && { aoMap: safeLeatherAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.1 : 0.9, // Smoother in x-ray mode
      metalness: xrayMode ? 0.8 : 0, // More metallic in x-ray mode
      color: new THREE.Color(xrayColor || strapColors.leather),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      // X-ray mode depth settings for non-metals
      depthTest: true,
      depthWrite: xrayMode ? false : true, // Don't write to depth buffer in x-ray mode
      renderOrder: xrayMode ? -1 : 0, // Render behind metals in x-ray mode
    };

    console.log("Created leather mapping with color:", leatherMapping.color, "for x-ray mode:", xrayMode);

    // Process other textures
    const safeMetalAlbedo = safeTexture(textures?.metalAlbedo, "metalAlbedo");
    const safeMetalRoughness = safeTexture(
      textures?.metalRoughness,
      "metalRoughness"
    );
    const safeMetalMetallic = safeTexture(
      textures?.metalMetallic,
      "metalMetallic"
    );

    // METAL MATERIALS - Slightly transparent in x-ray mode but more visible than other parts
    const metalMapping = {
      ...(safeMetalAlbedo && { map: safeMetalAlbedo }), // Always show textures for metals
      ...(safeMetalRoughness && { roughnessMap: safeMetalRoughness }),
      ...(safeMetalMetallic && { metalnessMap: safeMetalMetallic }),
      roughness: 0.1, // Keep original roughness
      metalness: 1, // Keep fully metallic
      color: new THREE.Color(strapColors.metal), // Keep original metal color
      opacity: xrayMetalOpacity, // Use metal-specific opacity (0.7 in x-ray mode)
      transparent: xrayMode || xrayMetalOpacity < 1, // Transparent in x-ray mode
      // Metal depth settings - ensure metals render on top
      depthTest: true,
      depthWrite: true, // Write to depth buffer to occlude other materials
      renderOrder: xrayMode ? 1 : 0, // Render on top in x-ray mode
    };

    const safeMetal2Albedo = safeTexture(
      textures?.metal2Albedo,
      "metal2Albedo"
    );
    const safeMetal2Roughness = safeTexture(
      textures?.metal2Roughness,
      "metal2Roughness"
    );
    const safeMetal2Metallic = safeTexture(
      textures?.metal2Metallic,
      "metal2Metallic"
    );
    const safeMetal2AO = safeTexture(textures?.metal2AO, "metal2AO");

    const metal2Mapping = safeMetal2Albedo
      ? {
          ...(safeMetal2Albedo && { map: safeMetal2Albedo }), // Always show textures
          ...(safeMetal2Roughness && { roughnessMap: safeMetal2Roughness }),
          ...(safeMetal2Metallic && { metalnessMap: safeMetal2Metallic }),
          roughness: 0.1,
          metalness: 1,
          color: new THREE.Color(strapColors.metal),
          opacity: xrayMetalOpacity, // Use metal-specific opacity
          transparent: xrayMode || xrayMetalOpacity < 1,
          depthTest: true,
          depthWrite: true,
          renderOrder: xrayMode ? 1 : 0,
        }
      : metalMapping;

    const safePlasticAlbedo = safeTexture(
      textures?.plasticAlbedo,
      "plasticAlbedo"
    );
    const safePlasticRoughness = safeTexture(
      textures?.plasticRoughness,
      "plasticRoughness"
    );
    const safePlasticMetallic = safeTexture(
      textures?.plasticMetallic,
      "plasticMetallic"
    );
    const safePlasticAO = safeTexture(textures?.plasticAO, "plasticAO");

    const plasticMapping = {
      ...(safePlasticAlbedo && !xrayMode && { map: safePlasticAlbedo }),
      ...(safePlasticRoughness &&
        !xrayMode && { roughnessMap: safePlasticRoughness }),
      ...(safePlasticMetallic &&
        !xrayMode && { metalnessMap: safePlasticMetallic }),
      ...(safePlasticAO &&
        !xrayMode && { aoMap: safePlasticAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.15 : 0.3,
      metalness: xrayMode ? 0.9 : 0.8,
      color: new THREE.Color(xrayColor || strapColors.plastic),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      depthTest: true,
      depthWrite: xrayMode ? false : true,
      renderOrder: xrayMode ? -1 : 0,
    };

    const safeSeamAlbedo = safeTexture(textures?.seamAlbedo, "seamAlbedo");
    const safeSeamNormal = safeTexture(textures?.seamNormal, "seamNormal");
    const safeSeamRoughness = safeTexture(
      textures?.seamRoughness,
      "seamRoughness"
    );
    const safeSeamMetallic = safeTexture(
      textures?.seamMetallic,
      "seamMetallic"
    );
    const safeSeamAO = safeTexture(textures?.seamAO, "seamAO");

    const seamMapping = {
      ...(safeSeamAlbedo && !xrayMode && { map: safeSeamAlbedo }),
      ...(safeSeamNormal && !xrayMode && { normalMap: safeSeamNormal }),
      ...(safeSeamRoughness &&
        !xrayMode && { roughnessMap: safeSeamRoughness }),
      ...(safeSeamMetallic && !xrayMode && { metalnessMap: safeSeamMetallic }),
      ...(safeSeamAO &&
        !xrayMode && { aoMap: safeSeamAO, aoMapIntensity: 1.0 }),
      roughness: xrayMode ? 0.2 : 0.4,
      metalness: xrayMode ? 0.7 : 0.0,
      color: new THREE.Color(xrayColor || strapColors.seams),
      opacity: xrayOpacity,
      transparent: xrayOpacity < 1 || xrayMode,
      depthTest: true,
      depthWrite: xrayMode ? false : true,      
      renderOrder: xrayMode ? -1 : 0,
    };

    const safeBeltAlbedo = safeTexture(textures?.beltAlbedo, "beltAlbedo");
    const safeBeltNormal = safeTexture(textures?.beltNormal, "beltNormal");
    const safeBeltRoughness = safeTexture(
      textures?.beltRoughness,
      "beltRoughness"
    );
    const safeBeltMetallic = safeTexture(
      textures?.beltMetallic,
      "beltMetallic"
    );
    const safeBeltAO = safeTexture(textures?.beltAO, "beltAO");

    const beltMapping = safeBeltAlbedo
      ? {
          ...(safeBeltAlbedo && !xrayMode && { map: safeBeltAlbedo }),
          ...(safeBeltNormal && !xrayMode && { normalMap: safeBeltNormal }),
          ...(safeBeltRoughness &&
            !xrayMode && { roughnessMap: safeBeltRoughness }),
          ...(safeBeltMetallic &&
            !xrayMode && { metalnessMap: safeBeltMetallic }),
          ...(safeBeltAO &&
            !xrayMode && { aoMap: safeBeltAO, aoMapIntensity: 1.0 }),
          roughness: xrayMode ? 0.1 : 0.9,
          metalness: xrayMode ? 0.8 : 0,
          color: new THREE.Color(strapColors.metal),
          opacity: xrayOpacity,
          transparent: xrayOpacity < 1 || xrayMode,
          depthTest: true,
          depthWrite: xrayMode ? false : true,
          renderOrder: xrayMode ? -1 : 0,
        }
      : leatherMapping;

    // Create comprehensive material mappings with more variations
    const createMappingSet = (defaultMapping) => ({
      // Leather variations - EXTENSIVE list
      Leather: leatherMapping,
      LEATHER: leatherMapping,
      leather: leatherMapping,
      Leather_01: leatherMapping,
      Leather_1: leatherMapping,
      "Leather.001": leatherMapping,
      Leather_Material: leatherMapping,
      LeatherMaterial: leatherMapping,
      leather_mat: leatherMapping,
      Belt: leatherMapping,
      BELT: leatherMapping,
      belt: leatherMapping,
      Belt2: beltMapping,
      BELT2: beltMapping,
      belt2: beltMapping,
      Strap: leatherMapping,
      STRAP: leatherMapping,
      strap: leatherMapping,

      // Metal variations - Use metal-specific opacity in x-ray mode
      Metal: metalMapping,
      METAL: metalMapping,
      metal: metalMapping,
      Metal_01: metalMapping,
      Metal_1: metalMapping,
      "Metal.001": metalMapping,
      Metal2: metal2Mapping,
      METAL2: metal2Mapping,
      metal2: metal2Mapping,
      Metal3: metalMapping,
      METAL3: metalMapping,
      metal3: metalMapping,
      Metal_PART1: metalMapping,
      Metal_PART2: metal2Mapping,
      METAL_PART1: metalMapping,
      METAL_PART2: metal2Mapping,
      HOOK_1: metalMapping,
      Chain: metalMapping,
      CHAIN: metalMapping,
      Link: metalMapping,
      LINK: metalMapping,
      Buckle: metalMapping,
      BUCKLE: metalMapping,
      buckle: metalMapping,
      Clasp: metalMapping,
      CLASP: metalMapping,
      clasp: metalMapping,

      // Plastic variations
      Plastic: plasticMapping,
      PLASTIC: plasticMapping,
      plastic: plasticMapping,
      Logo: plasticMapping,
      LOGO: plasticMapping,
      logo: plasticMapping,

      // Seam variations
      Seams: seamMapping,
      SEAMS: seamMapping,
      seams: seamMapping,
      Seams_3: seamMapping,
      SEAMS_3: seamMapping,

      // Generic material names
      Material: defaultMapping,
      material: defaultMapping,
      "Material.001": defaultMapping,
      "Material.002": metalMapping,
      "Material.003": plasticMapping,

      DEFAULT: defaultMapping,
    });

    // Return appropriate mapping based on strap type
    console.log(`Returning mappings for type: ${upperType}`);

    if (
      upperType === "HANDLE" ||
      upperType === "SHOULDER" ||
      upperType === "STRAPM" ||
      upperType === "SIDESTRAP3"
    ) {
      const mappings = createMappingSet(leatherMapping);
      console.log("Leather-based mappings created:", Object.keys(mappings));
      return mappings;
    } else if (
      upperType === "METAL" ||
      upperType === "LOGO" ||
      upperType === "LOGO1"
    ) {
      const mappings = createMappingSet(metalMapping);
      console.log("Metal-based mappings created:", Object.keys(mappings));
      return mappings;
    } else if (upperType === "BALL") {
      const mappings = createMappingSet(plasticMapping);
      console.log("Plastic-based mappings created:", Object.keys(mappings));
      return mappings;
    } else {
      const mappings = createMappingSet(leatherMapping);
      console.log("Generic mappings created:", Object.keys(mappings));
      return mappings;
    }
  }, [textures, strapColors, opacity, type, xrayMode, xrayOpacity, xrayMetalOpacity, xrayColor, isLeatherStrapType]);

  // Load FBX model
  const fbx = useFBX(modelPath);
  const model = useMemo(() => fbx.clone(), [fbx]);

  // Process the FBX model and apply materials
// In FBXStrap component, simplify the useEffect:
useEffect(() => {
  if (!model || !textures) {
    console.warn("Model or textures not available yet");
    return;
  }

  console.log("=== PROCESSING MODEL MATERIALS ===");
  
  let timeoutId;
  
  try {
    // Apply materials to the model
    model.traverse((child) => {
      if (child.isMesh) {
        const materialName = child.material?.name || "";
        console.log(`Processing mesh: "${child.name}" with material: "${materialName}"`);

        // Check if this is a metal material
        const isMetal = isMetalMaterial(materialName);

        // Create new material with appropriate opacity
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          opacity: isMetal && xrayMode ? xrayMetalOpacity : xrayOpacity,
          transparent: xrayMode || (isMetal ? xrayMetalOpacity < 1 : xrayOpacity < 1),
        });

        // Find and apply matching mapping
        let appliedMapping = materialMappings.DEFAULT;
        let matchedKey = "DEFAULT";

        // Try exact matches first
        Object.keys(materialMappings).forEach((matKey) => {
          if (matKey !== "DEFAULT" && materialName === matKey && materialMappings[matKey]) {
            appliedMapping = materialMappings[matKey];
            matchedKey = matKey;
          }
        });

        // Try partial matches if no exact match
        if (matchedKey === "DEFAULT") {
          Object.keys(materialMappings).forEach((matKey) => {
            if (
              matKey !== "DEFAULT" &&
              (materialName.includes(matKey) || matKey.toLowerCase().includes(materialName.toLowerCase())) &&
              materialMappings[matKey]
            ) {
              appliedMapping = materialMappings[matKey];
              matchedKey = matKey;
            }
          });
        }

        // Apply the mapping
        if (appliedMapping) {
          Object.assign(newMaterial, appliedMapping);

          // Special handling for x-ray mode
          if (isMetal && xrayMode) {
            newMaterial.opacity = xrayMetalOpacity;
            newMaterial.transparent = true;
            newMaterial.depthWrite = true;
            newMaterial.renderOrder = 1;
          } else if (xrayMode) {
            newMaterial.depthWrite = false;
            newMaterial.renderOrder = -1;
          }
        }

        // Setup UV coordinates
        if (child.geometry?.attributes?.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Apply environment map intensity
        newMaterial.envMapIntensity = isMetal && xrayMode ? 
          (envMapIntensity || 1.0) : 
          (xrayMode ? 0.3 : (envMapIntensity || 1.0));

        // Set the material
        child.material = newMaterial;
        child.castShadow = !xrayMode;
        child.receiveShadow = !xrayMode;
        child.material.needsUpdate = true;
      }
    });

    console.log("=== MODEL PROCESSING COMPLETE ===");
    
    // Set loading to false after a brief delay
    timeoutId = setTimeout(() => {
      if (setLoading) {
        setLoading('new strap', false);
      }
    }, 100);

  } catch (error) {
    console.error("Error in material processing:", error);
    if (setLoading) {
      timeoutId = setTimeout(() => {
        setLoading('new strap', false);
      }, 100);
    }
  }

  // Cleanup timeout on unmount or dependency change
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  };
}, [
  model,
  textures,
  materialMappings,
  strapColors,
  opacity,
  envMapIntensity,
  type,
  xrayMode,
  xrayOpacity,
  xrayMetalOpacity,
  // setLoading removed from dependencies
]);

  // Compute scale as array if a number is provided
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  return (
    <group
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
      ref={modelRef}
    >
      <group ref={modelGroupRef}>
        <primitive object={model} />
      </group>
    </group>
  );
}
function FBXModelWithTexturesopen(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 0, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    setActiveTab,
    sethoveredsphere,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setTitle,
    setanimation,
    setanimationsource,
    sethighlightdata,
    setLoading,
    showInfo,
    setShowTabs
  } = props;
  
  const useMobileDetection = () => useState(() => window.innerWidth <= 768)[0];
  const [modelPath, setModelPath] = useState("/totemmirror.fbx");
  const [modelKey, setModelKey] = useState(Date.now());
  const [isMirrorOpen, setIsMirrorOpen] = useState(false);
  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] = useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(false);
  const [modelLoaded, setModelLoaded] = useState(false);

  const initialAnimationDuration = 5000; // 5 seconds for initial animation
  const animationDelayTime = 3000; // 3 second delay before starting animation
  const initialRotationAmount = 0.07; // How much to rotate in each direction
  const fingerIconRef = useRef(null);
  const snap = useSnapshot(state);
  const modelGroupRef = useRef();
  const modelRef = useRef();
  const isMobile = useMobileDetection();

  // Add inactivity detection for showing spheres
  const isInactive = useInactivityDetection(4000);

  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});

  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});
  
  const toggleModel = useCallback(() => {
    console.log("Toggling model state");
    setLoading('model-toggle', true);
    
    state.isOpen = !state.isOpen;
    console.log("Model state is now:", state.isOpen);
  }, [setLoading]);

  // Define part pairs that should highlight together
  const partPairs = useMemo(() => {
    return {
      // Define pairs of parts that should highlight together
      Plane011: ["Plane011", "Plane012"],
      Plane012: ["Plane011", "Plane012"],
      For_mirrior: ["For_mirrior", "MIRROR"],
      MIRROR: ["For_mirrior", "MIRROR"],
      // Add more pairs as needed based on your model structure
    };
  }, []);

  // Keep track of which parts are hoverable
  const hoverableParts = useMemo(
    () => [
      "Plane011",
      "Plane012",
      "METALL_2",
      "Bottom_Plate",
      "For_mirrior",
      "MIRROR",
      "WLogoGreyOpen",
      "For_mirrior",
      "MIRROR",
      // "Cylinder",
      // "Outer",
      // "1_LEATHER",
      // "Middle",
      // "2_LEATHER",
      // "Inner",
      // "Object003"
    ],
    []
  );

  const fingerTexture = useTexture("/select.png");

  // Memoize textures to avoid unnecessary reloads
  const textures = useTexture({
    // Leather 1 textures
    leather1Albedo: "/totemmirrortex/LEATHER_albedo.jpg",
    leather1Normal: "/totemmirrortex/LEATHER_normal.png",
    leather1Roughness: "/totemmirrortex/LEATHER_roughness.jpg",
    leather1Metallic: "/totemmirrortex/LEATHER_metallic.jpg",
    // leather1AO: "/totemmirrortex/LEATHER_AO.jpg",

    // Metal textures
    metallAlbedo: "/totemmirrortex/METALL_albedo.jpg",
    // metallNormal: "/totemmirrortex/METALL_normal.png",
    metallRoughness: "/totemmirrortex/METALL_roughness.jpg",
    metallMetallic: "/totemmirrortex/METALL_metallic.jpg",
    // metallAO: "/totemmirrortex/METALL_AO.jpg",

    // Plastic textures
    plasticAlbedo: "/totemmirrortex/PLASTIC_albedo.jpg",
    plasticNormal: "/totemmirrortex/PLASTIC_normal.png",
    plasticRoughness: "totemmirrortex/PLASTIC_roughness.jpg",
    plasticMetallic: "/totemmirrortex/PLASTIC_metallic.jpg",
    // plasticAO: "/totemmirrortex/PLASTIC_AO.jpg",

    // Seams textures
    seamsAlbedo: "/totemmirrortex/Seams_albedo.jpg",
    seamsNormal: "/totemmirrortex/Seams_normal.png",
    seamsRoughness: "/totemmirrortex/Seams_roughness.jpg",
    seamsMetallic: "/totemmirrortex/Seams_metallic.jpg",
    // seamsAO: "/totemmirrortex/Seams_AO.jpg",
  });

  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX(modelPath);
  const model = useMemo(() => fbx.clone(), [fbx]);
  const tempcolor = state.colors;

  // Cache material mappings
  const materialMappings = useMemo(
    () => ({
      LEATHER: {
        map: textures.leather1Albedo,
        normalMap: textures.leather1Normal,
        roughnessMap: textures.leather1Roughness,
        metalnessMap: textures.leather1Metallic,
        aoMap: textures.leather1AO,
        aoMapIntensity: 1.0,
        roughness: 0.9,
        metalness: 0,
        color: new THREE.Color(tempcolor.leather),
      },
      METALL: {
        map: textures.metallAlbedo,
        normalMap: textures.metallNormal,
        roughnessMap: textures.metallRoughness,
        metalnessMap: textures.metallMetallic,
        aoMap: textures.metallAO,
        aoMapIntensity: 1.0,
        metalness: 1,
      },
      PLASTIC: {
        map: textures.plasticAlbedo,
        normalMap: textures.plasticNormal,
        roughnessMap: textures.plasticRoughness,
        metalnessMap: textures.plasticMetallic,
        aoMap: textures.plasticAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.plastic),
      },
      Seams: {
        map: textures.seamsAlbedo,
        normalMap: textures.seamsNormal,
        roughnessMap: textures.seamsRoughness,
        metalnessMap: textures.seamsMetallic,
        aoMap: textures.seamsAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.seams),
      },
    }),
    [textures]
  );

  // Handle inactivity state for showing spheres
  useEffect(() => {
    sharedInactivityRef.current = isInactive;
    setShowSpheres(isInactive);
  }, [isInactive]);

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;
      return hoverableParts.some((part) => partName.includes(part));
    },
    [hoverableParts]
  );

  // Helper function to get all parts that should be highlighted together
  const getRelatedParts = useCallback(
    (partName) => {
      // Find the base part name that matches our pairs
      const basePart = Object.keys(partPairs).find((key) =>
        partName.includes(key)
      );

      // If we found a base part that has defined pairs, return all related parts
      if (basePart && partPairs[basePart]) {
        return partPairs[basePart];
      }

      // If no pairing defined, just return the original part
      return [partName];
    },
    [partPairs]
  );
  
  const switchModel = useCallback(
    (newModelPath) => {
      console.log("Switching model to:", newModelPath);

      // Clear hover state
      setHoveredPart(null);
      sethoveredsphere?.(null);
      setTitle?.(null);

      // Reset material references
      allMeshRefs.current = {};
      originalMaterials.current = {};

      // Change the model path
      setModelPath(newModelPath);

      // Force component to remount completely with a new key
      setModelKey(Date.now());
    },
    [sethoveredsphere, setTitle]
  );
  
  // Handle part hover
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;

      // Set hover information based on part name
      setHoveredPart(partName);

      if (partName.includes("Plane012") || partName.includes("Plane011")) {
        setTitle?.("CLOSE BAG <br>");
        sethoveredsphere?.(
          "The WMB MAGFRAME™ clasp is custom-crafted from Italian Rhodoid — a sustainable cellulose material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a perfect fit, it closes with smooth precision and quiet certainty. Engineered for longevity, built into every WMB bag."
        );
      } else if (partName.includes("Bottom_Plate")) {
        setTitle?.("<br>BASE PLATE:");
        sethoveredsphere?.(
          "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB's engineered precision."
        );
      } else if (partName.includes("Cylinder")) {
        sethoveredsphere?.("side fastner");
        // setActiveTab?.(0);
      } else if (
        partName.includes("MIRROR") ||
        partName.includes("For_mirrior")
      ) {
        setanimation(true);
        setanimationsource("/ToteM_Mirror.mp4");
        setTitle?.("<br>PULL OUT MIRROR");
        sethoveredsphere?.(
          "A modern essential. This chromed metal mirror, engraved with the WMB monogram, is concealed within the bag's architecture. Revealed by a tonal leather tab, it reflects our focus on thoughtful utility and considered form."
        );
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("2_LEATHER") ||
        partName.includes("Inner")
      ) {
        setTitle?.("");
        sethoveredsphere?.(
          "<b>The Parcel - Frame </b><br><br>\
          Designed to blend in while carrying everything you need, the Tote M balances function and discretion.<br>\
          Understated yet intentional, it moves with quiet efficiency from day to night.<br><br>\
          Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for enduring shape and architectural clarity.<br>\
          Inside, a chromed metal mirror is concealed within its own compartment.<br>\
          Engraved with the WMB monogram and accessed via a tonal leather pull tab, it's a precise utility — seamlessly integrated.<br>\
          The magnetic closure is secured with our WMB MAGFRAME™ clasp — CNC-milled and laser-cut from sustainable Italian Rhodoid for a smooth, confident snap.<br>\
          Finished with a raised metal monogram and the WMB MODULAR LOCK SYSTEM™ — allowing for one or two straps or chains, and effortless carry by hand, shoulder, or crossbody.<br><br>\
          Structured, silent, and built to keep pace.<br>\
          Handcrafted in Germany.<br><br>\
          • Dimensions: 21.2 cm x 6.8 cm x 22.8 cm<br>\
          • Materials: Epsom Leather / Nappa lining / Palladium plated steel / Rhodoid / Chromed Mirror<br>\
          • Strap: Adjustable leather shoulder strap and leather handle strap included<br>\
          • Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>\
          • Colour options: Charcoal, Burgundy, Sand, Petrol<br>\
          • Hardware made in Italy"
        );
      }
      // else if (partName.includes("Object003")) {
      //   setTitle?.("");
      //   sethoveredsphere?.("flaps");
      // }
      else if (partName.includes("WLogoGreyOpen")) {
        setanimation(true);
        setanimationsource("/logo.mp4");
        setTitle?.("METAL MONOGRAM");
        sethoveredsphere?.(
          "A chromed metal plate, partially embedded into the leather and set with the WMB monogram in relief.<br><br>A restrained signature — discreet, dimensional, and unmistakable."
        );
      } else if (partName.includes("METALL_2")) {
        setanimation(true);
        setanimationsource("/side2u.mp4");
        setTitle?.("<brATTACH STRAPS");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      }
    },
    [sethoveredsphere, setActiveTab, setTitle, switchModel, setanimation, setanimationsource]
  );

  // Handle part click
  const handlePartClick = useCallback(
    (partName) => {
      console.log("partname in openfbx on click", partName);

      // If showInfo is true, apply hover-like effects on click (for mobile)
      if (showInfo && isMobile) {
        handlePartHover(partName);
        setTimeout(() => {
          setHoveredPart(null);
        }, 1000);
      } else {
        if (partName.includes("Plane012") || partName.includes("Plane011")) {
          // Toggle the bag open/close state
          toggleModel();
        } else if (partName.includes("METALL_2_3")) {
          // Handle cylinder clicks (side fastener)
          if (setActiveTab) {
            setShowTabs(true);
            setActiveTab(2);
          }
        } else if (
          partName.includes("MIRROR") ||
          partName.includes("For_mirrior")
        ) {
          const newState = !isMirrorOpen;
          console.log("Toggling mirror to:", newState ? "open" : "closed");
          const newPath =
            modelPath === "/totemmirror.fbx"
              ? "/totemmirrorout.fbx" // Change to this if currently mirror
              : "/totemmirror.fbx"; // Change to this if currently mirrorout

          switchModel(newPath);
          setLoading({newPath},true);
        }
      }

      // Hide finger icon when user interacts with the bag
      setShowFingerIcon(false);
    },
    [setActiveTab, modelPath, switchModel, showInfo, isMobile, handlePartHover, toggleModel]
  );

  // References for initial animation
  const fingerPositionRef = useRef({ x: -100, y: 0 });

  // Apply hover effects and animations in useFrame
  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      // Scale-based animation - start smaller and grow to full size
      const scaleValue = 0.8 + fadeProgress * 0.2; // Scale from 80% to 100%
      modelGroupRef.current.scale.set(scaleValue, scaleValue, scaleValue);

      // Add slight rotation during fade-in for more dynamic effect
      modelGroupRef.current.rotation.y = (1 - fadeProgress) * Math.PI * 0.08;
    }

    // Apply initial animation
    if (initialAnimationActive && ref.current) {
      // Calculate elapsed time since initial animation started
      const elapsedTime = Date.now() - initialAnimationStartTime;

      if (elapsedTime <= initialAnimationDuration) {
        // Create a pendulum-like motion during initial animation
        const progress = elapsedTime / initialAnimationDuration;
        const oscillation =
          Math.sin(progress * Math.PI * 4) * initialRotationAmount;

        // Set the rotation directly based on the oscillation
        ref.current.rotation.y = oscillation;

        // Update finger icon position to follow the motion
        if (fingerIconRef.current) {
          fingerPositionRef.current.x = -oscillation * 7;
          fingerIconRef.current.position.x = fingerPositionRef.current.x;
        }
      }
    }

    // Handle inactivity rotation (simple rotation without highlighting)
    if (sharedInactivityRef.current && ref.current) {
      sharedRotationRef.current -= 0.0025;
      ref.current.rotation.y = sharedRotationRef.current;
    } else if (!initialAnimationActive && ref.current && !sharedInactivityRef.current) {
      ref.current.rotation.y = sharedRotationRef.current;
    }

    // Update shared inactivity state
    state.isInactive = isInactive;

    // Handle hover effects - only user hover now
    const activeHighlight = hoveredPart;

    // Clear any previous hover effects if there's no currently highlighted part
    if (!activeHighlight) {
      // Restore original materials for all previously highlighted parts
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    }

    // Apply hover effect to currently highlighted part and related parts
    else if (activeHighlight) {
      // Get all parts that should be highlighted together
      const relatedParts = getRelatedParts(activeHighlight);

      // Highlight all related parts
      relatedParts.forEach((basePart) => {
        // Find all meshes that contain this base part name
        Object.keys(allMeshRefs.current).forEach((meshName) => {
          if (meshName.includes(basePart) && isHoverablePart(meshName)) {
            const mesh = allMeshRefs.current[meshName];

            if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
              // Store original material if not already stored
              if (!originalMaterials.current[meshName]) {
                originalMaterials.current[meshName] = mesh.material.clone();
              }

              // Create a new hover material
              const hoverMaterial = mesh.material.clone();

              // Apply hover effect based on part type
              if (
                meshName.includes("Plane012") ||
                meshName.includes("Plane011")
              ) {
                // Strong hover effect for clickable parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("METALL_2_3") ||
                meshName.includes("Cylinder") ||
                meshName === "MIRROR" ||
                meshName === "For_mirrior" ||
                meshName === "WLogoGreyOpen"
              ) {
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (meshName.includes("Bottom_Plate")) {
                // Subtle hover effect for other parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.2;
                // Keep the original color but brighten it slightly
                if (
                  originalMaterials.current[meshName] &&
                  originalMaterials.current[meshName].color
                ) {
                  const originalColor =
                    originalMaterials.current[meshName].color.clone();
                  hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
                }
              }

              // Mark this as a hover material
              hoverMaterial._isHoverMaterial = true;
              hoverMaterial.needsUpdate = true;

              // Apply the hover material
              mesh.material = hoverMaterial;
            }
          }
        });
      });
    }
  });

  // Set up initial materials and store references to all meshes
  useEffect(() => {
    if (!model || !textures) return;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Store material name for reference
        const materialName = child.material.name;

        // Create optimized material
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
        });

        // Find and apply the correct textures
        Object.keys(materialMappings).forEach((matKey) => {
          if (materialName.includes(matKey)) {
            Object.assign(newMaterial, materialMappings[matKey]);
          }
        });

        // Setup uv2 coordinates efficiently
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Apply environment map intensity
        newMaterial.envMapIntensity = envMapIntensity;

        // Set the new material
        child.material = newMaterial;
        child.castShadow = true;
        child.material.needsUpdate = true;
      }
    });
    
    state.isLoading = false;
    
    // Set model as loaded after materials are applied
    setModelLoaded(true);
    setLoading('totem-closed', false)

    // Cleanup
    return () => {
      allMeshRefs.current = {};
      originalMaterials.current = {};
    };
  }, [model, textures, materialMappings, envMapIntensity, modelPath]);

  // Event handlers for pointer events
  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;
      if (isMobile) return;

      // Set the hovered part and call the hover handler
      if (partName) {
        setHoveredPart(partName);
        handlePartHover(partName);
        console.log("Hovering over:", partName);
      }

      setShowSpheres(false);
      setShowFingerIcon(false);
    },
    [handlePartHover, isMobile]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setanimation?.(false);
      setanimationsource?.(null);

      // Only clear hover state on non-touch devices
      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle, setanimation, setanimationsource]
  );

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  // Create a finger icon sprite material
  const fingerMaterial = useMemo(() => {
    if (!fingerTexture) {
      return new THREE.SpriteMaterial({
        color: 0xffff00,
        opacity: 0.7,
        transparent: true,
      });
    }

    return new THREE.SpriteMaterial({
      map: fingerTexture,
      transparent: true,
      opacity: 0.9,
      color: 0xffff00,
    });
  }, [fingerTexture]);

  // Calculate finger icon position
  const fingerIconPosition = useMemo(() => {
    return [position[0] + 5.5, position[1] + 4.5, position[2] + 5];
  }, [position]);

  return (
    <group
      key={modelKey}
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      {/* Model group with fade-in effect */}
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>

      {/* Finger icon sprite */}
      {showFingerIcon && (
        <group position={fingerIconPosition}>
          <sprite
            ref={fingerIconRef}
            material={fingerMaterial}
            scale={[1.5, 1.5, 1.5]}
          />

          {/* Text hint */}
          <Html position={[0, -4, 0]} center>
            <div
              style={{
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px 10px",
                borderRadius: "4px",
                fontSize: "14px",
                whiteSpace: "nowrap",
                fontFamily: "Arial, sans-serif",
              }}
            >
              Drag to rotate
            </div>
          </Html>
        </group>
      )}
    </group>
  );
}
function FBXModelWithTexturesclosed(props) {
  const {
    position = [0, 0, 0],
    rotation = [0, 0, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    setActiveTab,
    sethoveredsphere,
    setTitle,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setanimation,
    setanimationsource,
    sethighlightdata,
    setLoading,
    showInfo,
    isLoading,
                      setShowTabs
  } = props;

  const useMobileDetection = () => useState(() => window.innerWidth <= 768)[0];
  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] =
    useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(true); // Show by default
  const [fingerIconStartTime, setFingerIconStartTime] = useState(null); // Track finger icon animation start
  const [modelLoaded, setModelLoaded] = useState(false); // Track if model is loaded

  // Add state for rotation-based highlighting
  const [rotationBasedHighlight, setRotationBasedHighlight] = useState(null);
  const [lastRotationAngle, setLastRotationAngle] = useState(0);
  const [highlightInfo, setHighlightInfo] = useState(null);
  
  // NEW: Add permanent disable states
  const [rotationHighlightingDisabled, setRotationHighlightingDisabled] = useState(false);
  const [rotationHighlightingHasStarted, setRotationHighlightingHasStarted] = useState(false);
  
  const fingerIconRef = useRef(null);

  const initialAnimationDuration = 5000; // 5 seconds for initial animation
  const animationDelayTime = 3000; // 3 second delay before starting animation
  const initialRotationAmount = 0.07; // How much to rotate in each direction
  const fingerOscillationDuration = 3000; // 3 seconds for finger oscillation
  const snap = useSnapshot(state);
  const oscillationCount = snap.oscillation_count;
  const modelGroupRef = useRef();
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);
  const isInactive = useInactivityDetection(4000);
  console.log("Is mobile device:", useMobileDetection());
  const isMobile = useMobileDetection();

  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});
  const isInactiveForRotation = useInactivityDetection(100);
  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});

  const isTogglingModelRef = useRef(false);

  // Define part pairs that should highlight together
  const partPairs = useMemo(() => {
    return {
      Plane009: ["Plane009", "Plane011"],
      Plane011: ["Plane009", "Plane011"],
      // Add more pairs as needed
    };
  }, []);

  // Keep track of which parts are hoverable
  const hoverableParts = useMemo(
    () => [
      "Plane011",
      "Plane009",
      "METALL_2",
      "Bottom_Plate",
      "WLogoGrey",
      "Cylinder",
      // Add more hoverable parts as needed
    ],
    []
  );

  // Load finger icon texture
  const fingerTexture = useTexture("/select.png");

  // Memoize textures to avoid unnecessary reloads
  const textures = useTexture({
    // Leather 1 textures
    leather1Albedo: "/totemclosed/2k/Leather_albedo.jpg",
    leather1Normal: "/totemclosed/2k/Leather_normal.png",
    leather1Roughness: "/totemclosed/2k/Leather_roughness.jpg",
    leather1Metallic: "/totemclosed/2k/Leather_metallic.jpg",
    leather1AO: "/totemclosed/2k/Leather_AO.jpg",

    // Metal textures
    metallAlbedo: "/totemclosed/2k/Metal_albedo.jpg",
    // metallNormal: "/totemclosed/2k/Metal_normal.png",
    metallRoughness: "/totemclosed/2k/Metal_roughness.jpg",
    metallMetallic: "/totemclosed/2k/Metal_metallic.jpg",
    metallAO: "/totemclosed/2k/Metal_AO.jpg",

    // Plastic textures
    plasticAlbedo: "/totemclosed/2k/Plastic_albedo.jpg",
    plasticNormal: "/totemclosed/2k/Plastic_normal.png",
    plasticRoughness: "/totemclosed/2k/Plastic_roughness.jpg",
    plasticMetallic: "/totemclosed/2k/Plastic_metallic.jpg",
    plasticAO: "/totemclosed/2k/Plastic_AO.jpg",

    // Seams textures
    seamsAlbedo: "/totemclosed/2k/Seams_albedo.jpg",
    seamsNormal: "/totemclosed/2k/Seams_normal.png",
    seamsRoughness: "/totemclosed/2k/Seams_roughness.jpg",
    seamsMetallic: "/totemclosed/2k/Seams_metallic.jpg",
    seamsAO: "/totemclosed/2k/Seams_AO.jpg",
  });

  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX("/Tote_M_Closed_Fix.fbx");
  const model = useMemo(() => fbx.clone(), [fbx]);
  const tempcolor = state.colors;

  // Cache material mappings
  const materialMappings = useMemo(
    () => ({
      Leather: {
        map: textures.leather1Albedo,
        normalMap: textures.leather1Normal,
        roughnessMap: textures.leather1Roughness,
        metalnessMap: textures.leather1Metallic,
        aoMap: textures.leather1AO,
        aoMapIntensity: 1.0,
        roughness: 0.9,
        metalness: 0,
        color: new THREE.Color(tempcolor.leather),
      },
      Metal: {
        map: textures.metallAlbedo,
        normalMap: textures.metallNormal,
        roughnessMap: textures.metallRoughness,
        metalnessMap: textures.metallMetallic,
        aoMap: textures.metallAO,
        aoMapIntensity: 1.0,
        metalness: 1,
      },
      Plastic: {
        map: textures.plasticAlbedo,
        normalMap: textures.plasticNormal,
        roughnessMap: textures.plasticRoughness,
        metalnessMap: textures.plasticMetallic,
        aoMap: textures.plasticAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.plastic),
      },
      Seams: {
        map: textures.seamsAlbedo,
        normalMap: textures.seamsNormal,
        roughnessMap: textures.seamsRoughness,
        metalnessMap: textures.seamsMetallic,
        aoMap: textures.seamsAO,
        aoMapIntensity: 1.0,
        metalness: 0.8,
        color: new THREE.Color(tempcolor.seams),
      },
    }),
    [textures, tempcolor]
  );

  // Add useEffect to initialize first interaction state
  useEffect(() => {
    // Initialize first interaction state if not already set
    if (state.firtsinteraction === undefined) {
      state.firtsinteraction = 0;
    }
  }, []);

  // Debug: Track rotation highlighting state changes
  useEffect(() => {
    console.log("Rotation highlighting states:", {
      disabled: rotationHighlightingDisabled,
      hasStarted: rotationHighlightingHasStarted,
      isInactive: isInactiveForRotation,
      firstInteraction: state.firtsinteraction
    });
  }, [rotationHighlightingDisabled, rotationHighlightingHasStarted, isInactiveForRotation]);

  // Add useEffect to permanently disable rotation highlighting on any user activity
  useEffect(() => {
    if (rotationHighlightingHasStarted && !isInactiveForRotation && !rotationHighlightingDisabled) {
      console.log("User activity detected after rotation highlighting started - permanently disabling");
      setRotationHighlightingDisabled(true);
      setRotationBasedHighlight(null);
      sethighlightdata(null);
      setHighlightInfo(null);
      sethoveredsphere?.(null);
      setTitle?.(null);
    }
  }, [isInactiveForRotation, rotationHighlightingHasStarted, rotationHighlightingDisabled, sethighlightdata, sethoveredsphere, setTitle]);

  // Add useEffect to clear highlighting when first interaction occurs
  useEffect(() => {
    if (state.firtsinteraction === 1) {
      setRotationBasedHighlight(null);
      sethighlightdata(null);
      setHighlightInfo(null);
      sethoveredsphere?.(null);
      setTitle?.(null);
    }
  }, [state.firtsinteraction, sethighlightdata, sethoveredsphere, setTitle]);

  // MODIFIED: Function to determine which part to highlight based on rotation angle
// REPLACE your getPartToHighlightByRotation function with this fixed version:

const getPartToHighlightByRotation = useCallback(
  (rotationAngle) => {
    // If rotation highlighting has been permanently disabled, always return null
    if (rotationHighlightingDisabled) {
      console.log("Rotation highlighting permanently disabled - returning null");
      return null;
    }

    // Check if user is currently active/interacting
    if (!isInactiveForRotation) {
      // If rotation highlighting had started and now user is active, disable it permanently
      if (rotationHighlightingHasStarted) {
        console.log("User became active after rotation highlighting started - permanently disabling");
        setRotationHighlightingDisabled(true);
        sethighlightdata(null);
        setHighlightInfo(null);
        state.firtsinteraction = 1; // Mark first interaction
        return null;
      }
      
      // If rotation highlighting hasn't started yet, just clear current highlights
      // DON'T set firtsinteraction = 1 here!
      // sethighlightdata(null);
      // setHighlightInfo(null);
      // state.firtsinteraction = 1; // ← REMOVE THIS LINE!
      return null;
    }

    // Normalize angle to 0-360 degrees
    console.log("Rotation angle:", rotationAngle);
    const normalizedAngle =
      ((((rotationAngle * 180) / Math.PI) % 360) + 360) % 360;

    // Check for specific rotation threshold OR if user has had their first interaction
    if (rotationAngle <= -6.283185307179586 || state.firtsinteraction === 1) {
      sethighlightdata(null);
      return null;
    }

    // Mark that rotation highlighting has started
    if (!rotationHighlightingHasStarted) {
      console.log("Rotation highlighting starting for the first time");
      setRotationHighlightingHasStarted(true);
    }

    // Create pulsing effect using timestamp
    const pulseInterval = 300; // Pulse every 300ms
    const shouldHighlight = Math.floor(Date.now() / pulseInterval) % 2 === 0;

    // If we're in the "off" phase of the pulse, return null
    if (!shouldHighlight) {
      return "xyz";
    }

    // Define rotation ranges for different parts (adapted for tote M bag)
    if (normalizedAngle >= 315 || normalizedAngle < 45) {
      // Front view (0 degrees ± 45) - highlight magnetic clasp (both Plane009 and Plane011)
      setHighlightInfo("OPEN BAG");
      sethighlightdata("PRESS HERE TO OPEN BAG");
      return "Plane009"; // This will trigger both Plane009 and Plane011 due to pairing
    } else if (normalizedAngle >= 45 && normalizedAngle < 135) {
      // Right side view (90 degrees ± 45) - highlight side fasteners
      setHighlightInfo("SIDE FASTENERS");
      sethighlightdata("PRESS HERE TO ATTACH STRAPS");
      return "METALL_2";
    } else if (normalizedAngle >= 135 && normalizedAngle < 225) {
      // Back view (180 degrees ± 45) - highlight side fasteners
      setHighlightInfo("SIDE FASTENERS");
      sethighlightdata("PRESS HERE TO ATTACH STRAPS");
      return "METALL_2";
    } else if (normalizedAngle >= 225 && normalizedAngle < 315) {
      // Left side view (270 degrees ± 45) - highlight side fasteners
      setHighlightInfo("SIDE FASTENERS");
      sethighlightdata("PRESS HERE TO ATTACH STRAPS");
      return "METALL_2";
    }

    setHighlightInfo(null);
    return null;
  },
  [setHighlightInfo, isInactiveForRotation, sethighlightdata, rotationHighlightingDisabled, rotationHighlightingHasStarted]
);

// ALSO: Add this debug useEffect to see what's happening:
useEffect(() => {
  console.log("DEBUG - Rotation states:", {
    disabled: rotationHighlightingDisabled,
    hasStarted: rotationHighlightingHasStarted,
    isInactiveForRotation: isInactiveForRotation,
    firtsinteraction: state.firtsinteraction
  });
}, [rotationHighlightingDisabled, rotationHighlightingHasStarted, isInactiveForRotation, state.firtsinteraction]);

// DEBUGGING: Add this to see if the function is being called:
useEffect(() => {
  const interval = setInterval(() => {
    if (isInactiveForRotation && !rotationHighlightingDisabled) {
      console.log("DEBUG - Should be highlighting! Inactive for rotation and not disabled");
    }
  }, 1000);
  
  return () => clearInterval(interval);
}, [isInactiveForRotation, rotationHighlightingDisabled]);



  // Reset state when model changes
  useEffect(() => {
    // Clear mesh references to prepare for new model
    allMeshRefs.current = {};
    originalMaterials.current = {};
    setModelLoaded(false);
    setRotationBasedHighlight(null);
  }, [sethoveredsphere, setTitle]);

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;
      return hoverableParts.some((part) => partName.includes(part));
    },
    [hoverableParts]
  );

  // Helper function to get all parts that should be highlighted together
  const getRelatedParts = useCallback(
    (partName) => {
      // Find the base part name that matches our pairs
      const basePart = Object.keys(partPairs).find((key) => partName === key);

      // If we found a base part that has defined pairs, return all related parts
      if (basePart && partPairs[basePart]) {
        return partPairs[basePart];
      }

      // If no pairing defined, just return the original part
      return [partName];
    },
    [partPairs]
  );

  // Handle part hover
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;

      // Set hover information based on part name
      setHoveredPart(partName);

      if (partName.includes("Plane009") || partName.includes("Plane011")) {
        setTitle?.("OPEN BAG");
        sethoveredsphere?.(
          "<br><b>MAGNETIC CLASP:</b><br>The WMB MAGFRAME™ clasp is custom-crafted in Italy from Rhodoid — a sustainable cellulose-based material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a flawless fit, it closes with quiet precision and enduring strength — a refined mechanism built into every WMB bag."
        );
      } else if (partName.includes("Bottom_Plate")) {
        setTitle?.("BASE PLATE:");
        sethoveredsphere?.(
          "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB's engineered precision."
        );
      } else if (partName.includes("Cylinder")) {
        setTitle?.(" ATTACH STRAPS");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      } else if (partName.includes("WLogoBlack")) {
        setanimation?.(true);
        setanimationsource?.("/logo.mp4");
        setTitle?.("METAL MONOGRAM");
        sethoveredsphere?.(
          "A chromed metal plate, partially embedded into the leather and set with the WMB monogram in relief.<br><br>A restrained signature — discreet, dimensional, and unmistakable."
        );
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("2_LEATHER") ||
        partName.includes("Inner")
      ) {
        sethoveredsphere?.(
          "The Parcel - Frame <br><br>\
          Designed to blend in while carrying everything you need, the Tote M balances function and discretion.<br>\
          Understated yet intentional, it moves with quiet efficiency from day to night.<br><br>\
          Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for enduring shape and architectural clarity.<br>\
          Inside, a chromed metal mirror is concealed within its own compartment.<br>\
          Engraved with the WMB monogram and accessed via a tonal leather pull tab, it's a precise utility — seamlessly integrated.<br>\
          The magnetic closure is secured with our WMB MAGFRAME™ clasp — CNC-milled and laser-cut from sustainable Italian Rhodoid for a smooth, confident snap.<br>\
          Finished with a raised metal monogram and the WMB MODULAR LOCK SYSTEM™ — allowing for one or two straps or chains, and effortless carry by hand, shoulder, or crossbody.<br><br>\
          Structured, silent, and built to keep pace.<br>\
          Handcrafted in Germany.<br><br>\
          • Dimensions: 21.2 cm x 6.8 cm x 22.8 cm<br>\
          • Materials: Epsom Leather / Nappa lining / Palladium plated steel / Rhodoid / Chromed Mirror<br>\
          • Strap: Adjustable leather shoulder strap and leather handle strap included<br>\
          • Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>\
          • Colour options: Charcoal, Burgundy, Sand, Petrol<br>\
          • Hardware made in Italy"
        );
      } else if (partName.includes("METALL_2")) {
        setanimation?.(true);
        setanimationsource?.("/side2u.mp4");
        setTitle?.("ATTACH STRAPS");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      }
    },
    [sethoveredsphere, setActiveTab, setTitle, setanimation, setanimationsource]
  );

  // Fix the toggleModel function inside this component
  const toggleModel = useCallback(() => {
    console.log("Toggling model state");
    
    if (isTogglingModelRef.current) return;
    isTogglingModelRef.current = true;
    
    setTimeout(() => {
      setLoading('model-toggle', true);
      state.isOpen = !state.isOpen;
      console.log("Model state is now:", state.isOpen);
      isTogglingModelRef.current = false;
    }, 50);
  }, [setLoading]);
  // Function to apply rotation-based highlighting
  const applyRotationBasedHighlight = useCallback(
    (partName) => {
      if (partName) {
        console.log("Rotation-based highlighting:", partName);
        setRotationBasedHighlight(partName);
        handlePartHover(partName);
      } else {
        setRotationBasedHighlight(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle,handlePartHover]
  );
  // Handle part click
  const handlePartClick = useCallback(
    (partName) => {
      console.log("Clicked on:", partName);

      // If showInfo is true, apply hover-like effects on click
      if (showInfo && isMobile) {
        handlePartHover(partName); // This will trigger the same effects as hover

        // Clear the hover state after a delay
        setTimeout(() => {
          setHoveredPart(null);
        }, 1000);
      } else {
        if (partName.includes("Plane009") || partName.includes("Plane011")) {
          // Toggle the bag open/close state
          toggleModel();
        } else if (partName.includes("METALL_2")) {
          // Handle cylinder clicks (side fastener)
                      setShowTabs(true);
          setActiveTab?.(2);
        }
      }

      // Hide finger icon when user interacts with the bag
      setShowFingerIcon(false);
    },
    [setActiveTab, showInfo, handlePartHover, isMobile, toggleModel]
  );

  console.log("Inactivity status:", isInactive);
  useEffect(() => {
    sharedInactivityRef.current = isInactive;
    setShowSpheres(isInactive);
  }, [isInactive]);

  // References for initial animation
  const fingerPositionRef = useRef({ x: -100, y: 0 });

  // MODIFIED: Apply hover effects and animations in useFrame
  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      // Scale-based animation - start smaller and grow to full size
      modelGroupRef.current.scale.set(0.8, 0.8, 0.8);
      modelGroupRef.current.rotation.y = (1 - fadeProgress) * Math.PI * 0.08;
    }

    // Apply initial animation
    if (initialAnimationActive) {
      const elapsedTime = Date.now() - initialAnimationStartTime;

      if (elapsedTime <= initialAnimationDuration) {
        const progress = elapsedTime / initialAnimationDuration;
        // Use oscillation count from global state
        const oscillation =
          Math.sin(progress * Math.PI * oscillationCount) * 0.3;

        console.log(`Progress: ${progress}, Oscillation: ${oscillation}`);

        // Try multiple approaches to apply rotation
        if (ref?.current) {
          ref.current.rotation.y = oscillation;
          console.log(`Applied rotation to ref: ${oscillation}`);
        }

        if (modelGroupRef?.current) {
          modelGroupRef.current.rotation.y = oscillation;
          console.log(`Applied rotation to modelGroup: ${oscillation}`);
        }

        // Update shared rotation ref if you have it
        if (sharedRotationRef) {
          sharedRotationRef.current = oscillation;
        }
      }
    }

    // Apply finger icon oscillation animation when model is loaded
    if (
      showFingerIcon &&
      modelLoaded &&
      fingerIconRef.current &&
      fingerIconStartTime
    ) {
      const elapsedTime = Date.now() - fingerIconStartTime;

      if (elapsedTime <= fingerOscillationDuration) {
        const progress = elapsedTime / fingerOscillationDuration;
        // Create oscillating movement (left-right swing)
        const oscillation = Math.sin(progress * Math.PI * 4) * 0.5; // 4 oscillations over 3 seconds

        // Apply horizontal oscillation to the finger icon
        fingerIconRef.current.position.x = oscillation;

        // Optional: Add slight vertical bounce
        const bounce = Math.abs(Math.sin(progress * Math.PI * 8)) * 0.2;
        fingerIconRef.current.position.y = bounce;

        // Fade out towards the end
        if (progress > 0.8) {
          const fadeProgress = (progress - 0.8) / 0.2;
          fingerIconRef.current.material.opacity = 0.9 * (1 - fadeProgress);
        }
      } else {
        // Animation complete, hide finger icon
        setShowFingerIcon(false);
      }
    }

    // MODIFIED: Handle inactivity rotation and rotation-based highlighting
    if (sharedInactivityRef.current && ref.current) {
      sharedRotationRef.current -= 0.0025;
      ref.current.rotation.y = sharedRotationRef.current;

      // Only do rotation-based highlighting if it hasn't been permanently disabled
      if (!rotationHighlightingDisabled) {
        // Check if rotation angle has changed significantly and update highlighting
        const currentRotation = sharedRotationRef.current;
        const rotationDifference = Math.abs(currentRotation - lastRotationAngle);

        // Update highlighting every 5 degrees (approximately 0.087 radians)
        if (rotationDifference > 0.087) {
          const partToHighlight = getPartToHighlightByRotation(currentRotation);

          // Only update if the part to highlight has changed
          if (partToHighlight !== rotationBasedHighlight) {
            applyRotationBasedHighlight(partToHighlight);
          }

          setLastRotationAngle(currentRotation);
        }
      } else {
        // If highlighting is disabled, ensure all highlight data is cleared
        if (rotationBasedHighlight) {
          setRotationBasedHighlight(null);
          sethighlightdata(null);
          setHighlightInfo(null);
          sethoveredsphere?.(null);
          setTitle?.(null);
          applyRotationBasedHighlight(null);
        }
      }
    } else if (
      !initialAnimationActive &&
      ref.current &&
      !sharedInactivityRef.current
    ) {
      ref.current.rotation.y = sharedRotationRef.current;
      
      // Always clear highlight data when user becomes active
      // sethighlightdata(null);
      
      // Clear rotation-based highlighting when not inactive
      if (rotationBasedHighlight && !hoveredPart) {
        setRotationBasedHighlight(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    }

    // Update shared inactivity state
    state.isInactive = isInactive;

    // Handle hover effects
    // Determine which part should be highlighted (user hover takes priority over rotation-based)
    const activeHighlight = hoveredPart || rotationBasedHighlight;

    // Step 1: First, clear any previous hover effects if there's no currently highlighted part
    if (!activeHighlight) {
      // Restore original materials for all previously highlighted parts
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    }

    // Step 2: Apply hover effect to currently highlighted part and related parts
    else if (activeHighlight) {
      // Get all parts that should be highlighted together
      const relatedParts = getRelatedParts(activeHighlight);

      // Highlight all related parts
      relatedParts.forEach((basePart) => {
        // Find all meshes that contain this base part name
        Object.keys(allMeshRefs.current).forEach((meshName) => {
          // Use exact matching for paired parts to avoid conflicts
          let shouldHighlight = false;

          if (meshName.includes("Plane") || basePart.includes("Plane")) {
            // Use exact match for Plane parts
            shouldHighlight = meshName === basePart;
          } else {
            // For other parts, continue using includes
            shouldHighlight = meshName.includes(basePart);
          }

          if (shouldHighlight && isHoverablePart(meshName)) {
            const mesh = allMeshRefs.current[meshName];

            if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
              // Store original material if not already stored
              if (!originalMaterials.current[meshName]) {
                originalMaterials.current[meshName] = mesh.material.clone();
              }

              // Create a new hover material
              const hoverMaterial = mesh.material.clone();

              // Apply hover effect based on part type
              if (
                meshName.includes("Plane009") ||
                meshName.includes("Plane011")
              ) {
                // Strong hover effect for clickable parts
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else if (
                meshName.includes("METALL_2") ||
                meshName.includes("Cylinder") ||
                meshName.includes("WLogoGrey") ||
                meshName.includes("Bottom_Plate")
              ) {
                // Medium hover effect for metal parts and other interactive elements
                hoverMaterial.emissive = new THREE.Color(0xffff00);
                hoverMaterial.emissiveIntensity = 0.8;
              } else {
                // Subtle hover effect for other parts
                hoverMaterial.emissive = new THREE.Color(0xf7ff0040);
                hoverMaterial.emissiveIntensity = 0.2;
                // Keep the original color but brighten it slightly
                if (
                  originalMaterials.current[meshName] &&
                  originalMaterials.current[meshName].color
                ) {
                  const originalColor =
                    originalMaterials.current[meshName].color.clone();
                  hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
                }
              }

              // Mark this as a hover material
              hoverMaterial._isHoverMaterial = true;
              hoverMaterial.needsUpdate = true;

              // Apply the hover material
              mesh.material = hoverMaterial;
            }
          }
        });
      });
    }
  });

  // Set up initial materials and store references to all meshes
  useEffect(() => {
    if (!model || !textures) return;

    let timeoutId;
    let fingerTimeoutId;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Debug log mesh names
        console.log("Available mesh names:", child.name);

        // Store material name for reference
        const materialName = child.material.name;
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
          roughness: 0.8,
          metalness: 0.0,
          envMapIntensity: envMapIntensity,
        });

        // Apply material mappings
        if (materialMappings[materialName]) {
          Object.assign(newMaterial, materialMappings[materialName]);
        }

        // Store original material
        originalMaterials.current[child.name] = newMaterial.clone();

        // Apply material to mesh
        child.material = newMaterial;
        child.castShadow = true;
        child.receiveShadow = true;
        child.material.needsUpdate = true;
      }
    });

    // Set model as loaded
    setModelLoaded(true);

    // Set loading states with delays
    timeoutId = setTimeout(() => {
      if (setLoading) {
        setLoading('initial', false);
        setLoading('micro-closed', false);
      }
    }, 50);

    fingerTimeoutId = setTimeout(() => {
      setFingerIconStartTime(Date.now());
    }, 1000);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (fingerTimeoutId) clearTimeout(fingerTimeoutId);
    };
  }, [model, textures, materialMappings, envMapIntensity]);

  // Event handlers for pointer events
  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;
      if (isMobile) return;

      // Set the hovered part and call the hover handler
      if (partName) {
        setHoveredPart(partName);
        handlePartHover(partName);
        console.log("Hovering over:", partName);
      }

      // Reset inactivity timer
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = setTimeout(() => {
          if (!isMouseDownRef.current) {
            setShowSpheres(true);
          }
        }, 4000);
      }

      setShowSpheres(false);
      setShowFingerIcon(false);
    },
    [handlePartHover, isMobile]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setanimation?.(false);
      setanimationsource?.(null);

      // Only clear hover state on non-touch devices
      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle, setanimation, setanimationsource]
  );

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  // Create a finger icon sprite material
  const fingerMaterial = useMemo(() => {
    if (!fingerTexture) {
      return new THREE.SpriteMaterial({
        color: 0xffff00,
        opacity: 0.7,
        transparent: true,
      });
    }

    return new THREE.SpriteMaterial({
      map: fingerTexture,
      transparent: true,
      opacity: 0.9,
      color: 0xffff00,
    });
  }, [fingerTexture]);

  // Calculate finger icon position
  const fingerIconPosition = useMemo(() => {
    return [position[0] + 5.5, position[1] + 4.5, position[2] + 5];
  }, [position]);

  return (
    <group
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      {/* Model group with fade-in effect */}
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>

      {/* Finger icon sprite */}
    </group>
  );
}
function FBXModelWithTexturesclosedd(props) {
  const {
    position = [0, 0, 0],
    scale = 1,
    setShowRotationAxes,
    setOpen,
    setActiveTab,
    sethoveredsphere,
    setTitle,
    xrayMode,
    envMapIntensity = 1,
    ref,
    fadeInDuration = 1500,
    skipInitialAnimation = false,
    setanimation,
    setanimationsource,
    setLoading
  } = props;

  const [fadeProgress, setFadeProgress] = useState(0);
  const [showSpheres, setShowSpheres] = useState(false);
  const [hoveredPart, setHoveredPart] = useState(null);
  const [initialAnimationActive, setInitialAnimationActive] = useState(false);
  const [initialAnimationStartTime, setInitialAnimationStartTime] =
    useState(null);
  const [showFingerIcon, setShowFingerIcon] = useState(false);
  const initialAnimationDuration = 5000; // 5 seconds for initial animation
  const animationDelayTime = 3000; // 3 second delay before starting animation
  const initialRotationAmount = 1; // How much to rotate in each direction
  const fingerIconRef = useRef(null);
  const snap = useSnapshot(state);
  const modelGroupRef = useRef();
const rotation = [0, Math.PI/2, 0]
  // Track all meshes in the model for direct material manipulation
  const allMeshRefs = useRef({});

  // Store original materials to restore them when not hovering
  const originalMaterials = useRef({});

  // Keep track of which parts are hoverable
  const hoverableParts = useMemo(
    () => ["Plane011", "Plane009", "METALL_2", "Bottom_Plate", "WLogoGrey"],
    []
  );
  const fingerTexture = useTexture("/select.png");

  const textures = useTexture({
    leather1Albedo: "/var1.png",
    leather1Normal: "/totesclosed/2/Leather_normal.png",
    leather1Roughness: "/totesclosed/2/Leather_roughness.jpg",
    leather1Metallic: "/totesclosed/2/Leather_metallic.jpg",
    leather1AO: "/totesclosed/2/Leather_AO.jpg",
    leatheralpha: "/LEATHERTOTEM.png",

    // Metal textures
    // metallAlbedo: "/totesclosed/_albedo.jpg",
    metallNormal: "/totemxraytex/METAL1.png",
    // metallRoughness: "/totemxraytex_roughness.jpg",
    // metallMetallic: "/totemxraytex_metallic.jpg",
    // metallAO: "/totemxraytex_AO.jpg",
    metalalpha: "/totemxraytex/METAL1.png",

    // Plastic textures
    plasticAlbedo: "/totesclosed/2/Plastic_albedo.jpg",
    plasticNormal: "/totesclosed/2/Plastic_normal.png",
    plasticRoughness: "/totesclosed/2/Plastic_roughness.jpg",
    plasticMetallic: "/totesclosed/2/Plastic_metallic.jpg",
    plasticAO: "/totesclosed/2/Plastic_AO.jpg",
    plasticalpha: "/plastictotemu.png",
    // plasticalpha1: "/totemxraytex/Plane011.png",
    // Seams textures
    seamsAlbedo: "/totesclosed/2/seams_albedo.jpg",
    seamsNormal: "/totesclosed/2/seams_normal.png",
    seamsRoughness: "/totesclosed/2/seams_roughness.jpg",
    seamsMetallic: "/totesclosed/2/seams_metallic.jpg",
    seamsAO: "/totesclosed/2/seams_AO.jpg",
    Mirroralpha: "/totemxraytex/Mirror_1.png",
    tabletalpha: "/totemxraytex/tablet.png",
    lidalpha: "/totemxraytex/lid 1.png",
    iphonealpha: "/totemxraytex/IPHONE X.png",
    keysalpha: "/totemxraytex/bunch_new_keys.png",
    bottlealpha: "/totemxraytex/Bottle.png",
    padalpha:"/totemxraytex/PAD_albedo.png",
    phonechargeralpha: "/totemxraytex/phone_charger.png",
    usbalpha: "/totemxraytex/usb.png",
    boxalpha: "/totemxraytex/BOX.png",
    leathermirroralpha: "/totemxraytex/Leather_mirror.png",
    chargeralpha: "/totemxraytex/phone_charger.png",
    metalalpha2: "/totemxraytex/METAL 2.png",
    // bottomalpha: "/totemxraytex/Bottom_Plate.png",
    // legsalpha: "/totemxraytex/legs.png",
  });

  // Load FBX model with useMemo to prevent unnecessary reloads
  const fbx = useFBX("/totemxrayupdated.fbx");
  const model = useMemo(() => fbx.clone(), [fbx]);
  const tempcolor = state.colors;

  // Cache material mappings`x
  const materialMappings = useMemo(
    () => ({
      LEATHER: {
        // map: textures.leather1Albedo,
        // normalMap: textures.leather1Normal,
        // roughnessMap: textures.leather1Roughness,
        // metalnessMap: textures.leather1Metallic,
        // aoMap: textures.leather1AO,
        // aoMapIntensity: 1.0,
        // roughness: 0.9,
        // metalness: 0,
        color: new THREE.Color("#707A7C"),
        // color: new THREE.Color(tempcolor.leather),
        alphaMap: textures.leatheralpha,
        transparent: true,
        depthWrite: false,
        opacity: 0.8,
        transparent: true,
      },
      METAL: {
        // map: textures.metallAlbedo,
        // roughnessMap: textures.metallRoughness,
        // metalnessMap: textures.metallMetallic,
        // aoMap: textures.metallAO,
        // aoMapIntensity: 1.0,
        // metalness: 1,
        alphaMap: textures.metalalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#3C3FB9"),
        opacity: 0.8,
      },
              "stitch": {
                color: new THREE.Color("#8F8A89"),
                opacity: 0.8,
                transparent: true,
                depthWrite: false,
              },
            "METAL 2": {
        // map: textures.metallAlbedo,
        // roughnessMap: textures.metallRoughness,
        // metalnessMap: textures.metallMetallic,
        // aoMap: textures.metallAO,
        // aoMapIntensity: 1.0,
        // metalness: 1,
        alphaMap: textures.metalalpha2,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#3C3FB9"),
        opacity: 0.8,
      },
      PLASTIC: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.plasticalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#A967DE"),
        opacity: 0.8,
      },
      Mirror: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.Mirroralpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#3C3FB9"),
        opacity: 0.8,
      },
      tablet: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.tabletalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#5D6382"),
        opacity: 0.8,
      },
      lid: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.lidalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#3C3FB9"),
        opacity: 0.8,
      },
      IPHONE: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.iphonealpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#515390"),
        opacity: 0.8,
      },
      keys: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.keysalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#5D6382"),
        metalness: 1.0, // How much the material is like a metal
        roughness: 0.1,
        opacity: 0.8,
      },
      keys: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.keysalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#5D6382"),
        opacity: 0.8,
      },
      Bottle: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.bottlealpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#A967DE"),
        opacity: 0.8,
      },
      Bottom_Plate: {
        // map: textures.plasticAlbedo,
        // normalMap: textures.plasticNormal,
        // roughnessMap: textures.plasticRoughness,
        // metalnessMap: textures.plasticMetallic,
        // aoMap: textures.plasticAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        // color: new THREE.Color(tempcolor.plastic),
        alphaMap: textures.bottomalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#800080"),
        opacity: 0.8,
      },
      Seams: {
        // map: textures.seamsAlbedo,
        // normalMap: textures.seamsNormal,
        // roughnessMap: textures.seamsRoughness,
        // metalnessMap: textures.seamsMetallic,
        // aoMap: textures.seamsAO,
        // aoMapIntensity: 1.0,
        // metalness: 0.8,
        color: new THREE.Color("#8F8A89"),
        opacity: 0.8,
      },
      legs: {
        // map: textures.metallAlbedo,
        // roughnessMap: textures.metallRoughness,
        // metalnessMap: textures.metallMetallic,
        // aoMap: textures.metallAO,
        // aoMapIntensity: 1.0,
        // metalness: 1,
        alphaMap: textures.legsalpha,
        transparent: true,
        depthWrite: false,
        color: new THREE.Color("#3C3FB9"),

        opacity: 0.8,
      },
                  BOX: {
              // map: textures.metallAlbedo,
              // roughnessMap: textures.metallRoughness,
              // metalnessMap: textures.metallMetallic,
              // aoMap: textures.metallAO,
              // aoMapIntensity: 1.0,
              // metalness: 1,
              alphaMap: textures.boxalpha,
              transparent: true,
              depthWrite: false,
              color: new THREE.Color("#964e55"),
      
              opacity: 0.8,
            },
                        usb: {
                          // map: textures.plasticAlbedo,
                          // normalMap: textures.plasticNormal,
                          // roughnessMap: textures.plasticRoughness,
                          // metalnessMap: textures.plasticMetallic,
                          // aoMap: textures.plasticAO,
                          // aoMapIntensity: 1.0,
                          // metalness: 0.8,
                          // color: new THREE.Color(tempcolor.plastic),
                          alphaMap: textures.usbalpha,
                          transparent: true,
                          depthWrite: false,
                          color: new THREE.Color("#5e649f"),
                          opacity: 0.8,
                        },
                                                PAD: {
                          // map: textures.plasticAlbedo,
                          // normalMap: textures.plasticNormal,
                          // roughnessMap: textures.plasticRoughness,
                          // metalnessMap: textures.plasticMetallic,
                          // aoMap: textures.plasticAO,
                          // aoMapIntensity: 1.0,
                          // metalness: 0.8,
                          // color: new THREE.Color(tempcolor.plastic),
                          alphaMap: textures.padalpha,
                          transparent: true,
                          depthWrite: false,
                          color: new THREE.Color("#11c328"),
                          opacity: 0.8,
                        },
                              Leather_mirror: {
                                // map: textures.metallAlbedo,
                                // roughnessMap: textures.metallRoughness,
                                // metalnessMap: textures.metallMetallic,
                                // aoMap: textures.metallAO,
                                // aoMapIntensity: 1.0,
                                // metalness: 1,
                                alphaMap: textures.leathermirroralpha,
                                transparent: true,
                                depthWrite: false,
                                color: new THREE.Color("#707A7C"),
                        
                                opacity: 0.8,
                              },
                              standard_surface: {
                                // map: textures.metallAlbedo,
                                // roughnessMap: textures.metallRoughness,
                                // metalnessMap: textures.metallMetallic,
                                // aoMap: textures.metallAO,
                                // aoMapIntensity: 1.0,
                                // metalness: 1,
                                alphaMap: textures.chargeralpha, 
                                transparent: true,
                                depthWrite: false,
                                color: new THREE.Color("#5e649f"),
                        
                                opacity: 0.8,
                              },
    }),
    [textures]
  );

  // Helper function to check if a part is hoverable
  const isHoverablePart = useCallback(
    (partName) => {
      if (!partName) return false;
      return hoverableParts.some((part) => partName.includes(part));
    },
    [hoverableParts]
  );

  // Handle part hover
  const handlePartHover = useCallback(
    (partName) => {
      if (!partName) return;

      // Set hover information based on part name
      setHoveredPart(partName);

      if (partName.includes("Plane009") || partName.includes("Plane011")) {
        setTitle?.("OPEN BAG");
        sethoveredsphere?.(
          "<br><b>MAGNETIC CLASP:</b><br>The WMB MAGFRAME™ clasp is custom-crafted in Italy from Rhodoid — a sustainable cellulose-based material with the depth and feel of natural horn.<br><br>CNC-milled and laser-cut for a flawless fit, it closes with quiet precision and enduring strength — a refined mechanism built into every WMB bag."
        );
      } else if (partName.includes("Bottom_Plate")) {
        setTitle?.("BASE PLATE:");
        sethoveredsphere?.(
          "A custom base plate in Italian Rhodoid reinforces every WMB bag — CNC-milled and laser-cut to exacting standards.<br><br>It protects, shapes, and defines the silhouette with structural clarity. A subtle but essential layer of WMB’s engineered precision."
        );
      } else if (partName.includes("Cylinder")) {
        setTitle?.(" ATTACH STRAPS ™");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );

        // setActiveTab?.(0);
      } else if (partName.includes("WLogoGrey")) {
        setanimation(true);
        setanimationsource("/logo.mp4");
        setTitle?.("METAL MONOGRAM");
        sethoveredsphere?.(
          "A chromed metal plate, partially embedded into the leather and set with the WMB monogram in relief.<br><br>A restrained signature — discreet, dimensional, and unmistakable."
        );
      } else if (
        partName.includes("Outer") ||
        partName.includes("1_LEATHER") ||
        partName.includes("Middle") ||
        partName.includes("2_LEATHER") ||
        partName.includes("Inner")
      ) {
        sethoveredsphere?.(
          "<b>The Parcel - Frame </b><br><br>\
          Sleek and versatile, the Tote S transitions effortlessly from day to night.<br>\
          It carries just enough — balancing function with an understated allure.<br><br>\
          Crafted in premium Epsom leather and reinforced with a custom Rhodoid base plate for lasting structure and architectural clarity.<br><br>\
          Inside, a chromed metal mirror is discreetly housed within a dedicated compartment.<br>\
          Engraved with the WMB monogram and revealed by a tonal leather pull tab, it reflects our approach to refined utility.<br>\
          The opening is secured with the WMB MAGFRAME™ clasp — CNC-milled and laser-cut from Italian Rhodoid for a precise and quiet magnetic close.<br>\
          Finished with a raised metal monogram and WMB MODULAR LOCK SYSTEM™ — our custom palladium mechanism allowing for one or two straps or chains, to carry by hand, shoulder, or crossbody.<br><br>\
          Compact, elegant, from day to night.<br>\
          Handcrafted in Germany.<br><br>\
          • Dimensions: 21.1 cm x 7.6 cm x 11.5 cm<br>\
          • Materials: Epsom Leather / Nappa lining / Palladium plated steel / Rhodoid / Chromed Mirror<br>\
          • Strap: Adjustable leather shoulder strap and leather handle strap included<br>\
          • Hardware: Palladium plated / Rhodoid / Chromed Mirror<br>\
          • Colour options: Charcoal, Burgundy, Sand, Petrol<br>\
          • Hardware made in Italy"
        );
      } else if (partName.includes("METALL_2")) {
        setanimation(true);
        setanimationsource("/side2u.mp4");
        setTitle?.("ATTACH STRAPS");
        sethoveredsphere?.(
          "The WMB MODULAR LOCK SYSTEM™ is custom-crafted in Italy from palladium-finished metal. Precision-engineered for fluid adaptability, it allows for single or double strap attachments — worn by hand, on the shoulder, or crossbody.<br><br>A modular signature, exclusive to WMB."
        );
      }
    },
    [sethoveredsphere, setActiveTab]
  );

  // Handle part click
  const handlePartClick = useCallback(
    (partName) => {
      console.log("partname in closefbx on click", partName);

      if (partName.includes("Plane009") || partName.includes("Plane011")) {
        // Toggle the bag open/close state
        toggleModel();
      } else if (partName.includes("METALL_2")) {
        // Handle cylinder clicks (side fastener)
        setActiveTab(2);
      }

      // Hide finger icon when user interacts with the bag
      setShowFingerIcon(false);
    },
    [setActiveTab]
  );

  // Optimized inactivity detection
  const inactivityTimeoutRef = useRef(null);
  const isMouseDownRef = useRef(false);

  // useEffect(() => {
  //   const handleInactivity = () => {
  //     if (!isMouseDownRef.current) {
  //       setShowSpheres(true);
  //     }
  //   };

  //   const resetInactivityTimeout = () => {
  //     clearTimeout(inactivityTimeoutRef.current);
  //     inactivityTimeoutRef.current = setTimeout(handleInactivity, 4000);
  //   };

  //   const handleUserActivity = () => {
  //     resetInactivityTimeout();
  //     setShowSpheres(false);
  //   };

  //   const handleMouseDown = () => {
  //     isMouseDownRef.current = true;
  //     resetInactivityTimeout();
  //     setShowSpheres(false);
  //     setShowFingerIcon(false);
  //     //  // if (ref.current) ref.current.rotation.y = 0;
  //   };

  //   const handleMouseUp = () => {
  //     isMouseDownRef.current = false;
  //     resetInactivityTimeout();
  //   };

  //   const handleMouseMove = () => {
  //     handleUserActivity();
  //     if (isMouseDownRef.current) {
  //       resetInactivityTimeout();
  //     }
  //   };

  //   window.addEventListener("keydown", handleUserActivity);
  //   window.addEventListener("mousedown", handleMouseDown);
  //   window.addEventListener("mouseup", handleMouseUp);
  //   window.addEventListener("mousemove", handleMouseMove);

  //   resetInactivityTimeout();

  //   return () => {
  //     clearTimeout(inactivityTimeoutRef.current);
  //     window.removeEventListener("keydown", handleUserActivity);
  //     window.removeEventListener("mousedown", handleMouseDown);
  //     window.removeEventListener("mouseup", handleMouseUp);
  //     window.removeEventListener("mousemove", handleMouseMove);
  //   };
  // }, []);

  // References for initial animation
  const fingerPositionRef = useRef({ x: -100, y: 0 });
  const isInactive = useInactivityDetection(4000);
  useEffect(() => {
    sharedInactivityRef.current = isInactive;
    setShowSpheres(isInactive);
  }, [isInactive]);
  // Apply hover effects and animations in useFrame
  useFrame((state) => {
    const currentTime = state.clock.getElapsedTime() * 1000;

    // Apply fade-in effect using scale
    if (modelGroupRef.current && fadeProgress < 1) {
      // Scale-based animation - start smaller and grow to full size
      const scaleValue = 0.8 + fadeProgress * 0.2; // Scale from 80% to 100%
      modelGroupRef.current.scale.set(scaleValue, scaleValue, scaleValue);

      // Add slight rotation during fade-in for more dynamic effect
      modelGroupRef.current.rotation.y =
        (1 - fadeProgress) * Math.PI * 0.08;
    }

    // Apply initial animation
    if (initialAnimationActive && ref.current) {
      // Calculate elapsed time since initial animation started
      const elapsedTime = Date.now() - initialAnimationStartTime;

      if (elapsedTime <= initialAnimationDuration) {
        // Create a pendulum-like motion during initial animation
        const progress = elapsedTime / initialAnimationDuration;
        const oscillation =
          Math.sin(progress * Math.PI * 4) * initialRotationAmount;

        // Set the rotation directly based on the oscillation
        ref.current.rotation.y = oscillation;

        // Update finger icon position to follow the motion
        if (fingerIconRef.current) {
          fingerPositionRef.current.x = -oscillation * 7;
          fingerIconRef.current.position.x = fingerPositionRef.current.x;
        }
      }
    } else if (isInactive && ref.current) {
      // ref.current.rotation.y -= 0.0025;
    }
    if (sharedInactivityRef.current && ref.current) {
      sharedRotationRef.current -= 0.0025;
      ref.current.rotation.y = sharedRotationRef.current;
    } else if (
      !initialAnimationActive &&
      ref.current &&
      !sharedInactivityRef.current
    ) {
      ref.current.rotation.y = sharedRotationRef.current;
      // sharedRotationRef.current = 0;
      // ref.current.rotation.y = 0;
    }

    // Update shared inactivity state
    state.isInactive = isInactive;
    // Handle hover effects
    // Step 1: First, clear any previous hover effects if there's no currently hovered part
    if (!hoveredPart) {
      // Restore original materials for all previously hovered parts
      Object.keys(originalMaterials.current).forEach((partName) => {
        const mesh = allMeshRefs.current[partName];
        if (mesh && mesh.material && mesh.material._isHoverMaterial) {
          mesh.material = originalMaterials.current[partName];
          delete originalMaterials.current[partName];
        }
      });
    }
    // Step 2: Apply hover effect to currently hovered part
    else if (
      hoveredPart &&
      isHoverablePart(hoveredPart) &&
      allMeshRefs.current[hoveredPart]
    ) {
      const mesh = allMeshRefs.current[hoveredPart];
      if (mesh && mesh.material && !mesh.material._isHoverMaterial) {
        // Store original material if not already stored
        if (!originalMaterials.current[hoveredPart]) {
          originalMaterials.current[hoveredPart] = mesh.material.clone();
        }

        // Create a new hover material
        const hoverMaterial = mesh.material.clone();

        // Apply hover effect based on part type
        if (
          hoveredPart.includes("Plane009") ||
          hoveredPart.includes("Plane011")
        ) {
          // Strong hover effect for clickable parts
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
          // hoverMaterial.emissive = new THREE.Color(0x3333ff);
          // hoverMaterial.emissiveIntensity = 0.5;
          // hoverMaterial.color = new THREE.Color().setHSL(0.6, 0.8, 0.6); // Bluish highlight
        } else if (
          hoveredPart.includes("METALL_2") ||
          hoveredPart.includes("Cylinder") ||
          hoveredPart.includes("WLogoGrey")
        ) {
          // Medium hover effect for metal parts
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
          // hoverMaterial.emissive = new THREE.Color(0xffff33);
          // hoverMaterial.emissiveIntensity = 0.4;
          // hoverMaterial.color = new THREE.Color().setHSL(0.15, 0.7, 0.7); // Golden highlight
        } else {
          // Subtle hover effect for other parts
          hoverMaterial.emissive = new THREE.Color(0xffff00);
          hoverMaterial.emissiveIntensity = 0.8;
          // Keep the original color but brighten it slightly
          if (originalMaterials.current[hoveredPart].color) {
            const originalColor =
              originalMaterials.current[hoveredPart].color.clone();
            hoverMaterial.color = originalColor.offsetHSL(0, 0, 0.1);
          }
        }

        // Mark this as a hover material
        hoverMaterial._isHoverMaterial = true;
        hoverMaterial.needsUpdate = true;

        // Apply the hover material
        mesh.material = hoverMaterial;
      }
    }
  });

  // Set up initial materials and store references to all meshes
  useEffect(() => {
    if (!model || !textures) return;

    // Set color spaces for all textures
    Object.keys(textures).forEach((key) => {
      textures[key].colorSpace = key.includes("Albedo")
        ? THREE.SRGBColorSpace
        : THREE.NoColorSpace;
      textures[key].wrapS = textures[key].wrapT = THREE.RepeatWrapping;
    });

    // Process all materials in the model
    model.traverse((child) => {
      if (child.isMesh) {
        // Store reference to mesh by name
        allMeshRefs.current[child.name] = child;

        // Store material name for reference
        const materialName = child.material.name;

        // Create optimized material
        const newMaterial = new THREE.MeshStandardMaterial({
          name: materialName,
          color: new THREE.Color(1, 1, 1),
        });

        // Find and apply the correct textures
        Object.keys(materialMappings).forEach((matKey) => {
          if (materialName.includes(matKey)) {
            Object.assign(newMaterial, materialMappings[matKey]);
          }
        });

        // Setup uv2 coordinates efficiently
        if (child.geometry.attributes.uv && !child.geometry.attributes.uv2) {
          child.geometry.setAttribute("uv2", child.geometry.attributes.uv);
        }

        // Apply environment map intensity
        newMaterial.envMapIntensity = envMapIntensity;

        // Set the new material
        child.material = newMaterial;
        child.castShadow = true;
        child.material.needsUpdate = true;
      }
    });
setLoading('xray-totem', false);
    // Cleanup
    return () => {
      allMeshRefs.current = {};
      originalMaterials.current = {};
    };
  }, [model, textures, materialMappings, envMapIntensity]);

  const handlePointerOver = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      // Set the hovered part and call the hover handler
      if (partName) {
        setHoveredPart(partName);
        handlePartHover(partName);
        console.log("Hovering over:", partName);
      }

      // Reset inactivity timer
      if (inactivityTimeoutRef.current) {
        clearTimeout(inactivityTimeoutRef.current);
        inactivityTimeoutRef.current = setTimeout(() => {
          if (!isMouseDownRef.current) {
            setShowSpheres(true);
          }
        }, 4000);
      }

      setShowSpheres(false);
      setShowFingerIcon(false);
    },
    [handlePartHover]
  );

  const handlePointerOut = useCallback(
    (e) => {
      e.stopPropagation();
      setanimation?.(false);
      setanimationsource?.(null);

      // Only clear hover state on non-touch devices
      const isTouchDevice =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

      if (!isTouchDevice) {
        setHoveredPart(null);
        sethoveredsphere?.(null);
        setTitle?.(null);
      }
    },
    [sethoveredsphere, setTitle, setanimation, setanimationsource]
  );

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      const partName = e.object.name;

      if (partName) {
        handlePartClick(partName);
      }

      setShowFingerIcon(false);
    },
    [handlePartClick]
  );

  // Function to toggle the model state (you need to define this)

  // Optimized scale computation
  const computedScale = useMemo(() => {
    return typeof scale === "number" ? [scale, scale, scale] : scale;
  }, [scale]);

  // Create a finger icon sprite material


  return (
    <group
      ref={ref}
      position={position}
      rotation={rotation}
      scale={computedScale}
      dispose={null}
    >
      {/* Model group with fade-in effect */}
      <group ref={modelGroupRef}>
        <primitive
          object={model}
          onPointerOver={handlePointerOver}
          onPointerOut={handlePointerOut}
          onClick={handleClick}
          castShadow
        />
      </group>


    </group>
  );
}
// NEW: Camera zoom control component
function CameraZoom() {
  const snap = useSnapshot(state);
  const { camera } = useThree();

  useEffect(() => {
    // Map the zoom level to a camera distance
    // Higher zoom level = closer camera (smaller z value)
    const minZ = 1.5; // Closest zoom
    const maxZ = 8; // Furthest zoom
    const zoomRange = maxZ - minZ;

    // Convert zoom level (10-200) to camera position
    // Inverted relationship: higher zoom level = closer camera
    const targetZ = maxZ - ((snap.zoomLevel - 10) / 190) * zoomRange;

    // Smoothly animate to the new position
    const duration = 0.3;
    const currentZ = camera.position.z;

    // Simple animation function using requestAnimationFrame
    let startTime = null;
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / (duration * 1000), 1);

      // Ease function (cubic ease-out)
      const easeProgress = 1 - Math.pow(1 - progress, 3);

      // Update camera position
      camera.position.z = currentZ + (targetZ - currentZ) * easeProgress;

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [snap.zoomLevel, camera]);

  return null;
}



const ToteMproduct = () => {
    const isTogglingRef = useRef(false);
  const debounceTimerRef = useRef(null);
  const isTogglingXrayRef = useRef(false);
    const { isLoading, setLoading, clearAll } = useLoadingManager();
  const [isUserAdjustingSlider, setIsUserAdjustingSlider] = useState(false);
  const snap = useSnapshot(state);
  const controlsRef = useRef();
  const lightRef = useRef();
  const fbxref = useRef();
  const [isOpen, setOpen] = useState(false);
  const [hoveredsphere, sethoveredsphere] = useState(null);
  const [xrayMode, setXrayMode] = useState(false);
  const bagGroupRef = useRef();
  const [bagRotation, setBagRotation] = useState({ x: 0, y: 0 });
  const [initialFbxRotation, setInitialFbxRotation] = useState({
    x: 0,
    y: -0.22,
    z: 0,
  });
    const toggleModel = useCallback(() => {
    console.log("Toggling model state");
    
    if (isTogglingRef.current) return;
    isTogglingRef.current = true;
    
    setTimeout(() => {
      setLoading('model-toggle', true);
      state.isOpen = !state.isOpen;
      console.log("Model state is now:", state.isOpen);
      isTogglingRef.current = false;
    }, 0);
  }, [setLoading]);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePosition, setLastMousePosition] = useState({ x: 0, y: 0 });
  const [showStrapVideo, setShowStrapVideo] = useState(false);
  const videoRef = useRef(null);
  const [clickcount, setClickCount] = useState(0);
  const [showAxes, setShowAxes] = useState(false);
  const [showRotationAxes, setShowRotationAxes] = useState(true);
  const [axesRadius, setAxesRadius] = useState(1);
  const [handlestraptab, sethandlestraptab] = useState(0);
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [touchDistance, setTouchDistance] = useState(null);
  const [isTwoFingerDrag, setIsTwoFingerDrag] = useState(false);
  const [lastTouchCenter, setLastTouchCenter] = useState({ x: 0, y: 0 });
  const [saturation, setSaturation] = useState(1.0);
  const [contrast, setContrast] = useState(1.0);
  const [modelOpacity, setModelOpacity] = useState(0);
  const [modelHasLoadedOnce, setModelHasLoadedOnce] = useState(false);
  const [title, setTitle] = useState(null);
  const [activeTab, setActiveTab] = useState(1);
  const [animationsource, setanimationsource] = useState(null);
  const [animation, setanimation] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isPanelActive, setIsPanelActive] = useState(false);

  const [selectedStraps, setSelectedStraps] = useState({
    shoulder: null,
    handle: null,
  });
  const [selectedColor, setSelectedColor] = useState(null);
  const [lastClickedStrap, setLastClickedStrap] = useState(null);
  const [showColorStraps, setShowColorStraps] = useState(false);
  const [showShoulderColorStraps, setShowShoulderColorStraps] = useState(false);
  // Use the inactivity hook with 4 second timeout
  const isInactive = useInactivityDetection(4000);
  const [highlightData, sethighlightdata] = useState(null);
  const [lastClickedShoulderStrap, setLastClickedShoulderStrap] =
    useState(null);
  const detailsPanelRef = useRef(null);
  const touchZoomTimeout = useRef(null);
  useEffect(() => {
    return () => {
      if (touchZoomTimeout.current) {
        clearTimeout(touchZoomTimeout.current);
      }
    };
  }, []);
  useEffect(() => {
    if (snap.isOpen) {
      // Start with opacity 0
      setModelOpacity(0);

      // Animate to full opacity
      const fadeIn = setTimeout(() => {
        setModelOpacity(1);
      }, 100);

      return () => clearTimeout(fadeIn);
    }
  }, [snap.isOpen]);
  const ensureBagAboveShadowPlane = (newPosition) => {
    // Find the shadow plane Y position - your shadow plane is at -1.5
    let shadowPlaneY = -0.7; // Default value based on your plane setup

    // Search for the shadow plane in the scene if needed
    if (bagGroupRef.current && bagGroupRef.current.parent) {
      bagGroupRef.current.parent.traverse((object) => {
        if (
          object.isMesh &&
          object.material &&
          object.material.type === "ShadowMaterial"
        ) {
          shadowPlaneY = object.position.y;
        }
      });
    }

    // Calculate current zoom scale
    const zoomFactor = snap.zoomLevel / 100;
    const zoomScale = Math.pow(1.5, zoomFactor);

    // Calculate bag's bottom based on its scale and center position
    const bagHeight = 0.5 * zoomScale; // Approximate half-height of bag
    const bagBottomY = newPosition.y - bagHeight;

    // Minimum distance to maintain above shadow plane
    const minBuffer = 0.05 * zoomScale; // Increased buffer for visibility

    // If bag would go below plane, adjust its Y position
    if (bagBottomY < shadowPlaneY + minBuffer) {
      newPosition.y = shadowPlaneY + minBuffer + bagHeight;
    }

    return newPosition;
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const deltaX = e.clientX - lastMousePosition.x;
      const deltaY = e.clientY - lastMousePosition.y;

      if (isShiftPressed) {
        // Move the bag position with limits
        const newPosX = bagGroupRef.current.position.x + deltaX * 0.005;
        const newPosY = bagGroupRef.current.position.y - deltaY * 0.005;

        // Create position object with limits
        const newPosition = clampPosition({
          x: newPosX,
          y: newPosY,
          z: bagGroupRef.current.position.z,
        });

        // Ensure bag stays above shadow plane
        const adjustedPosition = ensureBagAboveShadowPlane(newPosition);

        // Apply the adjusted position
        bagGroupRef.current.position.x = adjustedPosition.x;
        bagGroupRef.current.position.y = adjustedPosition.y;
        bagGroupRef.current.position.z = adjustedPosition.z;
      } else {
        // Rotate the bag
        setBagRotation({
          x: bagRotation.x + deltaY * 0.005,
          y: bagRotation.y + deltaX * 0.005,
        });
      }

      setLastMousePosition({
        x: e.clientX,
        y: e.clientY,
      });
    }
  };
  // Position limits for bag movement
  const positionLimits = {
    minX: -2,
    maxX: 2,
    minY: -0.5,
    maxY: 1.5,
  };

  // useEffect(() => {
  //   if (bagGroupRef.current) {
  //     applyBloomToMetalParts(bagGroupRef.current);
  //   }
  // }, [bagGroupRef.current, isOpen]);

  // Update the useEffect dependency array
  useEffect(() => {
    if (bagGroupRef.current) {
      bagGroupRef.current.traverse((child) => {
        if (child.isMesh && child.material) {
          const updateMaterial = (material) => {
            // Only update if the material has a map/texture
            if (material.map) {
              // Store original color if not already saved
              if (!material.userData.originalColor && material.color) {
                material.userData.originalColor = material.color.clone();
              }

              // Apply contrast and saturation via color adjustment
              if (material.userData.originalColor) {
                const originalColor = material.userData.originalColor;

                // Create a new color based on the original
                const newColor = originalColor.clone();

                // Apply contrast (moving color away from or toward gray)
                const contrastFactor = contrast;
                const gray = 0.5;
                newColor.r = Math.max(
                  0,
                  Math.min(1, gray + (newColor.r - gray) * contrastFactor)
                );
                newColor.g = Math.max(
                  0,
                  Math.min(1, gray + (newColor.g - gray) * contrastFactor)
                );
                newColor.b = Math.max(
                  0,
                  Math.min(1, gray + (newColor.b - gray) * contrastFactor)
                );

                // Apply saturation (mix between grayscale and color)
                if (saturation !== 1.0) {
                  const luminance =
                    0.299 * newColor.r +
                    0.587 * newColor.g +
                    0.114 * newColor.b;
                  newColor.r = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.r - luminance) * saturation
                    )
                  );
                  newColor.g = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.g - luminance) * saturation
                    )
                  );
                  newColor.b = Math.max(
                    0,
                    Math.min(
                      1,
                      luminance + (newColor.b - luminance) * saturation
                    )
                  );
                }

                // Apply the new color
                material.color.copy(newColor);
                material.needsUpdate = true;
              }
            }
          };

          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => updateMaterial(mat));
          } else {
            updateMaterial(child.material);
          }
        }
      });
    }
  }, [saturation, contrast]);

  const handleStrapChange = useCallback((strapType) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(() => {
      setLoading('strap-change', true);
      state.currentStrap = strapType;
    }, 100);
  }, [setLoading]);
  const handleXrayToggle = useCallback(() => {
    if (isTogglingXrayRef.current) return;
    isTogglingXrayRef.current = true;
    
    setTimeout(() => {
      setLoading('xray-toggle', true);
      setXrayMode(!xrayMode);
      isTogglingXrayRef.current = false;
    }, 0);
  }, [xrayMode, setLoading]);
  const handleZoomChange = useCallback(
    (value) => {
      const numericValue = Number(value);
      if (numericValue !== snap.zoomLevel) {
        // Inverting the zoom logic by using a different value range
        state.zoomLevel = numericValue;

        // Immediate slider feedback
        setIsUserAdjustingSlider(true);
        const timer = setTimeout(() => {
          setIsUserAdjustingSlider(false);
        }, 200);

        return () => clearTimeout(timer);
      }
    },
    [snap.zoomLevel]
  );

  // Refined wheel zoom handler
  const handleWheel = useCallback(
    (e) => {
      e.preventDefault();
      const zoomSpeed = 5;
      const currentZoom = snap.zoomLevel;

      // Invert the direction so scrolling down zooms out, up zooms in
      const zoomDelta = -Math.sign(e.deltaY) * zoomSpeed;
      const newZoom = Math.max(Math.min(185, currentZoom + zoomDelta), -25);

      if (newZoom !== currentZoom) {
        state.zoomLevel = newZoom;
        setIsUserAdjustingSlider(true);

        const timer = setTimeout(() => {
          setIsUserAdjustingSlider(false);
        }, 200);

        return () => clearTimeout(timer);
      }
    },
    [snap.zoomLevel]
  );

  // Clamps position within the defined limits
  const clampPosition = (position) => {
    console.log("!!!!", positionLimits.maxY);
    console.log("!!!!", position.y);
    return {
      x: Math.max(
        positionLimits.minX,
        Math.min(positionLimits.maxX, position.x)
      ),
      y: Math.max(
        positionLimits.minY,
        Math.min(positionLimits.maxY, position.y)
      ),
      z: position.z,
    };
  };

  // Mouse interaction handlers
  const handleMouseDown = (e) => {
    e.preventDefault(); // Preventem default behavior
    setIsDragging(true);
    setLastMousePosition({
      x: e.clientX,
      y: e.clientY,
    });
  };

  // const handleMouseMove = (e) => {
  //   if (isDragging) {
  //     const deltaX = e.clientX - lastMousePosition.x;
  //     const deltaY = e.clientY - lastMousePosition.y;

  //     if (isShiftPressed) {
  //       // Move the bag position with limits
  //       const newPosX = bagGroupRef.current.position.x + deltaX * 0.01;
  //       const newPosY = bagGroupRef.current.position.y - deltaY * 0.01;

  //       // Apply limits
  //       bagGroupRef.current.position.x = Math.max(positionLimits.minX, Math.min(positionLimits.maxX, newPosX));
  //       bagGroupRef.current.position.y = Math.max(positionLimits.minY, Math.min(positionLimits.maxY, newPosY));
  //     } else {
  //       // Rotate the bag
  //       setBagRotation({
  //         x: bagRotation.x + deltaY * 0.005,
  //         y: bagRotation.y + deltaX * 0.005,
  //       });
  //     }

  //     setLastMousePosition({
  //       x: e.clientX,
  //       y: e.clientY,
  //     });
  //   }
  // };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Calculate touch center point
  const getTouchCenter = (touches) => {
    if (touches.length === 2) {
      return {
        x: (touches[0].clientX + touches[1].clientX) / 2,
        y: (touches[0].clientY + touches[1].clientY) / 2,
      };
    }
    return { x: touches[0].clientX, y: touches[0].clientY };
  };

  // Touch event handlers for mobile with two-finger bag movement
  const handleTouchStart = (e) => {
    e.preventDefault(); // Prevent default behavior
    if (e.touches.length === 2) {
      // Two finger interaction - can be either zoom or movement
      const dx = e.touches[0].clientX - e.touches[1].clientX;
      const dy = e.touches[0].clientY - e.touches[1].clientY;
      setTouchDistance(Math.sqrt(dx * dx + dy * dy));

      // Track the center point between the two fingers for potential bag movement
      const center = getTouchCenter(e.touches);
      setLastTouchCenter(center);
      setIsTwoFingerDrag(true);

      // Disable OrbitControls when doing two-finger bag movement
      if (controlsRef.current) {
        controlsRef.current.enabled = false;
      }
    } else if (e.touches.length === 1) {
      // Single finger will rotate the bag (existing functionality)
      setIsDragging(true);
      setIsTwoFingerDrag(false);
      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });
    }
  };

  const handleTouchMove = (e) => {
    e.preventDefault(); // Prevent scrolling while interacting with the model

    if (e.touches.length === 2 && isTwoFingerDrag) {
      const currentCenter = getTouchCenter(e.touches);

      // Calculate current finger distance for potential pinch zoom
      const dx = e.touches[0].clientX - e.touches[1].clientX;
      const dy = e.touches[0].clientY - e.touches[1].clientY;
      const currentDistance = Math.sqrt(dx * dx + dy * dy);

      // Check if this is primarily a zoom gesture or a movement gesture
      if (touchDistance !== null) {
        const distanceDelta = Math.abs(currentDistance - touchDistance);
        const centerDelta = Math.sqrt(
          Math.pow(currentCenter.x - lastTouchCenter.x, 2) +
            Math.pow(currentCenter.y - lastTouchCenter.y, 2)
        );

        // If the distance between fingers changed significantly more than the center position,
        // treat as a zoom gesture
        if (distanceDelta > centerDelta * 1.5) {
          // Correct pinch zoom logic:
          // When fingers move apart (currentDistance > touchDistance), we want to zoom in (positive delta)
          // When fingers move together (currentDistance < touchDistance), we want to zoom out (negative delta)
          const zoomFactor = 0.2; // Adjust this value to control zoom sensitivity
          const delta = currentDistance - touchDistance;
          const zoomDelta = delta * zoomFactor;

          // Apply zoom using the same scale as the zoom slider (10-200 range)
          const newZoom = Math.max(
            10,
            Math.min(200, snap.zoomLevel + zoomDelta)
          );

          if (newZoom !== snap.zoomLevel) {
            state.zoomLevel = newZoom;
            setIsUserAdjustingSlider(true);

            clearTimeout(touchZoomTimeout.current);
            touchZoomTimeout.current = setTimeout(() => {
              setIsUserAdjustingSlider(false);
            }, 200);
          }
        } else {
          // Otherwise, treat as a movement gesture
          const deltaX = currentCenter.x - lastTouchCenter.x;
          const deltaY = currentCenter.y - lastTouchCenter.y;

          // Move the bag position
          if (bagGroupRef.current) {
            const newPosX = bagGroupRef.current.position.x + deltaX * 0.01;
            const newPosY = bagGroupRef.current.position.y - deltaY * 0.01;

            // Create position object with limits
            const newPosition = clampPosition({
              x: newPosX,
              y: newPosY,
              z: bagGroupRef.current.position.z,
            });

            // Ensure bag stays above shadow plane
            const adjustedPosition = ensureBagAboveShadowPlane(newPosition);

            // Apply the adjusted position
            bagGroupRef.current.position.x = adjustedPosition.x;
            bagGroupRef.current.position.y = adjustedPosition.y;
            bagGroupRef.current.position.z = adjustedPosition.z;
          }
        }
      }

      // Update touch tracking state
      setTouchDistance(currentDistance);
      setLastTouchCenter(currentCenter);
    } else if (e.touches.length === 1 && isDragging) {
      // Single finger rotation (existing functionality)
      const deltaX = e.touches[0].clientX - lastMousePosition.x;
      const deltaY = e.touches[0].clientY - lastMousePosition.y;

      // Rotate the bag
      setBagRotation({
        x: bagRotation.x + deltaY * 0.005,
        y: bagRotation.y + deltaX * 0.005,
      });

      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });
    }
  };

  const handleTouchEnd = (e) => {
    // Reset all touch interactions if no fingers are left on screen
    if (e.touches.length === 0) {
      setIsDragging(false);
      setIsTwoFingerDrag(false);
      setTouchDistance(null);

      // Re-enable OrbitControls when not interacting
      if (controlsRef.current) {
        controlsRef.current.enabled = true;
      }
    }
    // If we still have one finger, maintain single-finger rotation
    else if (e.touches.length === 1) {
      setIsTwoFingerDrag(false);
      setIsDragging(true);
      setLastMousePosition({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY,
      });

      // Re-enable OrbitControls when switching to single finger
      if (controlsRef.current) {
        controlsRef.current.enabled = true;
      }
    }
  };

  // Reset bag position button handler
  const resetBagPosition = () => {
    if (bagGroupRef.current) {
      // Find the shadow plane's Y position
      let shadowPlaneY = -0.7; // Default value based on your plane setup

      // Search for shadow plane
      if (bagGroupRef.current.parent) {
        bagGroupRef.current.parent.traverse((object) => {
          if (
            object.isMesh &&
            object.material &&
            object.material.type === "ShadowMaterial"
          ) {
            shadowPlaneY = object.position.y;
          }
        });
      }

      // Calculate current zoom scale
      const zoomFactor = snap.zoomLevel / 100;
      const zoomScale = Math.pow(1.5, zoomFactor);

      // Calculate minimum y position to stay above shadow plane
      const bagHeight = 0.5 * zoomScale; // Approximate half-height of bag
      const minBuffer = 0.01 * zoomScale;
      const minY = shadowPlaneY + minBuffer + bagHeight;

      // Reset bag position while respecting the shadow plane constraint
      bagGroupRef.current.position.x = 0;
      bagGroupRef.current.position.y = Math.max(0, minY);
      bagGroupRef.current.position.z = 0;
      setBagRotation({ x: 0, y: 0 });
    }
  };
  // useEffect(() => {
  //   if (!isMobile || !detailsPanelRef.current) return;

  //   const panel = detailsPanelRef.current;

  //   const handleTouchStart = () => {
  //     setIsPanelActive(true);
  //   };

  //   const handleTouchOutside = (e) => {
  //     if (panel && !panel.contains(e.target)) {
  //       setIsPanelActive(false);
  //     }
  //   };

  //   panel.addEventListener('touchstart', handleTouchStart);
  //   document.addEventListener('touchstart', handleTouchOutside);

  //   return () => {
  //     panel.removeEventListener('touchstart', handleTouchStart);
  //     document.removeEventListener('touchstart', handleTouchOutside);
  //   };
  // }, [isMobile, detailsPanelRef.current]);
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.matchMedia("(max-width: 768px)").matches);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);
  // Check if hovered part is a strap and control video visibility
  // useEffect(() => {
  //   // Check if the hoveredsphere contains the word "strap" or other strap-related keywords
  //   const isStrapHovered =
  //     hoveredsphere &&
  //     (hoveredsphere.toLowerCase().includes("strap") ||
  //       hoveredsphere.toLowerCase().includes("handle") ||
  //       hoveredsphere.toLowerCase().includes("metal"));

  //   setShowStrapVideo(isStrapHovered);

  //   // Play video when strap is hovered, pause when not
  //   if (isStrapHovered && videoRef.current) {
  //     videoRef.current
  //       .play()
  //       .catch((e) => console.log("Video play failed:", e));
  //   } else if (videoRef.current) {
  //     videoRef.current.pause();
  //   }
  // }, [hoveredsphere]);

  const attachmentPoints = [
    { name: "Cylinder002", position: [0, 0, 0], rotation: [0, 0, 0] },
  ];

  const handleColorChange = (newColors) => {
    console.log("New colors: for the fbx", newColors);
    state.colors = newColors;
  };

const handlestrapcolorchange = (strapcolor) => {
  setClickCount(clickcount + 1);
  if (strapcolor === "beige") {
    state.strapcolors.leather = "#D7CCBF";
    state.strapcolors.plastic = "#F2EEE8";
    state.strapcolors.seams = "#D0C6BB";
  }
  if (strapcolor === "black") {
    state.strapcolors.leather = "#232424";
    state.strapcolors.plastic = "#060606";
    state.strapcolors.seams = "#1B1B1C";
  }
  if (strapcolor === "red") {
    state.strapcolors.plastic = "#7A3E45";
    state.strapcolors.leather = "#680D18";
    state.strapcolors.seams = "#671E23";
  }
  if (strapcolor === "grey") {
    state.strapcolors.leather = "#6A7075";
    state.strapcolors.plastic = "#A1A7AC";
    state.strapcolors.seams = "#505457";
  }
};

const handlestrap2colorchange = (strapcolor) => {
  setClickCount(clickcount + 1);
  // console.error("setting the color of the shoulder strap",state.shoulderStrapColors);
  if (strapcolor === "beige") {
    state.shoulderStrapColors.leather = "#D7CCBF";
    state.shoulderStrapColors.plastic = "#F2EEE8";
    state.shoulderStrapColors.seams = "#D0C6BB";
  }
  if (strapcolor === "black") {
    state.shoulderStrapColors.leather = "#232424";
    state.shoulderStrapColors.plastic = "#060606";
    state.shoulderStrapColors.seams = "#1B1B1C";
  }
  if (strapcolor === "red") {
    state.shoulderStrapColors.plastic = "#7A3E45";
    state.shoulderStrapColors.leather = "#680D18";
    state.shoulderStrapColors.seams = "#671E23";
  }
  if (strapcolor === "grey") {
    state.shoulderStrapColors.leather = "#6A7075";
    state.shoulderStrapColors.plastic = "#2E2E2E";
    state.shoulderStrapColors.seams = "#505457";
  }
};


  const handlestrapopacity = (opacity) => {
    state.strapOpacity = opacity;
    console.log("strap opacity", state.strapOpacity);
  };

  // Add both mouse and touch event listeners
  useEffect(() => {
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("touchend", handleTouchEnd);
    window.addEventListener("touchmove", handleTouchMove, { passive: false });

    return () => {
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("touchend", handleTouchEnd);
      window.removeEventListener("touchmove", handleTouchMove);
    };
  }, [
    isDragging,
    isTwoFingerDrag,
    lastMousePosition,
    lastTouchCenter,
    bagRotation,
    touchDistance,
  ]);
  useEffect(() => {
    if (snap.isOpen) {
      // Once the model has been loaded/opened at least once
      setModelHasLoadedOnce(true);
    }
  }, [snap.isOpen]);
  useEffect(() => {
    const canvasContainer = document.querySelector(
      `.${styles.canvasContainer}`
    );
    if (canvasContainer) {
      canvasContainer.addEventListener("wheel", handleWheel, {
        passive: false,
      });
      return () => {
        canvasContainer.removeEventListener("wheel", handleWheel);
      };
    }
  }, [handleWheel]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Shift") {
        setIsShiftPressed(true);
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === "Shift") {
        setIsShiftPressed(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  // Thumb position for the slider
  const [isMobiles, setIsMobiles] = useState(window.innerWidth <= 576);

  useEffect(() => {
    const handleResize = () => {
      setIsMobiles(window.innerWidth <= 576);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Then use isMobile state to calculate thumb position
  const trackHeight = isMobiles ? 125 : 192;
  const thumbOffset = isMobiles ? 51 : 96;
  const thumbPosition =
    (1 - (snap.zoomLevel - 10) / 140) * trackHeight - thumbOffset;

  const resetStrapTab = () => {
    sethandlestraptab(0); // Resets the strap tab to 0
  };
  const [showTabs, setShowTabs] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const toggleTabManagerVisibility = () => {
    setShowTabs(!showTabs);
  };
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const openPopup = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  return (
    <>
{/* {isLoading && (
        // <div className={styles.loadingOverlay}>
          <Loaders redirectDelay={2000} />
        // </div>
      )} */}
      <div className={styles.container}>
        <div
          className={styles.canvasContainer}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          style={{
            cursor: isDragging ? "grabbing" : "grab",
            touchAction: "none",
          }}
          // camera={{ fov: 180 }}
        >
          <Canvas
            shadows
            gl={{
              physicallyCorrectLights: true,
              outputEncoding: THREE.sRGBEncoding,
              toneMapping: THREE.ACESFilmicToneMapping,
              toneMappingExposure: 0.4,
              shadowMap: {
                enabled: true,
                type: THREE.PCFSoftShadowMap,
              },
              antialias: true,
            }}
          >
            <Suspense fallback={null}>
              {/* Environment lighting from EXR */}
              <Environment
                preset="studio"
                background={false}
                intensity={0.05}
                opacity={0.1}
                envMapIntensity={0.5}
              />

              {/* Additional lights for better visualization */}
              {/* <spotLight
                ref={lightRef}
                position={[0, 0, 2]}
                intensity={1}
              /> */}
              {/* light on metal plate */}
              <directionalLight
                ref={lightRef}
                position={[2.5, 2.3, 1]}
                intensity={1}
                castShadow
                shadow-mapSize-width={4096}
                shadow-mapSize-height={4096}
                shadow-bias={-0.0005}
                shadow-radius={2}
              >
                {/* {lightRef.current && (
                  <DirectionalLightHelperr lightRef={lightRef} />
                )} */}
              </directionalLight>
              <directionalLight
                position={[4.6, 0.5, 2]}
                intensity={0.11}
                shadow-mapSize-width={4096}
                shadow-mapSize-height={4096}
                shadow-bias={-0.0005}
                shadow-radius={2}
              />
              {/* bottom light */}
              <directionalLight
                ref={lightRef}
                position={[0.2, -32, 1]}
                intensity={2}
              >
                {/* {lightRef.current && (
                  <DirectionalLightHelperr lightRef={lightRef} />
                )} */}
              </directionalLight>
              <directionalLight
                ref={lightRef}
                position={[0.2, +5, 0]}
                intensity={2}
              ></directionalLight>
              <mesh
                receiveShadow
                position={[0, -2.3, 0]}
                rotation={[-Math.PI / 2, 0, 0]}
              >
                <planeGeometry args={[1000, 1000]} />
                <shadowMaterial
                  color="#878787"
                  transparent={true}
                  opacity={0.7}
                />
              </mesh>

              <group
                ref={bagGroupRef}
                rotation={[
                  bagRotation.x,
                  bagRotation.y + initialFbxRotation.y,
                  snap.isOpen ? initialFbxRotation.z : 0,
                ]}
              >
{
  !xrayMode ? (
    !snap.isOpen ? (
      <FBXModelWithTexturesopen
        position={[0, -1.7, 0]}
        scale={snap.scale}
        setOpen={setOpen}
        setActiveTab={setActiveTab}
        sethoveredsphere={sethoveredsphere}
        xrayMode={xrayMode}
        setXrayMode={setXrayMode}
        castShadow
        receiveShadow
        ref={fbxref}
        opacity={modelOpacity}
        setTitle={setTitle}
        setanimationsource={setanimationsource}
        setanimation={setanimation}
              setLoading={setLoading}
              setShowTabs={setShowTabs}
      />
    ) : (
      <FBXModelWithTexturesclosed
        position={[0, -1.7, 0]}
        scale={snap.scale}
        setShowRotationAxes={setShowRotationAxes}
        setOpen={setOpen}
        setActiveTab={setActiveTab}
        sethoveredsphere={sethoveredsphere}
        xrayMode={xrayMode}
        setXrayMode={setXrayMode}
        castShadow
        receiveShadow
        ref={fbxref}
        opacity={modelOpacity}
        setTitle={setTitle}
        setanimationsource={setanimationsource}
        setanimation={setanimation}
              sethighlightdata={sethighlightdata}
                    setLoading={setLoading}
                                  setShowTabs={setShowTabs}
      />
    )
  ) : (
    <FBXModelWithTexturesclosedd
      position={[-0.0, -1.7, 0]}
      rotation={[0, 0, 0]}
      scale={snap.scale}
      setShowRotationAxes={setShowRotationAxes}
      setOpen={setOpen}
      setActiveTab={setActiveTab}
      sethoveredsphere={sethoveredsphere}
      setTitle={setTitle}
      xrayMode={xrayMode}
      setXrayMode={setXrayMode}
      castShadow
      receiveShadow
      ref={fbxref}
      opacity={modelOpacity}
      key="closed-model"
      setanimationsource={setanimationsource}
      setanimation={setanimation}
            setLoading={setLoading}
    />
  )
}


                <StrapWithTextures
                  type={snap.currentStrap}
                  attachmentPoints={attachmentPoints}
                  clickcount={clickcount}
                  sethoveredsphere={sethoveredsphere}
                  castShadow
                  receiveShadow
                  xrayMode={xrayMode}
                  selectedStraps={selectedStraps}
                        setLoading={setLoading}
                />
                {/* <RotationAxes radius={axesRadius} visible={showRotationAxes} /> */}
                {/* <OrbitControls
                enaablezoom={true}
                /> */}
              </group>
              <CameraZoom bagGroupRef={bagGroupRef} isDragging={isDragging} />
            </Suspense>
          </Canvas>
        </div>
        {showTabs && (
          <div className={styles.slider}>
            <div className={styles.verticalZoomSlider}>
              <p className={styles.zoomPercentage}>{`${Math.round(
                snap.zoomLevel + 25
              )} %`}</p>

              <div className={styles.sliderContainer}>
                <FaPlus size={14} className={styles.plusIcon} />

                <div className={styles.sliderTrackContainer}>
                  <div className={styles.sliderTrack}></div>

                  <input
                    type="range"
                    min="10"
                    max="200"
                    value={-(snap.zoomLevel - 185)}
                    onChange={(e) => handleZoomChange(Number(e.target.value))}
                    className={styles.sliderInput}
                    aria-label="Zoom level"
                  />

                  <div
                    className={styles.sliderThumb}
                    style={{ transform: `translateY(${thumbPosition}px)` }}
                  />
                </div>

                <FaMinus size={14} className={styles.minusIcon} />
              </div>
            </div>
            {/* 
            <button
              className={styles.resetStrapBtn}
              onClick={() => {
                handleStrapChange("none");
              }}
            >
              Remove strap
            </button>
            <button
              className={styles.resetPositionBtn}
              onClick={resetBagPosition}
            >
              Reset Position
            </button>
            <button
              className={styles.xreyBtn}
              onClick={() => {
                setXrayMode(!xrayMode);
              }}
            >
              Xray
            </button> */}

            {/* <div className={styles.controlButtons}>
            Control buttons remain unchanged
          </div> */}
            {/* <div>
              <button
                className={styles.resetPositionBtn}
                onClick={toggleTabManagerVisibility}
              >
                Show/Hide
              </button>
              <button
                className={styles.xreyBtn}
                onClick={() => {
                  setXrayMode(!xrayMode);
                }}
              >
                Xray
              </button>
            </div> */}
          </div>
        )}
      </div>
      {showTabs && (
        <div className={styles.controlBtnDivLeft}>
          <button
            className={styles.resetPositionBtn}
            onClick={toggleTabManagerVisibility}
          >
            Show/Hide
          </button>
          <button
            className={styles.xreyBtn}
  onClick={handleXrayToggle} 
          >
            Xray
          </button>
        </div>
      )}
      {!showTabs && (
        <button
          className={styles.controlBtn1}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button>
      )}
      <div className={styles.controlBtnDiv}>
        {showTabs && (
          <>
            {/* <div>
              <button
                className={styles.resetPositionBtn}
                onClick={resetBagPosition}
              >
                Reset
              </button>
            </div>

            <div>
              <button
                className={styles.xreyBtn}
                onClick={() => {
                  setXrayMode(!xrayMode);
                }}
              >
                Xray
              </button>
            </div> */}
            <div>
              <button
                className={styles.resetStrapBtn}
                onClick={() => {
                  // Determine which strap to remove based on priority
                  if (selectedStraps.handle !== "none") {
                    // Remove handle strap first
                    handleStrapChange("none");
                    setSelectedStraps((prev) => ({ ...prev, handle: "none" }));
                    setLastClickedStrap(null);
                  } else if (selectedStraps.shoulder !== "none") {
                    // Remove shoulder strap if handle is already removed
                    setSelectedStraps((prev) => ({
                      ...prev,
                      shoulder: "none",
                    }));
                    setLastClickedShoulderStrap(null);
                  } else {
                    // If both are already "none", reset everything
                    handleStrapChange("none");
                    setSelectedStraps({ shoulder: "none", handle: "none" });
                    setLastClickedStrap(null);
                    setLastClickedShoulderStrap(null);
                  }

                  // Reset color strap views
                  setShowColorStraps(false);
                  setShowShoulderColorStraps(false);
                }}
              >
                {(() => {
                  if (selectedStraps.handle !== "none") {
                    return "Remove handle strap";
                  } else if (selectedStraps.shoulder !== "none") {
                    return "Remove shoulder strap";
                  } else {
                    return "Remove strap";
                  }
                })()}
              </button>
            </div>
   <div className={styles.tooltipContainer}>
      <button
        onClick={() => setShowInfo(!showInfo)}
        className={`${styles.controlBtn} ${styles.customTooltip}`}
        data-tooltip="Hover over objects for details"
      >
        {showInfo ? "Hide Info" : "Show Info"}
      </button>
    </div>
            <div>
              <button className={styles.controlBtn} onClick={openPopup}>
                Product Images
              </button>
            </div>
          </>
        )}
        {/* <button
          className={styles.controlBtn}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button> */}
      </div>
      <div className={styles.showBtnDiv}>
        {showTabs && (
          <>
            {/* <div>
              <button
                className={styles.resetPositionBtn}
                onClick={resetBagPosition}
              >
                Reset
              </button>
            </div>
            <div>
              <button
                className={styles.xreyBtn}
                onClick={() => {
                  setXrayMode(!xrayMode);
                }}
              >
                Xray
              </button>
            </div> */}
            <div>
              <button
                className={styles.resetStrapBtn}
                onClick={() => {
                  // Determine which strap to remove based on priority
                  if (selectedStraps.handle !== "none") {
                    // Remove handle strap first
                    handleStrapChange("none");
                    setSelectedStraps((prev) => ({ ...prev, handle: "none" }));
                    setLastClickedStrap(null);
                  } else if (selectedStraps.shoulder !== "none") {
                    // Remove shoulder strap if handle is already removed
                    setSelectedStraps((prev) => ({
                      ...prev,
                      shoulder: "none",
                    }));
                    setLastClickedShoulderStrap(null);
                  } else {
                    // If both are already "none", reset everything
                    handleStrapChange("none");
                    setSelectedStraps({ shoulder: "none", handle: "none" });
                    setLastClickedStrap(null);
                    setLastClickedShoulderStrap(null);
                  }

                  // Reset color strap views
                  setShowColorStraps(false);
                  setShowShoulderColorStraps(false);
                }}
              >
                {(() => {
                  if (selectedStraps.handle !== "none") {
                    return "Remove handle strap";
                  } else if (selectedStraps.shoulder !== "none") {
                    return "Remove shoulder strap";
                  } else {
                    return "Remove strap";
                  }
                })()}
              </button>
            </div>
<div>
  <button
    onClick={() => setShowInfo(!showInfo)}
    className={styles.controlBtn}
    title="Hover over objects for details"
  >
    {showInfo ? "Hide Info" : "Show Info"}
  </button>
</div>
            <div>
              <button className={styles.controlBtn} onClick={openPopup}>
                Product Images
              </button>
            </div>
          </>
        )}
        {/* <button
          className={styles.controlBtn}
          onClick={toggleTabManagerVisibility}
          style={{ backgroundColor: "#f7ff0040" }}
        >
          Customise
        </button> */}
      </div>
      {showInfo && hoveredsphere && (
        <div className={styles.detailsPanelCon}>
          <div
            ref={detailsPanelRef}
            className={`${styles.detailsPanel} ${
              isMobile && isPanelActive ? styles.active : ""
            }`}
          >
            <button
              className={styles.closeButton}
              onClick={() => {
                sethoveredsphere?.(null);
                // setShowInfo(!showInfo);
              }}
              // onTouchEnd={(e) => {
              //   e.preventDefault();
              //   e.stopPropagation();
              // }}
            >
              &times;
            </button>
            <h3 dangerouslySetInnerHTML={{ __html: title }}></h3>
            <p dangerouslySetInnerHTML={{ __html: hoveredsphere }} />
            {animation && (
              <div className={styles.videoContainer}>
                <video
                  ref={videoRef}
                  className={styles.strapVideo}
                  width="50%"
                  autoPlay={true}
                  loop
                  muted
                  style={{ opacity: 0.7 }}
                >
                  <source src={animationsource} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
            )}
          </div>
        </div>
      )}
            {highlightData && (
        <div className={styles.detailsPanell}>
          <p dangerouslySetInnerHTML={{ __html: highlightData }} />
        </div>
      )}
      {showTabs && (
        <TabmanagerTotem
          onColorChange={handleColorChange}
          onStrapChange={handleStrapChange}
          handlestrapcolorchange={handlestrapcolorchange}
          handlestrap2colorchange={handlestrap2colorchange}
          strapopacity={handlestrapopacity}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          setanimationsource={setanimationsource}
          setanimation={setanimation}
          sethoveredsphere={sethoveredsphere}
          setSelectedStraps={setSelectedStraps}
                    xrayMode={xrayMode}
          selectedStraps={selectedStraps}
          setLastClickedStrap={setLastClickedStrap}
          lastClickedStrap={lastClickedStrap}
          setLastClickedShoulderStrap={setLastClickedShoulderStrap}
        />
      )}
      {isPopupOpen && <ImgsPopUp name={"totem"} onClose={closePopup} />}
      <ModalVideo
        channel="youtube"
        youtube={{ autoplay: 0 }}
        isOpen={isOpen}
        videoId="L61p2uyiMSo"
        onClose={() => setOpen(false)}
      />
    </>
  );
};
export default ToteMproduct;
