import { useState, useEffect, useRef } from 'react';

export function useInactivityDetection(timeout = 4000) {
  const [isInactive, setIsInactive] = useState(false);
  const inactivityTimeoutRef = useRef(null);
  const isInteractingRef = useRef(false); // Tracks mouse down or touch

  useEffect(() => {
    const handleInactivity = () => {
      if (!isInteractingRef.current) {
        setIsInactive(true);
      }
    };

    const resetInactivityTimeout = () => {
      clearTimeout(inactivityTimeoutRef.current);
      inactivityTimeoutRef.current = setTimeout(handleInactivity, timeout);
    };

    const handleUserActivity = () => {
      resetInactivityTimeout();
      setIsInactive(false);
    };

    const handleInteractionStart = () => {
      isInteractingRef.current = true;
      resetInactivityTimeout();
      setIsInactive(false);
    };

    const handleInteractionEnd = () => {
      isInteractingRef.current = false;
      resetInactivityTimeout();
    };

    // Mouse events
    window.addEventListener("mousemove", handleUserActivity);
    window.addEventListener("mousedown", handleInteractionStart);
    window.addEventListener("mouseup", handleInteractionEnd);
    
    // Keyboard events
    window.addEventListener("keydown", handleUserActivity);
    
    // Touch events for mobile
    window.addEventListener("touchstart", handleInteractionStart);
    window.addEventListener("touchmove", handleUserActivity);
    window.addEventListener("touchend", handleInteractionEnd);
    
    // Initialize the timeout
    resetInactivityTimeout();

    return () => {
      clearTimeout(inactivityTimeoutRef.current);
      
      // Clean up all event listeners
      window.removeEventListener("mousemove", handleUserActivity);
      window.removeEventListener("mousedown", handleInteractionStart);
      window.removeEventListener("mouseup", handleInteractionEnd);
      window.removeEventListener("keydown", handleUserActivity);
      window.removeEventListener("touchstart", handleInteractionStart);
      window.removeEventListener("touchmove", handleUserActivity);
      window.removeEventListener("touchend", handleInteractionEnd);
    };
  }, [timeout]);

  return isInactive;
}