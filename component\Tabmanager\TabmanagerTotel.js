import { useState, useEffect, useRef } from "react";
import styles from "./Tabmanager.module.scss";
import { HiOutlinePlusCircle } from "react-icons/hi";
import { IoBagHandleOutline } from "react-icons/io5";
import { useRouter } from "next/router";
import { FaLongArrowAltLeft, FaLongArrowAltRight } from "react-icons/fa";
import ScrollContainer from "react-indiana-drag-scroll";
import { LuMoveRight, LuMoveLeft } from "react-icons/lu";
import Link from "next/link";
import Loaders from "../Loader/Loaders";
import CartComponent from "../Home/Cart/CartPopup";

const TabmanagerTotel = ({
  onColorChange,
  onStrapChange,
  handlestrapcolorchange,
  handlestrap2colorchange, // Added this prop
  strapopacity,
  handlestraptab,
  sethandlestraptab,
  setanimation,
  setanimationsource,
  sethoveredsphere,
  setSelectedStraps,
  selectedStraps,
  lastClickedShoulderStrap,
  setLastClickedShoulderStrap,
  lastClickedStrap,
  setLastClickedStrap,
  activeTab,
  setActiveTab,
  xrayMode,
}) => {
  const [selectedColor, setSelectedColor] = useState("beige");
  const [showColorStraps, setShowColorStraps] = useState(false);
  const [handleStrapClickCount, setHandleStrapClickCount] = useState({});
  const [selectedColorStrap, setSelectedColorStrap] = useState("beige");
  const [selectStrap, setSelectStrap] = useState(null);
  const [showShoulderColorStraps, setShowShoulderColorStraps] = useState(false);
  const [selectedShoulderColorStrap, setSelectedShoulderColorStrap] =
    useState(null);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showLoader, setShowLoader] = useState(false);
  const [showLoader1, setShowLoader1] = useState(false);
  const [showLoader2, setShowLoader2] = useState(false);
  const [showLoader3, setShowLoader3] = useState(false);
  const [showLoader4, setShowLoader4] = useState(false);
  const [showLoader5, setShowLoader5] = useState(false);
  const [dynamicname, setdynamicname] = useState("Handle Straps");
  const [cartItems, setCartItems] = useState([]);

  // Add state to track individual strap colors
  const [strapColors, setStrapColors] = useState({
    handle: null,
    chain: null,
    shoulder: null,
  });

  const [currentBagConfig, setCurrentBagConfig] = useState({
    bagType: "TOTEL BAG",
    bagColor: "beige",
    strapType: [],
    strapColor: null,
    price: 6800.0,
  });

  const cartRef = useRef();

  const tabs = [
    { id: 1, name: "Colour", icon: "" },
    { id: 2, name: dynamicname, icon: "" },
    { id: 3, name: "Shoulder Strap", icon: "" },
    { id: 4, name: "Models", icon: "" },
    // { id: 5, name: "Add To Cart", icon: <IoBagHandleOutline /> },
  ];

  const [activeStrapType, setActiveStrapType] = useState(null);
  const router = useRouter();
  // const [activeTab, setActiveTab] = useState(1);

  const scrollRef = useRef(null);
  const touchStartX = useRef(0);
  const touchStartScrollLeft = useRef(0);

  useEffect(() => {
    const loadCart = () => {
      try {
        const savedCart = localStorage.getItem("cart");
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
          console.log("Loaded cart from localStorage:", parsedCart);
        }
      } catch (error) {
        console.error("Error loading cart:", error);
      }
    };
    loadCart();
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem("cart", JSON.stringify(cartItems));
      console.log("Saved cart to localStorage:", cartItems);
    } catch (error) {
      console.error("Error saving cart:", error);
    }
  }, [cartItems]);

  // Helper function to get default color for new straps
  const getDefaultStrapColor = (strapType) => {
    if (strapColors[strapType]) {
      return strapColors[strapType];
    }
    return currentBagConfig.bagColor;
  };

  // Helper function to get color name from hex value
  const getColorNameFromHex = (hexValue) => {
    const colorMap = {
      "#000000": "black",
      "#8a0e12": "red",
      "#ffe6d4": "beige",
      "#808080": "grey",
    };
    return colorMap[hexValue] || "beige";
  };

  // Helper function to get hex value from color name
  const getHexFromColorName = (colorName) => {
    const colorMap = {
      black: "#000000",
      red: "#8a0e12",
      beige: "#ffe6d4",
      grey: "#808080",
    };
    return colorMap[colorName] || "#ffe6d4";
  };

  const generateBagId = (config) => {
    const { bagType, bagColor } = config;
    return `${bagType.toLowerCase().replace(/\s+/g, "_")}_${bagColor}`;
  };

  const handleStrapRemovedFromCart = (removedStrapType) => {
    console.log("Strap removed from cart:", removedStrapType);

    const updatedStrapType = currentBagConfig.strapType.filter(
      (strap) => strap.type !== removedStrapType
    );

    updateBagConfig({
      strapType: updatedStrapType,
    });

    if (selectedStraps.handle === removedStrapType) {
      setSelectedStraps({
        ...selectedStraps,
        handle: null,
      });
    }
    if (selectedStraps.shoulder === removedStrapType) {
      setSelectedStraps({
        ...selectedStraps,
        shoulder: null,
      });
    }

    if (lastClickedStrap === removedStrapType) {
      setLastClickedStrap(null);
    }
    if (lastClickedShoulderStrap === removedStrapType) {
      setLastClickedShoulderStrap(null);
    }

    console.log("Updated config after strap removal:", {
      strapType: updatedStrapType,
      selectedStraps: selectedStraps,
    });
  };

  const getBagImage = (config) => {
    const { bagType, bagColor } = config;
    const colorMap = {
      black: "black",
      red: "red",
      beige: "beige",
      grey: "grey",
    };

    const bagTypeMap = {
      "MICRO BAG": "microbag",
      "PHONE POUCH": "pouch",
      "TOTE BAG": "totes",
      "PANIER BAG": "panier",
      "TOTEM BAG": "totem",
      "TOTEL BAG": "totel",
    };

    const bagFolder = bagTypeMap[bagType] || "totel";
    return `/colourbag/${bagFolder}/${colorMap[bagColor] || "beige"}.png`;
  };

  const getStrapDisplayName = (strapType) => {
const strapNames = {
  handle: "Handle Strap",
  metal: "Core-Link Chain™",
  ball: "Ball-Link Chain™",
  strapm: "Strap M",
  sidestrap3: "Strap V",
  logo: "Monogram Link Chain",
  logo1: "Flowlink Chain",
  shoulder: "Shoulder Strap",
};

    return strapNames[strapType] || strapType;
  };

  const calculatePrice = (config) => {
    let basePrice = 6800.0; // Totel bag base price

    if (config.strapType && config.strapType.length > 0) {
      const strapPrices = {
        handle: 200,
        chain: 350,
        shoulder: 300,
      };

      config.strapType.forEach((strap) => {
        if (strap.type) {
          basePrice += strapPrices[strap.type] || 0;
        }
      });
    }

    return basePrice;
  };

  const updateBagConfig = (updates) => {
    setCurrentBagConfig((prev) => {
      const newConfig = { ...prev, ...updates };
      newConfig.price = calculatePrice(newConfig);
      console.log("Updated bag config:", newConfig);
      return newConfig;
    });
  };

  const isStrapSelected = (strapType) => {
    return currentBagConfig.strapType.some((strap) => strap.type === strapType);
  };

  const removeStrapFromConfig = (strapType) => {
    const updatedStrapType = currentBagConfig.strapType.filter(
      (strap) => strap.type !== strapType
    );

    updateBagConfig({
      strapType: updatedStrapType,
    });

    setStrapColors((prev) => ({
      ...prev,
      [strapType]: null,
    }));

    if (selectedStraps.handle === strapType) {
      setSelectedStraps({
        ...selectedStraps,
        handle: null,
      });
    }
    if (selectedStraps.shoulder === strapType) {
      setSelectedStraps({
        ...selectedStraps,
        shoulder: null,
      });
    }

    console.log("Removed strap:", strapType);
  };

  const handleModelSelection = (newBagType, loaderFunction) => {
    console.log(
      "Changing bag type from",
      currentBagConfig.bagType,
      "to",
      newBagType
    );

    updateBagConfig({
      bagType: newBagType,
    });

    loaderFunction(true);
  };

  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartScrollLeft.current = scrollRef.current.scrollLeft;
  };

  const handleTouchMove = (e) => {
    if (scrollRef.current) {
      const touchX = e.touches[0].clientX;
      const touchDeltaX = touchX - touchStartX.current;
      scrollRef.current.scrollLeft = touchStartScrollLeft.current - touchDeltaX;
    }
  };

  const handleTouchEnd = () => {
    // No specific action needed on touch end
  };

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("touchstart", handleTouchStart);
      scrollContainer.addEventListener("touchmove", handleTouchMove);
      scrollContainer.addEventListener("touchend", handleTouchEnd);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("touchstart", handleTouchStart);
        scrollContainer.removeEventListener("touchmove", handleTouchMove);
        scrollContainer.removeEventListener("touchend", handleTouchEnd);
      }
    };
  }, []);

  const handleStrapMouseEnter = (strapType) => {
    setanimation(true);
    if (strapType === "handle") {
      sethoveredsphere?.(
        "<b>HANDLE STRAP</b><br><br>\
        A handle strap crafted in premium Epsom leather, reinforced for daily durability and finished with custom-engineered hinged hooks.<br>\
        Designed for a secure grip and seamless adaptability.<br><br>\
        Available in four colourways."
      );
      setanimation(false);
      setanimationsource("/ball1.mp4");
    } else if (strapType === "chain") {
      setanimation(true);
      sethoveredsphere?.(
        "<b>CORE-LINK CHAIN™</b><br><br>\
        Crafted from premium Rhodoid and engineered through CNC milling and laser cutting for precision articulation.<br>\
        Developed to balance flexibility with structural integrity, each fine hinge moves fluidly with the bag's weight while retaining a sculptural form.<br><br>\
        Lightweight, resilient, and tactile — a modular element designed for movement and form.<br><br>\
        Available in four colourways."
      );
      setanimationsource("/plastic.mp4");
    } else if (strapType === "shoulder") {
      sethoveredsphere?.(
        "<b>SHOULDER STRAP</b><br><br>\
        Crafted in reinforced Epsom leather, the Shoulder Strap offers versatile wear — over the shoulder or crossbody for hands-free carrying.<br>\
        Designed as the go-to strap for everyday, effortless, carefree movement.<br>\
        Available in four colourways."
      );
      setanimation(false);
    }
  };

  const handleStrapMouseLeave = () => {
    setanimation(false);
    sethoveredsphere(false);
  };

  const handleTabClick = (tabId) => {
    if (tabId === 1) {
      router.push("/");
      return;
    }
    setActiveTab(tabId);
    setShowColorStraps(false);
    setShowShoulderColorStraps(false);

    if (tabId === 3) {
      setActiveStrapType("handle");
      setSelectedStraps((prev) => ({
        ...prev,
        shoulder: null,
      }));
    } else if (tabId === 4) {
      setActiveStrapType("shoulder");
      setSelectedStraps((prev) => ({
        ...prev,
        handle: null,
      }));
      setLastClickedStrap(null);
      setSelectStrap(null);
    } else if (tabId !== 3 && tabId !== 4) {
      setLastClickedStrap(null);
      setLastClickedShoulderStrap(null);
    }
  };

  const handleColorChange = (color, colorName) => {
    setSelectedColor(colorName);
    updateBagConfig({ bagColor: colorName });

    if (colorName === "black") {
      const tempcolors = {
        plastic: "#060606",
        leather: "#232424",
        seams: "#1B1B1C",
      };
      onColorChange(tempcolors);
    }
    if (colorName === "red") {
      const tempcolors = {
        plastic: "#5F292F",
        leather: "#680D18",
        seams: "#671E23",
      };
      onColorChange(tempcolors);
    }
    if (colorName === "beige") {
      const tempcolors = {
        plastic: "#E1D8CF",
        leather: "#C7B8A9",
        seams: "#D0C6BB",
      };
      onColorChange(tempcolors);
    }
    if (colorName === "grey") {
      const tempcolors = {
        leather: "#6A7075",
        plastic: "#7C8287",
        seams: "#8E9499",
      };
      onColorChange(tempcolors);
    }
  };

  // Updated handleShoulderStrapClick - now uses handlestrap2colorchange
  const handleShoulderStrapClick = (value) => {
    if (lastClickedShoulderStrap === value) {
      // If clicking the same strap again, show color options
      setShowShoulderColorStraps(true);
      if (strapopacity) {
        strapopacity(1);
      }

      // Get the current color of this specific strap
      const currentStrap = currentBagConfig.strapType.find(
        (strap) => strap.type === value
      );
      const currentStrapColor =
        currentStrap?.color || getDefaultStrapColor(value);

      // Set the color state to this strap's current color
      setSelectedShoulderColorStrap(currentStrapColor);
      handlestrap2colorchange(currentStrapColor); // Use handlestrap2colorchange for shoulder straps
    } else {
      // For new strap selection - REPLACE any existing strap, don't add to it
      setSelectedStraps({
        ...selectedStraps,
        shoulder: value,
      });

      // CLEAR all existing straps and only add the new one
      const defaultColor = "beige";

      updateBagConfig({
        strapType: [{ type: value, color: defaultColor }], // Replace entire array with just this strap
      });

      // Update individual strap color tracking - clear others
      setStrapColors((prev) => ({
        handle: null,
        chain: null,
        shoulder: defaultColor,
      }));

      // Set the selected color to beige and show color options immediately
      setSelectedShoulderColorStrap(defaultColor);
      handlestrap2colorchange(defaultColor); // Use handlestrap2colorchange for shoulder straps
      setShowShoulderColorStraps(true); // Show colors immediately
      if (strapopacity) {
        strapopacity(1);
      }

      onStrapChange(value);
      setLastClickedShoulderStrap(value);
    }
  };

  // Fixed handleHandleStrapClick - uses handlestrapcolorchange
  const handleHandleStrapClick = (value) => {
    if (lastClickedStrap === value) {
      // If clicking the same strap again, show color options
      setShowColorStraps(true);
      if (strapopacity) {
        strapopacity(1);
      }

      // Get the current color of this specific strap
      const currentStrap = currentBagConfig.strapType.find(
        (strap) => strap.type === value
      );
      const currentStrapColor =
        currentStrap?.color || getDefaultStrapColor(value);

      // Set the color state to this strap's current color
      setSelectedColorStrap(currentStrapColor);
      handlestrapcolorchange(currentStrapColor); // Use handlestrapcolorchange for handle straps
    } else {
      // For new strap selection - REPLACE any existing strap, don't add to it
      setSelectedStraps({
        ...selectedStraps,
        handle: value,
      });

      // CLEAR all existing straps and only add the new one
      const defaultColor = "beige";

      updateBagConfig({
        strapType: [{ type: value, color: defaultColor }], // Replace entire array with just this strap
      });

      // Update individual strap color tracking - clear others
      setStrapColors((prev) => ({
        handle: defaultColor,
        chain: null,
        shoulder: null,
      }));

      // Set the selected color to beige and show color options immediately
      setSelectedColorStrap(defaultColor);
      handlestrapcolorchange(defaultColor); // Use handlestrapcolorchange for handle straps
      setShowColorStraps(true); // Show colors immediately
      if (strapopacity) {
        strapopacity(1);
      }

      setdynamicname(getStrapDisplayName(value));
      onStrapChange(value);
      setLastClickedStrap(value);
    }
  };

  useEffect(() => {
    console.log("handlestraptab changed:", handlestraptab);
    if (handlestraptab === 1) {
      setActiveTab(4);
    }
  }, [handlestraptab]);

  // Updated handleStrapColorChange - uses handlestrapcolorchange for handle straps
  const handleStrapColorChange = (colorValue, colorName) => {
    const finalColorName = colorName || getColorNameFromHex(colorValue);

    handlestrapcolorchange(finalColorName); // Use handlestrapcolorchange for handle straps
    setSelectedColorStrap(finalColorName);

    const strapToUpdate = selectStrap || lastClickedStrap;

    setStrapColors((prev) => ({
      ...prev,
      [strapToUpdate]: finalColorName,
    }));

    const updatedStrapType = currentBagConfig.strapType.map((strap) => {
      if (strap.type === strapToUpdate) {
        return { ...strap, color: finalColorName };
      }
      return strap;
    });

    updateBagConfig({
      strapType: updatedStrapType,
      strapColor: finalColorName,
    });
  };

  // Updated handleShoulderStrapColorChange - uses handlestrap2colorchange for shoulder straps
  const handleShoulderStrapColorChange = (colorValue, colorName) => {
    const finalColorName = colorName || getColorNameFromHex(colorValue);

    handlestrap2colorchange(finalColorName); // Use handlestrap2colorchange for shoulder straps
    setSelectedShoulderColorStrap(finalColorName);

    const strapToUpdate = selectedStraps.shoulder || lastClickedShoulderStrap;

    setStrapColors((prev) => ({
      ...prev,
      [strapToUpdate]: finalColorName,
    }));

    const updatedStrapType = currentBagConfig.strapType.map((strap) => {
      if (strap.type === strapToUpdate) {
        return { ...strap, color: finalColorName };
      }
      return strap;
    });

    updateBagConfig({
      strapType: updatedStrapType,
      strapColor: finalColorName,
    });
  };

  const handleStrap = [
    {
      id: 2,
      name: "Handle Straps 1",
      image: "/handlestrap/new/h3.png",
      type: "metal",
    },
  ];

  const handleColorStrap = [
    {
      id: 1,
      name: "Color Straps 1",
      image: "/handlestrap/new/h4.png",
      type: "metal",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 2,
      name: "Color Straps 2",
      image: "/handlestrap/new/h2.png",
      type: "metal",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 3,
      name: "Color Straps 3",
      image: "/handlestrap/new/h1.png",
      type: "metal",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 4,
      name: "Color Straps 4",
      image: "/handlestrap/new/h3.png",
      type: "metal",
      color: "beige",
      colorValue: "#ffe6d4",
    },
    {
      id: 5,
      name: "Color Straps 1",
      image: "/handlestrap/c1.png",
      type: "chain",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 6,
      name: "Color Straps 2",
      image: "/handlestrap/c2.png",
      type: "chain",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 7,
      name: "Color Straps 3",
      image: "/handlestrap/c3.png",
      type: "chain",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 8,
      name: "Color Straps 4",
      image: "/handlestrap/c4.png",
      type: "chain",
      color: "beige",
      colorValue: "#ffe6d4",
    },
  ];

  const shoulderStrap = [
    {
      id: 1,
      name: "Shoulder Strap 1",
      image: "/handlestrap/shoulderStrap/s3.png",
      type: "shoulder",
    },
  ];

  const shoulderColorStrap = [
    {
      id: 1,
      name: "Color Straps 1",
      image: "/handlestrap/shoulderStrap/s4.png",
      type: "shoulder",
      color: "red",
      colorValue: "#8a0e12",
    },
    {
      id: 2,
      name: "Color Straps 2",
      image: "/handlestrap/shoulderStrap/s2.png",
      type: "shoulder",
      color: "black",
      colorValue: "#000000",
    },
    {
      id: 3,
      name: "Color Straps 3",
      image: "/handlestrap/shoulderStrap/s1.png",
      type: "shoulder",
      color: "grey",
      colorValue: "#808080",
    },
    {
      id: 4,
      name: "Color Straps 4",
      image: "/handlestrap/shoulderStrap/s3.png",
      type: "shoulder",
      color: "beige",
      colorValue: "#ffe6d4",
    },
  ];

  const handleNextTab = () => {
    if (activeTab < tabs.length) {
      setActiveTab(activeTab + 1);
    }
  };

  const handlePrevTab = () => {
    if (showShoulderColorStraps) {
      setShowShoulderColorStraps(false);
    }
    if (showColorStraps) {
      setShowColorStraps(false);
    }
    if (activeTab > 1) {
      setActiveTab(activeTab - 1);
    }
  };

  const [isHovered, setIsHovered] = useState(false);

  const getTabDisplayName = () => {
    const currentTab = tabs[activeTab - 1];
    if (currentTab?.id === 2 && isHovered) {
      return "All Straps";
    }
    return currentTab?.name || "";
  };

  const handleAllStrapsClick = () => {
    setActiveTab(2);
    setIsHovered(false);
    setShowColorStraps(false);
  };

  const handleAddToCart = () => {
    console.log("=== ADD TO CART - ONE BAG ONLY ===");
    console.log("Current bag config:", currentBagConfig);

    if (!currentBagConfig || !currentBagConfig.bagType) {
      console.error("No valid bag configuration");
      alert("Please configure your bag first");
      return;
    }

    if (cartRef.current) {
      cartRef.current.addToCart(currentBagConfig);
    }

    setIsCartOpen(true);

    console.log("=== END ADD TO CART ===");
  };

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 4:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.modelOptions}>
              <div className={styles.modelOption}>
                <div
                  className={styles.modelImage1}
                  onClick={() =>
                    handleModelSelection("MICRO BAG", setShowLoader)
                  }
                >
                  <img src="/homebag/microbag/grey.png" alt="Micro Bag" />
                </div>
              </div>
              <div className={styles.modelOption}>
                <div
                  className={styles.modelImage1}
                  onClick={() =>
                    handleModelSelection("PHONE POUCH", setShowLoader1)
                  }
                >
                  <img src="/homebag/pouch/grey.png" alt="Phone Pouch" />
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() => handleModelSelection("TOTE BAG", setShowLoader2)}
              >
                <div className={styles.modelImage1}>
                  <img src="/homebag/totes/grey.png" alt="Tote Bag" />
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() =>
                  handleModelSelection("PANIER BAG", setShowLoader3)
                }
              >
                <div className={styles.modelImage1}>
                  <img src="/homebag/panier/grey.png" alt="Panier Bag" />
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() =>
                  handleModelSelection("TOTEM BAG", setShowLoader4)
                }
              >
                <div className={styles.modelImage1}>
                  <img src="/homebag/totem/grey.png" alt="Totem Bag" />
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() =>
                  handleModelSelection("TOTEL BAG", setShowLoader5)
                }
              >
                <div className={styles.modelImage1}>
                  <img src="/homebag/totel/grey.png" alt="Totel Bag" />
                </div>
              </div>
            </div>
          </div>
        );
      case 1:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.modelOptions}>
              <div
                className={styles.modelOption}
                onClick={() => handleColorChange("#000000", "black")}
              >
                <div className={styles.modelImage}>
                  {selectedColor !== "black" ? (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "black",
                      })}
                      alt="Black model"
                      className={
                        selectedColor === "black" ? styles.clicked : ""
                      }
                    />
                  ) : (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "black",
                      }).replace(".png", "1.png")}
                      alt="Black model"
                      className={
                        selectedColor === "black" ? styles.clicked : ""
                      }
                    />
                  )}
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() => handleColorChange("#8a0e12", "red")}
              >
                <div className={styles.modelImage}>
                  {selectedColor !== "red" ? (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "red",
                      })}
                      alt="red model"
                      className={selectedColor === "red" ? styles.clicked : ""}
                    />
                  ) : (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "red",
                      }).replace(".png", "1.png")}
                      alt="red model"
                      className={selectedColor === "red" ? styles.clicked : ""}
                    />
                  )}
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() => handleColorChange("#ffe6d4", "beige")}
              >
                <div className={styles.modelImage}>
                  {selectedColor !== "beige" ? (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "beige",
                      })}
                      alt="beige model"
                      className={
                        selectedColor === "beige" ? styles.clicked : ""
                      }
                    />
                  ) : (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "beige",
                      }).replace(".png", "1.png")}
                      alt="beige model"
                      className={
                        selectedColor === "beige" ? styles.clicked : ""
                      }
                    />
                  )}
                </div>
              </div>
              <div
                className={styles.modelOption}
                onClick={() => handleColorChange("#808080", "grey")}
              >
                <div className={styles.modelImage}>
                  {selectedColor !== "grey" ? (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "grey",
                      })}
                      alt="grey model"
                      className={selectedColor === "grey" ? styles.clicked : ""}
                    />
                  ) : (
                    <img
                      src={getBagImage({
                        ...currentBagConfig,
                        bagColor: "grey",
                      }).replace(".png", "1.png")}
                      alt="grey model"
                      className={selectedColor === "grey" ? styles.clicked : ""}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case 2:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.shoulderOption}>
              {showColorStraps&&!xrayMode  ? (
                <>
                  {handleColorStrap
                    .filter((e) => e.type === selectStrap)
                    .map((value) => (
                      <div
                        key={value.id}
                        className={`${styles.modelOption} ${
                          selectedColorStrap === value.color
                            ? styles.colorSelected
                            : ""
                        }`}
                        onClick={() =>
                          handleStrapColorChange(value.colorValue, value.color)
                        }
                      >
                        <div className={styles.shouderImage}>
                          <img
                            src={value.image}
                            alt={value.name}
                            className={
                              selectedColorStrap === value.color
                                ? styles.clicked
                                : ""
                            }
                          />
                        </div>
                      </div>
                    ))}
                </>
              ) : (
                handleStrap.map((val) => (
                  <div
                    key={val.id}
                    className={`${styles.modelOption} ${
                      isStrapSelected(val.type) ? styles.strapSelected : ""
                    }`}
                    onClick={() => {
                      handleHandleStrapClick(val.type);
                      setSelectStrap(val.type);
                      setShowColorStraps(true);
                      setanimation(false);
                    }}
                    onMouseEnter={() => handleStrapMouseEnter(val.type)}
                    onMouseLeave={handleStrapMouseLeave}
                  >
                    <div className={styles.shouderImage}>
                      <img
                        src={val.image}
                        alt={val.name}
                        className={
                          selectedStraps.handle === val.type
                            ? styles.clicked
                            : ""
                        }
                      />
                      {/* {isStrapSelected(val.type) && (
                        <div className={styles.selectedIndicator}>✓</div>
                      )} */}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        );
      case 3:
        return (
          <div className={styles.modelsContent}>
            <div className={styles.shoulderOption}>
              {showShoulderColorStraps ? (
                <>
                  <div className={styles.colorStrapsHeader}></div>
                  {shoulderColorStrap.map((value) => (
                    <div
                      key={value.id}
                      className={`${styles.modelOption} ${
                        selectedShoulderColorStrap === value.color
                          ? styles.colorSelected
                          : ""
                      }`}
                      onClick={() => {
                        handleShoulderStrapColorChange(
                          value.colorValue,
                          value.color
                        );
                      }}
                    >
                      <div className={styles.shouderImage}>
                        <img
                          src={value.image}
                          alt={value.name}
                          className={
                            selectedShoulderColorStrap === value.color
                              ? styles.clicked
                              : ""
                          }
                        />
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                shoulderStrap.map((val) => (
                  <div
                    key={val.id}
                    className={styles.modelOption}
                    onClick={() => handleShoulderStrapClick(val.type)}
                    onMouseEnter={() => handleStrapMouseEnter(val.type)}
                    onMouseLeave={handleStrapMouseLeave}
                  >
                    <div className={styles.shouderImage}>
                      <img
                        src={val.image}
                        alt={val.name}
                        className={
                          selectedStraps.shoulder === val.type
                            ? styles.clicked
                            : ""
                        }
                      />
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        );
      case 5:
        return (
          <div className={styles.cartContent}>
            <div className={styles.cartSummary}>
              <div className={styles.bagConfigSummary}>
                <h3>Your Configuration</h3>
                <div className={styles.configItem}>
                  <img
                    src={getBagImage(currentBagConfig)}
                    alt={currentBagConfig.bagType}
                    className={styles.bagPreview}
                  />
                </div>
                <div className={styles.configDetails}>
                  <p>
                    <strong>Bag:</strong> {currentBagConfig.bagType}
                  </p>
                  <p>
                    <strong>Color:</strong>{" "}
                    {currentBagConfig.bagColor.toUpperCase()}
                  </p>
                  {currentBagConfig.strapType &&
                    currentBagConfig.strapType.length > 0 && (
                      <div>
                        <p>
                          <strong>
                            Straps ({currentBagConfig.strapType.length}):
                          </strong>
                        </p>
                        {currentBagConfig.strapType.map((strap, index) => (
                          <div key={index} className={styles.strapConfigItem}>
                            <div
                              style={{
                                marginLeft: "20px",
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <div>
                                <p>• {getStrapDisplayName(strap.type)}</p>
                                {strap.color && (
                                  <p
                                    style={{
                                      marginLeft: "20px",
                                      fontSize: "0.9em",
                                      color: "#666",
                                    }}
                                  >
                                    Color: {strap.color.toUpperCase()}
                                  </p>
                                )}
                              </div>
                              <button
                                onClick={() =>
                                  removeStrapFromConfig(strap.type)
                                }
                                className={styles.removeStrapBtn}
                                style={{
                                  background: "#ff4444",
                                  color: "white",
                                  border: "none",
                                  borderRadius: "3px",
                                  padding: "2px 6px",
                                  fontSize: "10px",
                                  cursor: "pointer",
                                }}
                              >
                                Remove
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                </div>
                <div className={styles.priceDisplay}>
                  <h4>Total: ${currentBagConfig.price.toFixed(2)}</h4>
                  <small style={{ color: "#666", fontSize: "0.8em" }}>
                    {currentBagConfig.strapType.length} strap(s) included
                  </small>
                </div>
                <button
                  className={styles.addToCartButton}
                  onClick={handleAddToCart}
                >
                  Add to Cart{" "}
                  {currentBagConfig.strapType.length > 0 &&
                    `(${currentBagConfig.strapType.length} straps)`}
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {showLoader && <Loaders redirectTo="/microbag" redirectDelay={4000} />}
      {showLoader1 && <Loaders redirectTo="/phonepouch" redirectDelay={4000} />}
      {showLoader2 && <Loaders redirectTo="/totes" redirectDelay={4000} />}
      {showLoader3 && <Loaders redirectTo="/panierbag" redirectDelay={4000} />}
      {showLoader4 && <Loaders redirectTo="/totem" redirectDelay={4000} />}
      {showLoader5 && <Loaders redirectTo="/totel" redirectDelay={4000} />}

      <div className={styles.tabManagerContainer}>
        <div className={styles.subCon}>
          <div className={styles.tabNavigation}>
            <div className={styles.tabContent}>{renderTabContent()}</div>
          </div>

          <div ref={scrollRef} className={styles.scrollContainer}>
            <div className={styles.tabsContainer}>
              <p className={styles.configText}>The Maxi - Frame</p>
              <div className={styles.tabMainCon}>
                <button
                  className={styles.navButton}
                  aria-label="Previous"
                  onClick={handlePrevTab}
                >
                  <LuMoveLeft color="#d4d4d4" />
                </button>
                {activeTab > 0 && activeTab <= tabs.length && (
                  <div
                    className={`${styles.tab} ${
                      activeTab === tabs[activeTab - 1].id ? styles.active : ""
                    }`}
                    onClick={() => {
                      if (tabs[activeTab - 1]?.id === 2 && isHovered) {
                        handleAllStrapsClick();
                      } else {
                        handleTabClick(tabs[activeTab - 1].id);
                      }
                    }}
                    onMouseEnter={() => {
                      if (tabs[activeTab - 1]?.id === 2) {
                        setIsHovered(true);
                      }
                    }}
                    onMouseLeave={() => setIsHovered(false)}
                  >

                    <span className={styles.tabName}>
                                                                  {tabs[activeTab - 1].id}.
                      {getTabDisplayName()}
                    </span>
                    {tabs[activeTab - 1].icon}
                  </div>
                )}
                <button
                  className={styles.navButton}
                  aria-label="Next"
                  onClick={handleNextTab}
                >
                  <LuMoveRight color="#d4d4d4" />
                </button>
              </div>
              <button className={styles.cartBtn} onClick={toggleCart}>
                Cart (
                {cartItems.reduce((total, item) => total + item.quantity, 0)})
              </button>
            </div>
          </div>
        </div>
      </div>

      <button className={styles.addcartBtn} onClick={handleAddToCart}>
        Add To Cart
      </button>

      <CartComponent
        ref={cartRef}
        isCartOpen={isCartOpen}
        setIsCartOpen={setIsCartOpen}
        currentBagConfig={currentBagConfig}
        cartItems={cartItems}
        setCartItems={setCartItems}
        onStrapRemovedFromCart={handleStrapRemovedFromCart}
      />
    </>
  );
};

export default TabmanagerTotel;