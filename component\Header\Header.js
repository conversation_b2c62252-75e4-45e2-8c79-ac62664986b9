import React, { useState } from "react";
import style from "./Header.module.scss";
import { useRouter } from "next/router";
import Link from "next/link";

const Header = () => {

  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <div className={style.container}>
        <div
          className={style.subCon}
          // onClick={() => (window.location.href = "/")}
          onClick={() => router.push("/")}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className={style.logoContainer}>
            <img
              src="/logo.svg"
              alt="logo"
              className={`${style.logo} ${style.defaultLogo} ${
                isHovered ? style.hidden : ""
              }`}
            />
            <img
              src="/walihlogo.png"
              alt="logo"
              className={`${style.logo} ${style.hoverLogo} ${
                isHovered ? style.visible : ""
              }`}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;

