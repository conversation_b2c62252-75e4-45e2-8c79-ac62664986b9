import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { Text } from "@react-three/drei";
import Loader from "@/component/Loader/Loaders";

function Microopen({ position, scale }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/microopencomp.glb");
  const bagName = "Micro Bag";

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });

  return (
    <>
      {/* <Loader /> */}
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        dispose={null}
        // onClick={() => {
        //   window.location.href = "/microbag";
        // }}
      >
        <mesh
          geometry={nodes.Bottom_Plate.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Magnet_Closure.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object003.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder002.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
        <mesh
          geometry={nodes.Shape009.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Mirror.geometry} material={materials.Material} />
        <mesh
          geometry={nodes.Outer_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Middle_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object005.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object006.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape012.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape014.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape015.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape016.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape017.geometry}
          material={materials.Material}
        />
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={2.3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}

export default Microopen;
useGLTF.preload("/microopencomp.glb");
