import React, { ReactNode, useContext, useState } from "react";

export const PromptContext = React.createContext<{
  loginShow: any;
  setLoginShow?: any;
  userData: any;
  setUserData?: any;
  checkoutData: any;
  setCheckoutData?: any;
}>({
  loginShow: false,
  userData: null,
  checkoutData: null,
});
export const usePromptContext = () => useContext(PromptContext);

export const PromptProvider = ({ children }: { children: ReactNode }) => {
  const [loginShow, setLoginShow] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [checkoutData, setCheckoutData] = useState<any>(null);
  return (
    <PromptContext.Provider
      value={{
        setLoginShow,
        loginShow,
        setUserData,
        userData,
        checkoutData,
        setCheckoutData,
      }}
    >
      {children}
    </PromptContext.Provider>
  );
};
