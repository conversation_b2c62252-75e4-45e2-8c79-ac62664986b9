import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { Text } from "@react-three/drei";

function Phonepouch({ position, scale }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/phonepouchcomp.glb");
  const bagName = "Phone Pouch";

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });

  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        dispose={null}
        // onClick={() => {
        //   window.location.href = "/phonepouch";
        // }}
      >
        <mesh
          geometry={nodes.Bottom_Plate.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Magnet_Closure.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object003.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Cylinder002.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Box005.geometry} material={materials.Material} />
        <mesh
          geometry={nodes.Shape009.geometry}
          material={materials.Material}
        />
        <mesh geometry={nodes.Mirror.geometry} material={materials.Material} />
        <mesh
          geometry={nodes.Outer_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Inner_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Middle_Layer.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Object005.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape020.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape021.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape023.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape024.geometry}
          material={materials.Material}
        />
        <mesh
          geometry={nodes.Shape025.geometry}
          material={materials.Material}
        />
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={2.3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}

export default Phonepouch;

useGLTF.preload("/phonepouchcomp.glb");
