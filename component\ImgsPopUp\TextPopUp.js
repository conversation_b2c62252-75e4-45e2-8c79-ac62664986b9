import React from "react";
import styles from "./TextPopUp.module.scss";

const TextPopUp = ({
  onClose,
  title,
  hoveredsphere,
  animation,
  videoRef,
  animationsource,
}) => {
  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.container} onClick={(e) => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>
          &times;
        </button>

        <div className={styles.detailsPanel}>
          <h3 dangerouslySetInnerHTML={{ __html: title }}></h3>
          <p dangerouslySetInnerHTML={{ __html: hoveredsphere }} />
          {animation && (
            <div className={styles.videoContainer}>
              <video
                // ref={videoRef}
                className={styles.strapVideo}
                width="50%"
                autoPlay={true}
                loop
                muted
                style={{ opacity: 0.7 }}
              >
                <source src={animationsource} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextPopUp;
