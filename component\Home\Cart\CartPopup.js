import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import { FaTimes, FaShoppingBag } from "react-icons/fa";
import styles from "../../Home/Cart/CartComponent.module.scss";
import { useRouter } from "next/router";

const CartComponent = forwardRef(
  (
    {
      isCartOpen,
      setIsCartOpen,
      currentBagConfig,
      cartItems = [],
      setCartItems,
      onStrapRemovedFromCart,
    },
    ref
  ) => {

    const router = useRouter();

    // Ensure cartItems is always an array
    const safeCartItems = Array.isArray(cartItems) ? cartItems : [];

    // Generate unique ID for bags (bag type + color only, loop state is just orientation)
    const generateBagId = (config) => {
      const { bagType, bagColor } = config;
      return `${bagType.toLowerCase().replace(/\s+/g, "_")}_${bagColor}`;
    };

    // Get bag display name for cart
    const getBagDisplayName = (bagType) => {
      const bagDisplayMap = {
        "MICRO BAG": "The Micro - Frame ",
        "PHONE POUCH": "The Cell - Frame ",
        "TOTE BAG": "The Mono - Frame ",
        "PANIER BAG": "The Hunar - Frame ",
        "TOTEM BAG": "The Parcel - Frame ",
        "TOTEL BAG": "The Maxi - Frame ",
      };

      return bagDisplayMap[bagType] || bagType;
    };

    // Get bag image based on configuration
    const getBagImage = (config) => {
      const { bagType, bagColor } = config;
      const colorMap = {
        black: "black",
        red: "red",
        beige: "beige",
        grey: "grey",
      };

      const bagTypeMap = {
        "MICRO BAG": "microbag",
        "PHONE POUCH": "pouch",
        "TOTE BAG": "totes",
        "PANIER BAG": "panier",
        "TOTEM BAG": "totem",
        "TOTEL BAG": "totel",
      };

      const bagPath = bagTypeMap[bagType] || "microbag";
      const colorPath = colorMap[bagColor] || "beige";
      return `/colourbag/${bagPath}/${colorPath}.png`;
    };

    // Get strap image based on configuration
    const getStrapImage = (strapType, strapColor) => {
      if (!strapType) return null;

      const colorMap = {
        black: "black",
        red: "red",
        beige: "beige",
        grey: "grey",
        gold: "gold",
        silver: "silver",
      };

      const strapTypeMap = {
        handle: "handle",
        metal: "metal",
        ball: "ball",
        strapm: "strapm",
        sidestrap3: "sidestrap3", // Keep original for image paths
        logo: "logo",
        logo1: "logo1",
        shoulder: "shoulder",
      };

      const strapPath = strapTypeMap[strapType] || strapType;
      const color = colorMap[strapColor] || strapColor || "black";

      return `/straps/${strapPath}/${color}.png`;
    };

    // Helper function to normalize strap types (Strap V and Strap M are the same)
    const normalizeStrapType = (strapType) => {
      if (strapType === "sidestrap3" || strapType === "strapm") {
        return "strap M"; // Unified type for both orientations
      }
      return strapType;
    };

    // Helper function to check if strap is valid
    const isValidStrap = (strap) => {
      if (!strap) return false;
      if (typeof strap === "string") return strap.trim() !== "";
      if (typeof strap === "object" && strap.type) {
        return typeof strap.type === "string" && strap.type.trim() !== "";
      }
      return false;
    };

    // Helper function to filter valid straps
    const getValidStraps = (strapType) => {
      if (!strapType) return [];
      if (Array.isArray(strapType)) {
        return strapType.filter(isValidStrap);
      }
      return isValidStrap(strapType) ? [strapType] : [];
    };

    // Get strap display name - UPDATED to unify Strap V and Strap M
    const getStrapDisplayName = (strapType) => {
      // Return null for falsy values or empty strings
      if (
        !strapType ||
        strapType === "" ||
        strapType === null ||
        strapType === undefined
      ) {
        return null;
      }

      const strapKey =
        typeof strapType === "string"
          ? strapType
          : strapType?.type || strapType;

      // Return null if strapKey is still falsy after processing
      if (!strapKey || strapKey === "") {
        return null;
      }

      const strapNames = {
        handle: "Handle Strap",
        metal: "Core-Link Chain™",
        ball: "Ball-Link Chain™",
        strapm: "Strap", // Unified name
        sidestrap3: "Strap", // Unified name - same as strapm
        logo: "Monogram Link Chain",
        logo1: "Flowlink Chain",
        shoulder: "Shoulder Strap",
      };

      return strapNames[strapKey] || strapKey?.toUpperCase?.() || null;
    };

    // Get strap price based on type (updated to euros)
    const getStrapPrice = (strapType) => {
      const strapKey =
        typeof strapType === "string"
          ? strapType
          : strapType?.type || strapType;

      const strapPrices = {
        handle: 125,
        metal: 195,
        ball: 195,
        strapm: 125,
        sidestrap3: 125, // Same price as strapm
        logo: 195,
        logo1: 195,
        shoulder: 245,
      };

      return strapPrices[strapKey] || 1250;
    };

    // Calculate base bag price (updated to euros)
    const getBaseBagPrice = (bagType) => {
      const bagPrices = {
        "MICRO BAG": 745,
        "PHONE POUCH": 845,
        "TOTE BAG": 1350,
        "PANIER BAG": 1845,
        "TOTEM BAG": 1745,
        "TOTEL BAG": 2245,
      };

      return bagPrices[bagType];
    };

    // Calculate total price including straps with separate quantities (MODIFIED: First strap is free)
    const calculateItemPrice = (item) => {
      const baseBagPrice = getBaseBagPrice(item.bagType);
      const bagTotal = baseBagPrice * (item.bagQuantity || item.quantity || 1);

      let strapTotal = 0;

      if (
        Array.isArray(item.strapDisplayNames) &&
        item.strapDisplayNames.length > 0
      ) {
        // Group straps by NORMALIZED type to handle unified straps correctly
        const strapGroups = {};
        
        item.strapDisplayNames
          .filter(
            (strap) =>
              strap &&
              strap.name &&
              strap.name !== "UNKNOWN STRAP" &&
              strap.type
          )
          .forEach((strap) => {
            const normalizedType = normalizeStrapType(strap.type);
            if (!strapGroups[normalizedType]) {
              strapGroups[normalizedType] = [];
            }
            strapGroups[normalizedType].push(strap);
          });

        // Calculate pricing for each strap type
        Object.entries(strapGroups).forEach(([normalizedType, straps]) => {
          // Use the original type for pricing (strapm and sidestrap3 have same price anyway)
          const strapPrice = getStrapPrice(straps[0].type);
          const totalQuantity = straps.reduce((sum, strap) => sum + (strap.quantity || 1), 0);
          
          if (normalizedType === "shoulder") {
            // For shoulder straps: first one is free, rest are paid
            const paidQuantity = Math.max(0, totalQuantity - 1);
            strapTotal += strapPrice * paidQuantity;
          } else {
            // For all other straps: pay full price
            strapTotal += strapPrice * totalQuantity;
          }
        });
      } else if (item.strapType && isValidStrap(item.strapType)) {
        const strapPrice = getStrapPrice(item.strapType);
        const strapQuantity = item.strapQuantity || 1;
        
        if (normalizeStrapType(item.strapType) === "shoulder") {
          // For shoulder strap: first one is free, rest are paid
          const paidQuantity = Math.max(0, strapQuantity - 1);
          strapTotal = strapPrice * paidQuantity;
        } else {
          // For all other straps: pay full price
          strapTotal = strapPrice * strapQuantity;
        }
      }

      return bagTotal + strapTotal;
    };

    // Get pricing breakdown for display with separate quantities (MODIFIED: Show first strap as free)
    const getPricingBreakdown = (item) => {
      const baseBagPrice = getBaseBagPrice(item.bagType);
      const bagQuantity = item.bagQuantity || item.quantity || 1;

      const breakdown = {
        bagPrice: baseBagPrice,
        bagQuantity: bagQuantity,
        bagTotal: baseBagPrice * bagQuantity,
        straps: [],
        strapTotal: 0,
        total: baseBagPrice * bagQuantity,
      };

      if (
        Array.isArray(item.strapDisplayNames) &&
        item.strapDisplayNames.length > 0
      ) {
        // Group straps by NORMALIZED type and color for unified display
        const strapGroups = {};
        
        item.strapDisplayNames
          .filter(
            (strap) =>
              strap &&
              strap.name &&
              strap.name !== "UNKNOWN STRAP" &&
              strap.type
          )
          .forEach((strap) => {
            const normalizedType = normalizeStrapType(strap.type);
            const key = `${normalizedType}_${strap.color}`;
            if (!strapGroups[key]) {
              strapGroups[key] = {
                ...strap,
                type: normalizedType,
                name: getStrapDisplayName(normalizedType), // Use unified name
                totalQuantity: 0
              };
            }
            strapGroups[key].totalQuantity += (strap.quantity || 1);
          });

        // Process each strap group
        breakdown.straps = Object.values(strapGroups).map((strapGroup) => {
          const strapPrice = getStrapPrice(strapGroup.type === "strap" ? "strapm" : strapGroup.type); // Use strapm price for unified strap
          const totalQuantity = strapGroup.totalQuantity;
          
          let freeQuantity = 0;
          let paidQuantity = totalQuantity;
          
          if (strapGroup.type === "shoulder") {
            // For shoulder straps: first one is free
            freeQuantity = 1;
            paidQuantity = Math.max(0, totalQuantity - 1);
          }
          // For all other straps: no free quantity, pay full price
          
          const strapTotal = strapPrice * paidQuantity;

          return {
            name: strapGroup.name,
            color: strapGroup.color,
            type: strapGroup.type,
            unitPrice: strapPrice,
            quantity: totalQuantity,
            freeQuantity: freeQuantity,
            paidQuantity: paidQuantity,
            total: strapTotal,
          };
        });
        
        breakdown.strapTotal = breakdown.straps.reduce(
          (total, strap) => total + strap.total,
          0
        );
      } else if (item.strapType && isValidStrap(item.strapType)) {
        const normalizedType = normalizeStrapType(item.strapType);
        const strapDisplayName = getStrapDisplayName(normalizedType);
        if (strapDisplayName) {
          const strapPrice = getStrapPrice(item.strapType);
          const strapQuantity = item.strapQuantity || 1;
          
          let freeQuantity = 0;
          let paidQuantity = strapQuantity;
          
          if (normalizedType === "shoulder") {
            // For shoulder strap: first one is free
            freeQuantity = 1;
            paidQuantity = Math.max(0, strapQuantity - 1);
          }
          // For all other straps: no free quantity, pay full price
          
          const strapTotal = strapPrice * paidQuantity;

          breakdown.straps = [
            {
              name: strapDisplayName,
              color: item.strapColor,
              type: normalizedType,
              unitPrice: strapPrice,
              quantity: strapQuantity,
              freeQuantity: freeQuantity,
              paidQuantity: paidQuantity,
              total: strapTotal,
            },
          ];
          breakdown.strapTotal = strapTotal;
        }
      }

      breakdown.total = breakdown.bagTotal + breakdown.strapTotal;
      return breakdown;
    };

    // Mapping functions for Shopify checkout (existing code)
    const getBagVariantId = (bagType, bagColor) => {
      const bagVariantMap = {
        // MICRO BAG (from Micro product)
        "MICRO BAG_beige": "gid://shopify/ProductVariant/57826551431517",
        "MICRO BAG_black": "gid://shopify/ProductVariant/57826551464285", 
        "MICRO BAG_red": "gid://shopify/ProductVariant/57826551497053",
        "MICRO BAG_grey": "gid://shopify/ProductVariant/57826551529821",

        // PHONE POUCH (from Pouch product)
        "PHONE POUCH_beige": "gid://shopify/ProductVariant/57826552643933",
        "PHONE POUCH_black": "gid://shopify/ProductVariant/57826552676701",
        "PHONE POUCH_red": "gid://shopify/ProductVariant/57826552709469", 
        "PHONE POUCH_grey": "gid://shopify/ProductVariant/57826552742237",

        // TOTE BAG (from Tote-S product)
        "TOTE BAG_beige": "gid://shopify/ProductVariant/57826553790813",
        "TOTE BAG_black": "gid://shopify/ProductVariant/57826553823581",
        "TOTE BAG_red": "gid://shopify/ProductVariant/57826553856349",
        "TOTE BAG_grey": "gid://shopify/ProductVariant/57826553889117",

        // TOTEM BAG (from Tote-M product)  
        "TOTEM BAG_beige": "gid://shopify/ProductVariant/57826555003229",
        "TOTEM BAG_black": "gid://shopify/ProductVariant/57826555035997",
        "TOTEM BAG_red": "gid://shopify/ProductVariant/57826555068765",
        "TOTEM BAG_grey": "gid://shopify/ProductVariant/57826555101533",

        // TOTEL BAG (from Tote-L product)
        "TOTEL BAG_beige": "gid://shopify/ProductVariant/57826556281181", 
        "TOTEL BAG_black": "gid://shopify/ProductVariant/57826556313949",
        "TOTEL BAG_red": "gid://shopify/ProductVariant/57826556346717",
        "TOTEL BAG_grey": "gid://shopify/ProductVariant/57826556379485",

        // PANIER BAG (from Full Tote Bag product)
        "PANIER BAG_beige": "gid://shopify/ProductVariant/57826557100381",
        "PANIER BAG_black": "gid://shopify/ProductVariant/57826557133149", 
        "PANIER BAG_red": "gid://shopify/ProductVariant/57826557165917",
        "PANIER BAG_grey": "gid://shopify/ProductVariant/57826557198685",
      };

      const key = `${bagType}_${bagColor}`;
      const variantId = bagVariantMap[key];

      console.log(`🎒 Bag Variant Lookup:`, {
        bagType,
        bagColor,
        key,
        variantId,
        found: !!variantId,
      });

      if (!variantId) {
        console.warn(`❌ No variant ID found for bag: ${key}`);
      }

      return variantId || "gid://shopify/ProductVariant/PLACEHOLDER_BAG_ID";
    };

    const getStrapVariantId = (strapType, strapColor) => {
      const strapVariantMap = {
        // Handle Hand Strap
        handle_black: "gid://shopify/ProductVariant/57826623455581",
        handle_grey: "gid://shopify/ProductVariant/57826623488349", 
        handle_beige: "gid://shopify/ProductVariant/57826623521117",
        handle_red: "gid://shopify/ProductVariant/57826623553885",

        // Metal Hand Strap  
        metal_black: "gid://shopify/ProductVariant/57826565423453",
        metal_grey: "gid://shopify/ProductVariant/57826565456221",
        metal_beige: "gid://shopify/ProductVariant/57826565488989", 
        metal_red: "gid://shopify/ProductVariant/57826565521757",

        // Ball Hand Strap
        ball_black: "gid://shopify/ProductVariant/57826562539869",
        ball_grey: "gid://shopify/ProductVariant/57826562572637",
        ball_beige: "gid://shopify/ProductVariant/57826562605405",
        ball_red: "gid://shopify/ProductVariant/57826562638173",

        // Unified Strap (both strapm and sidestrap3 use same variant)
        strapm_black: "gid://shopify/ProductVariant/57826558542173", 
        strapm_grey: "gid://shopify/ProductVariant/57826558574941",
        strapm_beige: "gid://shopify/ProductVariant/57826558607709",
        strapm_red: "gid://shopify/ProductVariant/57826558640477",
        
        // Map sidestrap3 to same variants as strapm
        sidestrap3_black: "gid://shopify/ProductVariant/57826558542173",
        sidestrap3_grey: "gid://shopify/ProductVariant/57826558574941",
        sidestrap3_beige: "gid://shopify/ProductVariant/57826558607709", 
        sidestrap3_red: "gid://shopify/ProductVariant/57826558640477",

        // Logo Hand Strap
        logo_black: "gid://shopify/ProductVariant/57826626994525",
        logo_grey: "gid://shopify/ProductVariant/57826627027293",
        logo_beige: "gid://shopify/ProductVariant/57826627060061",
        logo_red: "gid://shopify/ProductVariant/57826627092829",

        // Logo-One Hand Strap (logo1)
        logo1_black: "gid://shopify/ProductVariant/57826628403549",
        logo1_grey: "gid://shopify/ProductVariant/57826628436317", 
        logo1_beige: "gid://shopify/ProductVariant/57826628469085",
        logo1_red: "gid://shopify/ProductVariant/57826628501853",

        // Designer Strap (shoulder) - Note: Different color variants
        shoulder_black: "gid://shopify/ProductVariant/57826560541021", // Maps to white   
        shoulder_grey: "gid://shopify/ProductVariant/57826560475485",  // Maps to silver  
        shoulder_beige: "gid://shopify/ProductVariant/57826560442717", // Maps to rose gold
        shoulder_red: "gid://shopify/ProductVariant/57826560508253",   // Maps to pink    
      };

      const key = `${strapType}_${strapColor}`;
      const variantId = strapVariantMap[key];

      console.error(`🔍 Strap Variant Lookup:`, {
        strapType,
        strapColor, 
        key,
        variantId,
        found: !!variantId,
      });

      if (!variantId) {
        console.warn(`❌ No variant ID found for strap: ${key}`);
        console.error(
          "Available keys:",
          Object.keys(strapVariantMap).filter((k) => k.startsWith(strapType))
        );
      }

      return variantId || "gid://shopify/ProductVariant/PLACEHOLDER_STRAP_ID";
    };

    // Transform cart items to Shopify format with separate quantities
    const transformCartToShopifyFormatWithQuantities = () => {
      return safeCartItems.map((item, index) => {
        const customPayload = {
          custom: {
            model: getBagVariantId(item.bagType, item.bagColor),
            modelQuantity: item.bagQuantity || item.quantity || 1,
            strap: [],
            strapQuantities: [],
            itemId: item.id,
            bagType: item.bagType,
            bagColor: item.bagColor,
            loopState: item.loopState,
            unitPrice: calculateItemPrice(item),
            totalPrice: calculateItemPrice(item),
          },
        };

        // Only process valid straps
        if (item.strapDisplayNames && item.strapDisplayNames.length > 0) {
          // Group by normalized type and color for Shopify
          const strapGroups = {};
          
          item.strapDisplayNames
            .filter(
              (strap) =>
                strap &&
                strap.name &&
                strap.name !== "UNKNOWN STRAP" &&
                strap.type
            )
            .forEach((strap) => {
              const normalizedType = normalizeStrapType(strap.type);
              const key = `${normalizedType}_${strap.color}`;
              if (!strapGroups[key]) {
                strapGroups[key] = {
                  type: strap.type, // Keep original type for variant lookup
                  color: strap.color,
                  quantity: 0
                };
              }
              strapGroups[key].quantity += (strap.quantity || 1);
            });

          // Add grouped straps to payload
          Object.values(strapGroups).forEach((group) => {
            const strapId = getStrapVariantId(group.type, group.color);
            customPayload.custom.strap.push(strapId);
            customPayload.custom.strapQuantities.push(group.quantity);
          });
        } else if (item.strapType && isValidStrap(item.strapType)) {
          const strapId = getStrapVariantId(item.strapType, item.strapColor);
          customPayload.custom.strap.push(strapId);
          customPayload.custom.strapQuantities.push(item.strapQuantity || 1);
        }

        return customPayload;
      });
    };

    // Handle proceed to checkout with separate quantities
    const handleProceedToCheckout = async () => {
      if (safeCartItems.length === 0) {
        alert("Your cart is empty!");
        return;
      }

      try {
        const checkoutData = transformCartToShopifyFormatWithQuantities();

        console.error(
          "Sending checkout data with separate quantities:",
          JSON.stringify(checkoutData, null, 2)
        );

        const summary = {
          totalItems: safeCartItems.length,
          totalBags: safeCartItems.reduce(
            (sum, item) => sum + (item.bagQuantity || item.quantity || 1),
            0
          ),
          totalStraps: safeCartItems.reduce((sum, item) => {
            if (item.strapDisplayNames) {
              return (
                sum +
                item.strapDisplayNames
                  .filter(
                    (strap) =>
                      strap &&
                      strap.name &&
                      strap.name !== "UNKNOWN STRAP" &&
                      strap.type
                  )
                  .reduce(
                    (strapSum, strap) => strapSum + (strap.quantity || 1),
                    0
                  )
              );
            } else if (item.strapType && isValidStrap(item.strapType)) {
              return sum + (item.strapQuantity || 1);
            }
            return sum;
          }, 0),
          totalValue: getTotal(),
          itemBreakdown: safeCartItems.map((item) => ({
            id: item.id,
            bagType: item.bagType,
            bagColor: item.bagColor,
            bagQuantity: item.bagQuantity || item.quantity || 1,
            straps:
              item.strapDisplayNames?.filter(
                (strap) =>
                  strap &&
                  strap.name &&
                  strap.name !== "UNKNOWN STRAP" &&
                  strap.type
              ) ||
              (item.strapType && isValidStrap(item.strapType)
                ? [
                    {
                      type: item.strapType,
                      color: item.strapColor,
                      quantity: item.strapQuantity || 1,
                    },
                  ]
                : []),
            totalPrice: calculateItemPrice(item),
          })),
        };
        console.log("Checkout Summary with Separate Quantities:", summary);

        const response = await fetch(
          "https://showroom-api.walimohammedbarrech.com/user/meet-cart",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(checkoutData),
          }
        );

        try {
          if (response.ok) {
            const result = await response.json(); // Parse only once
            console.error("Checkout successful:", result);

            const checkoutUrl = result?.data?.checkoutUrl;
            if (checkoutUrl) {
              router.push(checkoutUrl);
              // window.location.href = checkoutUrl; // Redirect to checkout
            } else {
              console.error("checkoutUrl not found in response data");
            }

            setCartItems([]);
            setIsCartOpen(false);

            const totalBags = summary.totalBags;
            const totalStraps = summary.totalStraps;

            alert(
              `Successfully created checkout with ${totalBags} bag${totalBags > 1 ? "s" : ""} and ${totalStraps} strap${totalStraps > 1 ? "s" : ""}!`
            );
          } else {
            const errorData = await response.text(); // Use .text() if not OK
            throw new Error(
              `HTTP error! status: ${response.status}, message: ${errorData}`
            );
          }
        } catch (error) {
          console.error("Checkout error:", error);
          alert("There was an error processing your checkout. Please try again.");
        }

      } catch (error) {
        console.error("Checkout error:", error);
        alert("There was an error processing your checkout. Please try again.");
      }
    };

    // Update bag quantity
    const updateBagQuantity = (id, newQuantity) => {
      if (newQuantity === 0) {
        setCartItems(safeCartItems.filter((item) => item.id !== id));
      } else {
        setCartItems(
          safeCartItems.map((item) =>
            item.id === id
              ? { ...item, bagQuantity: newQuantity, quantity: newQuantity }
              : item
          )
        );
      }
    };

    // Update individual strap quantity
    const updateStrapQuantity = (itemId, strapIndex, newQuantity) => {
      if (newQuantity === 0) {
        removeStrapFromItem(itemId, strapIndex);
        return;
      }

      setCartItems(
        safeCartItems.map((item) => {
          if (item.id === itemId) {
            const updatedItem = { ...item };

            if (
              updatedItem.strapDisplayNames &&
              updatedItem.strapDisplayNames[strapIndex]
            ) {
              updatedItem.strapDisplayNames = [
                ...updatedItem.strapDisplayNames,
              ];
              updatedItem.strapDisplayNames[strapIndex] = {
                ...updatedItem.strapDisplayNames[strapIndex],
                quantity: newQuantity,
              };
            } else if (
              !updatedItem.strapDisplayNames &&
              updatedItem.strapType &&
              strapIndex === 0
            ) {
              updatedItem.strapQuantity = newQuantity;
            }

            return updatedItem;
          }
          return item;
        })
      );
    };

    const removeItem = (id) => {
      setCartItems(safeCartItems.filter((item) => item.id !== id));
    };

    const getTotal = () => {
      return safeCartItems.reduce(
        (total, item) => total + calculateItemPrice(item),
        0
      );
    };

    const getTotalItemsCount = () => {
      return safeCartItems.reduce((total, item) => {
        const bagQuantity = item.bagQuantity || item.quantity || 1;
        let strapQuantity = 0;

        if (item.strapDisplayNames) {
          strapQuantity = item.strapDisplayNames
            .filter(
              (strap) =>
                strap &&
                strap.name &&
                strap.name !== "UNKNOWN STRAP" &&
                strap.type
            )
            .reduce((sum, strap) => sum + (strap.quantity || 1), 0);
        } else if (item.strapType && isValidStrap(item.strapType)) {
          strapQuantity = item.strapQuantity || 1;
        }

        return total + bagQuantity + strapQuantity;
      }, 0);
    };

    const handleCloseCart = (e) => {
      e.stopPropagation();
      e.preventDefault();
      setIsCartOpen(false);
    };

    const handleRequestOrder = () => {
      if (safeCartItems.length === 0) {
        alert("Your cart is empty!");
        return;
      }

      const orderSummary = safeCartItems.map((item) => ({
        id: item.id,
        bagType: item.bagType,
        bagColor: item.bagColor,
        bagQuantity: item.bagQuantity || item.quantity || 1,
        strapType: item.strapType,
        strapColor: item.strapColor,
        strapQuantity: item.strapQuantity,
        strapDisplayNames: item.strapDisplayNames?.filter(
          (strap) =>
            strap && strap.name && strap.name !== "UNKNOWN STRAP" && strap.type
        ),
        loopState: item.loopState,
        totalPrice: calculateItemPrice(item),
        pricingBreakdown: getPricingBreakdown(item),
      }));

      console.log("Order Summary with Separate Quantities:", orderSummary);
      alert(
        `Order requested! Total: €${getTotal().toFixed(
          2
        )} for ${getTotalItemsCount()} total items`
      );
    };

    const toggleCart = (e) => {
      e.stopPropagation();
      e.preventDefault();
      setIsCartOpen(!isCartOpen);
    };

    // Remove individual strap from cart item
    const removeStrapFromItem = (itemId, strapIndex) => {
      setCartItems(
        safeCartItems.map((item) => {
          if (item.id === itemId) {
            const updatedItem = { ...item };
            let removedStrapType = null;

            if (
              Array.isArray(updatedItem.strapType) &&
              updatedItem.strapType.length > 0
            ) {
              if (
                updatedItem.strapDisplayNames &&
                updatedItem.strapDisplayNames[strapIndex]
              ) {
                removedStrapType =
                  updatedItem.strapDisplayNames[strapIndex].type;
              } else if (updatedItem.strapType[strapIndex]) {
                removedStrapType =
                  updatedItem.strapType[strapIndex].type ||
                  updatedItem.strapType[strapIndex];
              }

              updatedItem.strapType = updatedItem.strapType.filter(
                (_, index) => index !== strapIndex
              );
              if (updatedItem.strapDisplayNames) {
                updatedItem.strapDisplayNames =
                  updatedItem.strapDisplayNames.filter(
                    (_, index) => index !== strapIndex
                  );
              }

              if (updatedItem.strapType.length === 0) {
                updatedItem.strapType = [];
                updatedItem.strapDisplayNames = [];
                updatedItem.strapColor = null;
              }
            } else if (updatedItem.strapType && strapIndex === 0) {
              removedStrapType = updatedItem.strapType;
              updatedItem.strapType = [];
              updatedItem.strapColor = null;
              updatedItem.strapDisplayNames = [];
              updatedItem.strapQuantity = 0;
            }

            if (
              removedStrapType &&
              typeof onStrapRemovedFromCart === "function"
            ) {
              console.log(
                "Calling onStrapRemovedFromCart with:",
                removedStrapType
              );
              onStrapRemovedFromCart(removedStrapType);
            }

            return updatedItem;
          }
          return item;
        })
      );
    };

    // Add straps to existing bag or create new bag with separate quantities - UPDATED WITH UNIFIED STRAP HANDLING
    const addStrapsToBag = (bagConfig) => {
      console.log("=== ADD TO CART - WITH UNIFIED STRAP HANDLING ===");
      console.log("Bag config received:", bagConfig);

      if (!bagConfig) {
        console.log("No bag config available");
        return;
      }

      const bagId = generateBagId(bagConfig);
      console.log("Generated bag ID:", bagId);

      const existingItemIndex = safeCartItems.findIndex(
        (item) => item.id === bagId
      );

      const validStraps = getValidStraps(bagConfig.strapType);
      console.log("Valid straps:", validStraps);

      if (existingItemIndex !== -1) {
        console.log("Found existing bag, updating...");
        const existingItem = safeCartItems[existingItemIndex];
        const existingStraps = existingItem.strapDisplayNames || [];

        let updatedStraps = [...existingStraps];

        validStraps.forEach((newStrap) => {
          const strapType = newStrap.type || newStrap;
          const strapColor = newStrap.color || bagConfig.strapColor;
          const normalizedType = normalizeStrapType(strapType);

          // Only proceed if we have a valid strap type
          if (!isValidStrap(strapType)) return;

          // Look for existing strap by NORMALIZED type and color
          const existingStrapIndex = updatedStraps.findIndex(
            (existingStrap) =>
              normalizeStrapType(existingStrap.type) === normalizedType &&
              existingStrap.color === strapColor
          );

          if (existingStrapIndex !== -1) {
            // Increment quantity of existing strap
            updatedStraps[existingStrapIndex] = {
              ...updatedStraps[existingStrapIndex],
              quantity: (updatedStraps[existingStrapIndex].quantity || 1) + 1,
            };
          } else {
            // Add new strap with quantity 1
            const strapDisplayName = getStrapDisplayName(normalizedType);
            if (strapDisplayName) {
              updatedStraps.push({
                name: strapDisplayName,
                color: strapColor,
                type: strapType, // Keep original type for image/variant purposes
                image: getStrapImage(strapType, strapColor),
                quantity: 1,
              });
            }
          }
        });

        const updatedCartItems = [...safeCartItems];
        updatedCartItems[existingItemIndex] = {
          ...existingItem,
          bagType: bagConfig.bagType,
          bagColor: bagConfig.bagColor,
          loopState: bagConfig.loopState,
          strapType: validStraps,
          strapDisplayNames: updatedStraps,
          image: getBagImage(bagConfig),
          dateAdded: new Date().toISOString(),
        };

        setCartItems(updatedCartItems);
        console.log("Updated existing bag with unified strap handling");
      } else {
        console.log("Creating new bag item with unified strap handling...");

        // Create default shoulder strap for new bags
        const createDefaultShoulderStrap = (bagColor) => {
          const shoulderColorMap = {
            black: "black",
            grey: "grey",
            beige: "beige",
            red: "red",
          };

          const shoulderColor = shoulderColorMap[bagColor] || bagColor;
          
          return {
            name: getStrapDisplayName("shoulder"),
            color: shoulderColor,
            type: "shoulder",
            image: getStrapImage("shoulder", shoulderColor),
            quantity: 1,
          };
        };

        // Create straps array starting with default shoulder strap
        const defaultShoulderStrap = createDefaultShoulderStrap(bagConfig.bagColor);
        let allStraps = [defaultShoulderStrap];

        // Add any additional straps from the configuration with unified handling
        const additionalStraps = validStraps
          .map((strap) => {
            const strapType = strap.type || strap;
            const strapColor = strap.color || bagConfig.strapColor;
            const normalizedType = normalizeStrapType(strapType);
            const displayName = getStrapDisplayName(normalizedType);

            // Only create strap object if we have a valid display name
            if (!displayName) return null;

            // Don't duplicate shoulder strap if it's already in the config
            if (normalizedType === "shoulder") {
              // Update the existing shoulder strap quantity instead
              const existingShoulderIndex = allStraps.findIndex(s => 
                normalizeStrapType(s.type) === "shoulder" && s.color === strapColor
              );
              if (existingShoulderIndex !== -1) {
                allStraps[existingShoulderIndex].quantity += 1;
                return null; // Don't add as separate strap
              }
            }

            // Check if we already have this unified strap type and color
            const existingStrapIndex = allStraps.findIndex(s => 
              normalizeStrapType(s.type) === normalizedType && s.color === strapColor
            );
            
            if (existingStrapIndex !== -1) {
              // Increment existing unified strap quantity
              allStraps[existingStrapIndex].quantity += 1;
              return null;
            }

            return {
              name: displayName,
              color: strapColor,
              type: strapType, // Keep original for image/variant purposes
              image: getStrapImage(strapType, strapColor),
              quantity: 1,
            };
          })
          .filter(Boolean); // Remove null entries

        // Combine default shoulder strap with additional straps
        allStraps = [...allStraps, ...additionalStraps];

        const cartItem = {
          id: bagId,
          bagType: bagConfig.bagType,
          bagColor: bagConfig.bagColor,
          loopState: bagConfig.loopState,
          bagQuantity: 1, // Start with 1 bag
          strapType: ["shoulder", ...validStraps], // Include shoulder in strapType array
          strapColor: bagConfig.strapColor,
          strapQuantity: 1, // Default strap quantity for legacy support
          image: getBagImage(bagConfig),
          strapDisplayNames: allStraps,
          quantity: 1, // Legacy support
          dateAdded: new Date().toISOString(),
        };

        console.log("Cart item created with unified strap handling:", cartItem);
        const newCartItems = [...safeCartItems, cartItem];
        setCartItems(newCartItems);
        console.log("Added new bag with unified strap handling");
      }

      console.log("=== END ADD TO CART ===");
    };

    const refreshCart = () => {
      console.log("Manually refreshing cart...");
      console.log("Current cart items:", safeCartItems);
    };

    useImperativeHandle(ref, () => ({
      addToCart: addStrapsToBag,
      getCartItemsCount: () => getTotalItemsCount(),
      openCart: () => setIsCartOpen(true),
      closeCart: () => setIsCartOpen(false),
    }));

    console.log("Cart Component - Current items:", safeCartItems.length);

    return (
      <>
        {isCartOpen && (
          <div className={styles.cartOverlay}>
            <div className={styles.backgroundOverlay} onClick={toggleCart} />

            <div className={styles.cartPanel}>
              <div className={styles.cartContent}>
                {/* Header */}
                <div className={styles.header}>
                  <h2 className={styles.title}>
                    Cart ({getTotalItemsCount()})
                  </h2>
                  <button
                    onClick={handleCloseCart}
                    className={styles.closeButton}
                    aria-label="Close cart"
                  >
                    <FaTimes />
                  </button>
                </div>

                {safeCartItems.length === 0 ? (
                  <div className={styles.emptyCart}>
                    <FaShoppingBag size={48} color="#ccc" />
                    <p>Your cart is empty</p>

                    {/* DEBUG: Show current config info */}
                    {currentBagConfig && (
                      <div
                        style={{
                          fontSize: "12px",
                          color: "#666",
                          margin: "10px 0",
                        }}
                      >
                        <p>Current config available:</p>
                        <p>
                          Bag: {getBagDisplayName(currentBagConfig.bagType)}
                        </p>
                        <p>Color: {currentBagConfig.bagColor}</p>
                        <p>
                          Straps:{" "}
                          {getValidStraps(currentBagConfig.strapType)?.length ||
                            0}
                        </p>
                      </div>
                    )}

                    {currentBagConfig && (
                      <button
                        onClick={() => addStrapsToBag(currentBagConfig)}
                        className={styles.addCurrentBagBtn}
                        style={{
                          marginTop: "10px",
                          padding: "8px 16px",
                          background: "#007bff",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                        }}
                      >
                        Add Current Configuration
                      </button>
                    )}

                    <button
                      onClick={refreshCart}
                      className={styles.refreshCartBtn}
                      style={{
                        marginTop: "10px",
                        padding: "8px 16px",
                        background: "#6c757d",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                      }}
                    >
                      Refresh Cart
                    </button>
                  </div>
                ) : (
                  <>
                    <div className={styles.itemsList}>
                      {safeCartItems.map((item) => {
                        const pricingBreakdown = getPricingBreakdown(item);

                        return (
                          <div key={item.id} className={styles.cartItem}>
                            <div className={styles.itemImages}>
                              <div className={styles.bagImageContainer}>
                                <img
                                  src={item.image}
                                  alt={`${getBagDisplayName(item.bagType)} in ${
                                    item.bagColor
                                  }`}
                                  className={styles.bagImage}
                                  onError={(e) => {
                                    e.target.src = "/homebag/micro1.png";
                                  }}
                                />
                              </div>
                            </div>

                            <div className={styles.itemActions}>
                              {/* Enhanced Pricing Breakdown with unified strap display */}
                              <div className={styles.priceBreakdown}>
                                <div className={styles.bagPrice}>
                                  <span className={styles.priceLabel}>
                                    {getBagDisplayName(item.bagType)}(
                                    {item.bagColor?.toUpperCase()}) (€
                                    {pricingBreakdown.bagPrice.toFixed(
                                      2
                                    )} × {pricingBreakdown.bagQuantity}):
                                  </span>
                                  <span className={styles.priceValue}>
                                    €{pricingBreakdown.bagTotal.toFixed(2)}
                                  </span>
                                </div>

                                {pricingBreakdown.straps.length > 0 && (
                                  <div className={styles.strapPrices}>
                                    {pricingBreakdown.straps.map(
                                      (strap, index) => (
                                        <div
                                          key={index}
                                          className={styles.strapPrice}
                                        >
                                          <div
                                            className={styles.strapPriceInfo}
                                          >
                                            <span className={styles.priceLabel}>
                                              {strap.name}
                                              {strap.color &&
                                                ` (${strap.color.toUpperCase()})`}{" "}
                                              {strap.freeQuantity > 0 && (
                                                <span style={{ color: 'green', fontWeight: 'bold' }}>
                                                  ({strap.freeQuantity} FREE{strap.paidQuantity > 0 ? ` + ${strap.paidQuantity} × €${strap.unitPrice.toFixed(2)}` : ''})
                                                </span>
                                              )}
                                              {strap.freeQuantity === 0 && (
                                                <span>
                                                  (€{strap.unitPrice.toFixed(2)} × {strap.quantity})
                                                </span>
                                              )}:
                                            </span>
                                            <span className={styles.priceValue}>
                                              €{strap.total.toFixed(2)}
                                            </span>
                                          </div>
                                          <button
                                            onClick={() =>
                                              removeStrapFromItem(
                                                item.id,
                                                index
                                              )
                                            }
                                            className={
                                              styles.removeStrapPriceBtn
                                            }
                                            aria-label={`Remove ${strap.name}`}
                                            title={`Remove ${strap.name} from pricing`}
                                          >
                                            <FaTimes size={8} />
                                          </button>
                                        </div>
                                      )
                                    )}
                                  </div>
                                )}

                                <div className={styles.totalPrice}>
                                  <span className={styles.priceLabel}>
                                    Configuration Total:
                                  </span>
                                  <span className={styles.priceValue}>
                                    €{pricingBreakdown.total.toFixed(2)}
                                  </span>
                                </div>
                              </div>

                              <button
                                onClick={() => removeItem(item.id)}
                                className={styles.removeBtn}
                                aria-label="Remove entire configuration"
                                title="Remove entire configuration"
                              >
                                <FaTimes size={12} /> Remove All
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <div className={styles.notice}>
                      <strong>Important Notice:</strong>
                      <p>
                        Each bag comes with 1 FREE tonal shoulder strap included. Additional straps are charged separately.
                      </p>
                      <p>
                        Each bag is handcrafted to order in our Berlin atelier,
                        ensuring the highest quality and craftsmanship. Please
                        allow 3-5 weeks for production and delivery.
                      </p>
                      <p>
                        All straps and customizations are carefully crafted to
                        match your selected configuration. Quantities are per
                        item type.
                      </p>
                    </div>

                    <div className={styles.cartSummary}>
                      <div className={styles.summaryRow}>
                        <span>Total Items ({getTotalItemsCount()}):</span>
                        <span>
                          {safeCartItems.reduce(
                            (sum, item) =>
                              sum + (item.bagQuantity || item.quantity || 1),
                            0
                          )}{" "}
                          bags
                          {safeCartItems.reduce((sum, item) => {
                            if (item.strapDisplayNames) {
                              return (
                                sum +
                                item.strapDisplayNames
                                  .filter(
                                    (strap) =>
                                      strap &&
                                      strap.name &&
                                      strap.name !== "UNKNOWN STRAP" &&
                                      strap.type
                                  )
                                  .reduce(
                                    (strapSum, strap) =>
                                      strapSum + (strap.quantity || 1),
                                    0
                                  )
                              );
                            } else if (
                              item.strapType &&
                              isValidStrap(item.strapType)
                            ) {
                              return sum + (item.strapQuantity || 1);
                            }
                            return sum;
                          }, 0) > 0 && (
                            <>
                              {" + "}
                              {safeCartItems.reduce((sum, item) => {
                                if (item.strapDisplayNames) {
                                  return (
                                    sum +
                                    item.strapDisplayNames
                                      .filter(
                                        (strap) =>
                                          strap &&
                                          strap.name &&
                                          strap.name !== "UNKNOWN STRAP" &&
                                          strap.type
                                      )
                                      .reduce(
                                        (strapSum, strap) =>
                                          strapSum + (strap.quantity || 1),
                                        0
                                      )
                                  );
                                } else if (
                                  item.strapType &&
                                  isValidStrap(item.strapType)
                                ) {
                                  return sum + (item.strapQuantity || 1);
                                }
                                return sum;
                              }, 0)}{" "}
                              straps
                            </>
                          )}
                        </span>
                      </div>
                      <div className={styles.summaryRow}>
                        <span>Subtotal:</span>
                        <span>€ {getTotal().toFixed(2)}</span>
                      </div>
                      <div className={styles.summaryRow}>
                        <span>Taxes & Shipping:</span>
                        <span>NOT INCLUDED</span>
                      </div>
                    </div>

                    <div className={styles.total}>
                      <div className={styles.totalLabel}>Total</div>
                      <div className={styles.totalAmount}>
                        € {getTotal().toFixed(2)}
                      </div>
                    </div>

                    <div className={styles.actions}>
                      <button onClick={toggleCart} className={styles.closeBtn}>
                        CONTINUE SHOPPING
                      </button>
                      <button
                        onClick={handleProceedToCheckout}
                        className={styles.requestBtn}
                      >
                        REQUEST ORDER
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </>
    );
  }
);

CartComponent.displayName = "CartComponent";

export default CartComponent;