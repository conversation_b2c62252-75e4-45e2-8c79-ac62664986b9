// CartComponent.module.scss - Enhanced styling for proper cart display

.cartContainer {
  position: relative;
}

.cartOverlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  overflow: hidden;
}

.backgroundOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.25);
}

.cartPanel {
  position: absolute;
  right: 10px;
  bottom: 55px;
  // height: 50%;
  width: 384px;
  box-shadow: -10px 0 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateX(0);
  transition: transform 0.3s ease-in-out;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.cartContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Arial", sans-serif;
  color: #333;
}

.header {
  padding: 12px 16px;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 16px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #333;
  }
}

.emptyCart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #fff;
  text-align: center;
  flex: 1;

  p {
    margin: 16px 0;
    color: #666;
    font-size: 14px;
  }
}

.addCurrentBagBtn {
  padding: 10px 20px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
  color: #333;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #000;
    background-color: #f9f9f9;
  }
}

.itemsList {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #fff;
  max-height: 250px;
  overflow-y: auto;
}

.cartItem {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  align-items: flex-start;
  gap: 16px;

  &:last-child {
    border-bottom: none;
  }
}

.itemImages {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.bagImageContainer {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #eee;
}

.bagImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.itemDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 0;
}

.itemName {
  font-weight: bold;
  font-size: 15px;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-bottom: 4px;
}

.itemCategory,
.itemFeature {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  margin-bottom: 2px;
}

.strapDetails {
  margin-top: 8px;

  .strapTitle {
    font-weight: 600;
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.2px;
  }

  .strapItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
    padding: 4px 0;

    .strapInfo {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .strapName {
      font-size: 11px;
      color: #333;
      font-weight: 500;
    }

    .strapColor {
      font-size: 10px;
      color: #666;
    }

    .strapActions {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .strapThumbnail {
      width: 24px;
      height: 24px;
      object-fit: contain;
      border-radius: 3px;
      border: 1px solid #eee;
    }

    .removeStrapBtn {
      background: #ff4444;
      border: none;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: white;
      font-size: 8px;

      &:hover {
        background: #cc0000;
      }
    }
  }
}

.itemId {
  font-size: 9px;
  color: #999;
  margin-top: 6px;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  display: inline-block;
}

.itemActions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
  flex-shrink: 0;
  min-width: 120px;
}

.priceBreakdown {
  text-align: right;
  font-size: 11px;
  width: 100%;

  .bagPrice,
  .totalPrice {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3px;
    padding: 2px 0;
    gap: 40px;
  }

  .strapPrices {
    margin-bottom: 6px;

    .strapPrice {
      margin-bottom: 2px;

      .strapPriceInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
      }

      .removeStrapPriceBtn {
        background: #ff4444;
        border: none;
        border-radius: 50%;
        width: 14px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        font-size: 6px;
        margin-left: 4px;

        &:hover {
          background: #cc0000;
        }
      }
    }
  }

  .totalPrice {
    font-weight: bold;
    border-top: 1px solid #eee;
    padding-top: 6px;
    margin-top: 6px;
    font-size: 12px;
  }

  .priceLabel {
    color: #666;
    font-size: 10px;
  }

  .priceValue {
    font-weight: 600;
    color: #333;
  }
}

.quantityControls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
}

.quantityBtn {
  background: #f9f9f9;
  border: none;
  padding: 6px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;

  &:hover {
    background-color: #eee;
    color: #333;
  }

  &:first-child {
    border-right: 1px solid #ddd;
  }

  &:last-child {
    border-left: 1px solid #ddd;
  }
}

.quantity {
  font-size: 12px;
  font-weight: 600;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background-color: #fff;
}

.removeBtn {
  background: #ff4444;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  font-size: 12px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #cc0000;
  }
}

.notice {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  font-size: 11px;
  line-height: 1.5;
  color: #666;
  text-align: center;
  flex-shrink: 0;

  strong {
    color: #333;
    display: block;
    margin-bottom: 8px;
    font-size: 12px;
  }

  p {
    margin: 0 0 6px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.cartSummary {
  background-color: #fff;
  border-bottom: 1px solid #eee;
  padding: 12px 16px;
  flex-shrink: 0;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
  color: #666;

  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 6px;
    margin-bottom: 6px;
  }
}

.total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
  font-weight: bold;
  font-size: 14px;
  color: #333;
  flex-shrink: 0;
}

.totalLabel {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.totalAmount {
  color: #333;
  font-size: 16px;
}

.actions {
  display: flex;
  padding: 12px 16px;
  gap: 12px;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.closeBtn,
.requestBtn {
  flex: 1;
  padding: 8px 16px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #fff3e2;
  color: #6e6e6e;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  border: 1px solid #000000;
}
.requestBtn:hover {
  border: 1px solid #000000;
}

// Responsive adjustments
@media (max-width: 480px) {
  .cartPanel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
  }

  .cartItem {
    flex-direction: column;
    gap: 12px;
  }

  .itemImages {
    flex-direction: row;
    justify-content: center;
    width: 100%;
    gap: 16px;
  }

  .bagImageContainer {
    width: 80px;
    height: 80px;
  }

  .itemActions {
    flex-direction: column;
    justify-content: space-between;
    align-items: end;
    width: 100%;
  }

  .priceBreakdown {
    text-align: left;
    margin-bottom: 8px;
  }

  .actions {
    flex-direction: column;
    gap: 8px;
  }
}
