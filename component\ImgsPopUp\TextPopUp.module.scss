.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 13%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.container {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 700px;
  max-height: 90%;
  overflow-y: auto;
  padding: 34px;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s ease;

  &:hover {
    color: #000;
  }
}

.detailsPanel {
  // position: fixed;
  // top: 48%;
  // right: 50px;
  // width: 60vh;
  background-color: transparent;
  // z-index: 1;
  // transform: translateY(-50%);

  h3 {
    color: #807f7f;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  p {
    color: #807f7f;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-top: 20px;
  }
}

.videoContainer {
  margin-top: 12px;
  width: 70%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  // margin-left: px;
}
@media only screen and (max-width: 768px) {
  .videoContainer {
    margin-left: auto;
    margin-right: auto;
  }
}

.strapVideo {
  display: block;
  width: 100%;
  height: auto;
}
