import React, { useRef, useEffect } from "react";
import styles from "./TestCom.module.scss";

const TestCom = () => {
  const scrollRef = useRef(null);
  const touchStartX = useRef(0);
  const touchStartScrollLeft = useRef(0);

  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartScrollLeft.current = scrollRef.current.scrollLeft;
  };

  const handleTouchMove = (e) => {
    if (scrollRef.current) {
      const touchX = e.touches[0].clientX;
      const touchDeltaX = touchX - touchStartX.current;
      scrollRef.current.scrollLeft = touchStartScrollLeft.current - touchDeltaX;
    }
  };

  const handleTouchEnd = () => {
    // No specific action needed on touch end
  };

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("touchstart", handleTouchStart);
      scrollContainer.addEventListener("touchmove", handleTouchMove);
      scrollContainer.addEventListener("touchend", handleTouchEnd);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("touchstart", handleTouchStart);
        scrollContainer.removeEventListener("touchmove", handleTouchMove);
        scrollContainer.removeEventListener("touchend", handleTouchEnd);
      }
    };
  }, []);

  return (
    <div ref={scrollRef} className={styles.scrollContainer}>
      <div className={styles.tabsContainer}>
        {[1, 2, 3, 4, 5, 6, 7].map((tab) => (
          <div key={tab} className={`${styles.tab}`}>
            <span className={styles.tabName}>{"Modal Name"}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TestCom;
