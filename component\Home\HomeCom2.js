import React, { useRef, useState, useEffect, Suspense } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
  ContactShadows,
  Environment,
  OrbitControls,
  PerspectiveCamera,
} from "@react-three/drei";
import * as THREE from "three";
import style from "./Home.module.scss";
import Microopen from "./Modal/Micropen";
import Phonepouch from "./Modal/Phonepouch";
import Totes from "./Modal/Totes";
import PanierBag from "./Modal/PanierBag";
import ToteM from "./Modal/ToteM";
import ToteL from "./Modal/ToteL";
import ToteMcloser from "./Modal/ToteMClosed";

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Three.js error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}

// Simple fallback component when 3D fails
const SimpleFallback = () => {
  return (
    <mesh>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="red" />
    </mesh>
  );
};

function SceneSetup({ children }) {
  const controlsRef = useRef();
  const [isMoving, setIsMoving] = useState(false);
  const [shouldReset, setShouldReset] = useState(false);
  const initialPosition = new THREE.Vector3(0, 0, 2);
  const initialTarget = new THREE.Vector3(0, 0, 0);

  useFrame((state, delta) => {
    if (shouldReset && controlsRef.current && !isMoving) {
      const currentPos = controlsRef.current.object.position;
      const currentTarget = controlsRef.current.target;

      currentPos.lerp(initialPosition, 0.05);
      currentTarget.lerp(initialTarget, 0.05);

      controlsRef.current.update();

      if (
        currentPos.distanceTo(initialPosition) < 0.01 &&
        currentTarget.distanceTo(initialTarget) < 0.01
      ) {
        setShouldReset(false);
      }
    }
  });

  const handleControlStart = () => {
    setIsMoving(true);
    setShouldReset(false);
  };

  const handleControlEnd = () => {
    setIsMoving(false);
    setShouldReset(true);
  };

  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight
        position={[2, 4, 2]}
        intensity={1}
        castShadow
        shadow-mapSize={[1024, 1024]}
        shadow-camera-far={10}
        shadow-camera-near={1}
        shadow-camera-top={3}
        shadow-camera-bottom={-3}
        shadow-camera-left={-3}
        shadow-camera-right={3}
      />

      <ContactShadows
        position={[0, -0.145, 0]}
        opacity={0.4}
        scale={4}
        blur={1.5}
        far={0.4}
        resolution={256}
        color="#000000"
      />

      <Environment preset="city" />

      <OrbitControls
        ref={controlsRef}
        enableZoom={true}
        enablePan={true}
        enableRotate={true}
        makeDefault
        minDistance={0.5}
        maxDistance={10}
        onStart={handleControlStart}
        onEnd={handleControlEnd}
      />
      <PerspectiveCamera makeDefault position={[0, 0, 2]} fov={45} />
      {children}
    </>
  );
}

// Function to render a specific model (lazy loaded)
const LazyModelRenderer = ({ modelIndex }) => {
  const models = [
    {
      component: Microopen,
      name: "Micro Open",
      position: [0, 0, 0],
      scale: 0.015,
    },
    {
      component: Phonepouch,
      name: "Phone Pouch",
      position: [0, -0.1, 0],
      scale: 0.015,
    },
    { component: Totes, name: "Tote S", position: [0, 0.02, 0], scale: 0.012 },
    {
      component: PanierBag,
      name: "Panier Bag",
      position: [0, 0.01, 0],
      scale: 0.012,
    },
    { component: ToteM, name: "Tote M", position: [0, -0.1, 0], scale: 0.013 },
    { component: ToteL, name: "Tote L", position: [0, -0.1, 0], scale: 0.01 },
  ];

  const model = models[modelIndex];
  const ModelComponent = model.component;

  return <ModelComponent position={model.position} scale={model.scale} />;
};

const MobileSlider = () => {
  const modelNames = [
    "Micro Open",
    "Phone Pouch",
    "Tote S",
    "Panier Bag",
    "Tote M",
    "Tote L",
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isWebGLSupported, setIsWebGLSupported] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Check WebGL support on mount
  useEffect(() => {
    try {
      const canvas = document.createElement("canvas");
      const gl =
        canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
      if (!gl) {
        setIsWebGLSupported(false);
        setErrorMessage("WebGL is not supported in your browser.");
      }
    } catch (e) {
      setIsWebGLSupported(false);
      setErrorMessage("Error checking WebGL support: " + e.message);
    }
  }, []);

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === modelNames.length - 1 ? 0 : prev + 1));
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev === 0 ? modelNames.length - 1 : prev - 1));
  };

  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = (e) => {
    touchEndX.current = e.changedTouches[0].clientX;
    handleSwipe();
  };

  const handleSwipe = () => {
    const difference = touchStartX.current - touchEndX.current;
    if (difference > 50) {
      handleNext();
    } else if (difference < -50) {
      handlePrev();
    }
  };

  // Error handler for Canvas
  const handleCanvasError = (error) => {
    console.error("Canvas error:", error);
    setIsWebGLSupported(false);
    setErrorMessage(
      "Error creating WebGL context. Please try a different browser."
    );
  };

  return (
    <div className={style.mobileSlider}>
      {isWebGLSupported ? (
        <div
          className={style.mobileCanvasContainer}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          <Canvas
            shadows
            gl={{
              alpha: true,
              antialias: false,
              powerPreference: "low-power",
              failIfMajorPerformanceCaveat: false,
            }}
            onCreated={({ gl }) => {
              gl.setClearColor(0x000000, 0);
              gl.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
            }}
            onError={handleCanvasError}
          >
            <ErrorBoundary fallback={<SimpleFallback />}>
              <Suspense fallback={null}>
                <SceneSetup>
                  <LazyModelRenderer modelIndex={currentIndex} />
                </SceneSetup>
              </Suspense>
            </ErrorBoundary>
          </Canvas>
        </div>
      ) : (
        <div className={style.webglError}>
          <p>{errorMessage}</p>
          <p>Please try using a different browser or device.</p>
        </div>
      )}

      <div className={style.mobileControls}>
        <button className={style.controlButton} onClick={handlePrev}>
          ←
        </button>
        <div className={style.modelName}>{modelNames[currentIndex]}</div>
        <button className={style.controlButton} onClick={handleNext}>
          →
        </button>
      </div>

      <div className={style.paginationDots}>
        {modelNames.map((_, index) => (
          <div
            key={index}
            className={`${style.dot} ${
              index === currentIndex ? style.activeDot : ""
            }`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  );
};

const HomeCom2 = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, []);

  // Your original desktop implementation
  const renderDesktop = () => (
    <div className={style.container}>
      <div className={style.canvasContainer}>
        <Canvas shadows gl={{ alpha: true, antialias: true }}>
          <Suspense fallback={null}>
            <SceneSetup>
              <Microopen position={[0, 0, 0]} scale={0.015} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>
      {/* ... other canvas containers ... */}
      <div className={style.canvasContainer}>
        <Canvas shadows gl={{ alpha: true }}>
          <Suspense fallback={null}>
            <SceneSetup>
              <Phonepouch position={[0, -0.1, 0]} scale={0.015} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>
      <div className={style.canvasContainer}>
        <Canvas
          shadows
          gl={{ alpha: true, antialias: true, preserveDrawingBuffer: true }}
        >
          <Suspense fallback={null}>
            <SceneSetup>
              <Totes position={[0, 0.02, 0]} scale={0.012} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>

      <div className={style.canvasContainer}>
        <Canvas shadows gl={{ alpha: true }}>
          <Suspense fallback={null}>
            <SceneSetup>
              <PanierBag position={[0, 0.01, 0]} scale={0.012} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>

      <div className={style.canvasContainer}>
        <Canvas shadows gl={{ alpha: true }}>
          <Suspense fallback={null}>
            <SceneSetup>
              <ToteMcloser position={[0, -0.1, 0]} scale={0.013} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>
      <div className={style.canvasContainer}>
        <Canvas shadows gl={{ alpha: true }}>
          <Suspense fallback={null}>
            <SceneSetup>
              <ToteL position={[0, -0.1, 0]} scale={0.01} />
            </SceneSetup>
          </Suspense>
        </Canvas>
      </div>
    </div>
  );

  return isMobile ? <MobileSlider /> : renderDesktop();
};

export default HomeCom2;
