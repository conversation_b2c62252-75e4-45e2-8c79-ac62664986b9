import React, { useEffect } from "react";
import styles from "./Loader.module.scss";
import { useRouter } from "next/router";

const Loaders = ({ redirectTo = null, redirectDelay = 5000 }) => {

  const router = useRouter();

  useEffect(() => {
    let timer;

    if (redirectTo) {
      // timer = setTimeout(() => {
      //   // window.location.href = redirectTo;
      // }, redirectDelay);
      router.push(redirectTo);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [redirectTo, redirectDelay]);

  return (
    <div className={styles.loaderContainer}>
      <div className={styles.loader}>
        {/* <div className={styles.spinner}></div> */}
        <img className={styles.img} src="/loader.gif" alt="wali loader" />
      </div>
    </div>
  );
};

export default Loaders;
