.container {
  position: relative;
  width: 100vw;
  height: 90vh;
  display: flex;
  background: transparent;
}
.canvasContainer {
  position: relative;
  width: 16.66%;
  height: 100%;
  background: transparent;
}
.picker {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}
.pickerTitle {
  margin-top: 10px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.mobileSlider {
  position: relative;
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  background: transparent;

  .mobileCanvasContainer {
    position: relative;
    width: 100%;
    height: 85%;
    background: transparent;
  }

  .mobileControls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 10px 20px;
    z-index: 10;

    .controlButton {
      background: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .modelName {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      padding: 5px 15px;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.8);
    }
  }

  .paginationDots {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px 0;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ccc;
      margin: 0 5px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &.activeDot {
        background-color: #333;
        transform: scale(1.2);
      }
    }
  }
}

// Make sure your existing container styles are preserved
.container {
  position: relative;
  width: 100vw;
  height: 90vh;
  display: flex;
  background: transparent;
}

.canvasContainer {
  position: relative;
  width: 16.66%;
  height: 100%;
  background: transparent;
}

.loaderContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loaderSpinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.line {
  width: 100%;
  height: 1px;
  background-color: #f3f1f1;
  margin: 10px 0;
  position: absolute;
  top: 57.5%;
}
