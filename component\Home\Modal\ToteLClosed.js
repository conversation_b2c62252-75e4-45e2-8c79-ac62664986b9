import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { proxy, useSnapshot } from "valtio";
import { Text } from "@react-three/drei";
import * as THREE from "three";
function ToteLcloser({ position, scale, setShowRotationAxes, setOpen }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/Tote_L_Closed.glb");
  const bagName = "Tote L";
  // Log the model's hierarchy to the console
  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });
  return (
    <>
    <group
      ref={ref}
      position={position}
      scale={scale || DEFAULT_SCALE} // Use the default scale if not provided
      rotation={[0, 0, 0]}
      onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
      dispose={null}
    >
      {Object.keys(nodes).map((key, index) => (
        <mesh
          key={index}
          geometry={nodes[key].geometry}
          material={new THREE.MeshStandardMaterial({ })}
        />
      ))}

    
    </group>
    {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={3.9}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}
export default ToteLcloser;
useGLTF.preload("/Tote_L_Closed.glb");