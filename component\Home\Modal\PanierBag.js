import { useMemo, useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import * as THREE from "three";
import { useFrame } from "@react-three/fiber";
import { proxy, useSnapshot } from "valtio";
import { Text } from "@react-three/drei";

function PanierBag({ position, scale }) {
  const ref = useRef();
  const [isHovered, setIsHovered] = useState(false);
  const { nodes, materials } = useGLTF("/paineropencomp.glb");
  const bagName = "Panier Bag";

  // Check if materials exist and Material property is available
  const customMaterial = useMemo(() => {
    if (!materials || !materials.Material) {
      // Return a default material if the model hasn't loaded yet
      return new THREE.MeshStandardMaterial({
        roughness: 0.5,
        metalness: 0.3,
        color: new THREE.Color("#333333"),
      });
    }

    const material = materials.Material.clone();
    material.roughness = 0.5;
    material.metalness = 0.3;
    material.color = new THREE.Color("#333333");
    return material;
  }, [materials]);

  useFrame((state, delta) => {
    if (!ref.current) return;

    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
// ref.current.rotation.y -= 0.0025;
    } else {
      easing.damp3(ref.current.position, position, 0.2, delta);
    }
  });

  // Check if nodes exist before rendering
  if (!nodes || !nodes.Mesh) {
    return null;
  }

  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        dispose={null}
        // onClick={() => {
        //   window.location.href = "/panierbag";
        // }}
      >
        <mesh
          geometry={nodes.Mesh.geometry}
          material={customMaterial}
          castShadow
          receiveShadow
        />
      </group>
      {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}

export default PanierBag;

useGLTF.preload("/paineropencomp.glb");
