import React, { useRef, useState, Suspense, useEffect } from "react";
import { <PERSON><PERSON>, useThree, useFrame, extend } from "@react-three/fiber";
import {
  useGLTF,
  ContactShadows,
  Environment,
  OrbitControls,
} from "@react-three/drei";
import { proxy, useSnapshot } from "valtio";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";
import styles from "../SingleProduct/SingleProduct.module.scss";
import { SketchPicker } from "react-color";
import * as THREE from "three"; // Import THREE

// Default scale for the bag and strap
const DEFAULT_SCALE = 0.08;

function CustomSphere({ position, onClick, color = "red", size = 0.1 }) {
  return (
    <mesh position={position} onClick={onClick}>
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
}


function ConcentricSpheres({ position, innerRadius = 0.1, outerRadius = 0.2, opacity = 0.5, onClick }) {
  const outerSphereRef = useRef();
  const [hovered, setHovered] = useState(false);

  useFrame(() => {
    if (outerSphereRef.current) {
      outerSphereRef.current.scale.x =
        outerSphereRef.current.scale.y =
        outerSphereRef.current.scale.z =
        THREE.MathUtils.lerp(outerSphereRef.current.scale.z, hovered ? 1.3 : 1, 0.1);
    }
  });

  return (
    <group position={position} onClick={onClick}>
      {/* Inner Sphere */}
      <mesh>
        <sphereGeometry args={[innerRadius, 32, 32]} />
        <meshStandardMaterial color="grey" transparent={false} />
      </mesh>
      {/* Outer Sphere */}
      <mesh
        ref={outerSphereRef}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <sphereGeometry args={[outerRadius, 32, 32]} />
        <meshStandardMaterial color="grey" transparent={true} opacity={opacity} />
      </mesh>
    </group>
  );
}

// Strap configurations
const STRAP_TYPES = {
  NONE: {
    id: "none",
    name: "No Strap",
    modelPath: null,
  },
  BALL: {
    id: "ball",
    name: "Ball Strap",
    modelPath: "/ballstrapmicrocomp.glb",
    scale: DEFAULT_SCALE,
    rotation: [0, 0, 0],
    position: [0, -0.5, 0],
  },
  HANDLE: {
    id: "handle",
    name: "Handle Strap",
    modelPath: "/handlestrapmicrocomp.glb",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE,
  },
  METAL: {
    id: "metal",
    name: "Metal Chain",
    modelPath: "/chain1microcomp.glb",
    position: [0, -0.5, 0],
    rotation: [0, 0, 0],
    scale: DEFAULT_SCALE,
  },
};

// State management
const state = proxy({
  currentStrap: STRAP_TYPES.NONE.id,
  zoomedPart: null,
  scale: DEFAULT_SCALE,
  zoomLevel: 50,
  isOpen: true, // Add state to track if the model is open or closed
  colors: {
    Bottom_Plate: "#fcc279",
    Outer_Layer: "#fcc279",
    Inner_Layer: "#fcc279",
    Magnet_Closure: "#fcc279",
    Object003: "#000000",
    Middle_Layer: "#fcc279",
    Plane009: "#fcc279",
    Plane011: "#fcc279",
    Object005: "#fcc279",
    // Add more parts as needed
  },
});

// Ball Chain Strap Component
function BallStrap(props) {
  const { nodes, materials } = useGLTF("/ballstrapmicrocomp.glb");
  if (!nodes) {
    console.error("Failed to load ball strap model");
    return null;
  }

  return (
    <group {...props} dispose={null}>
      <mesh
        geometry={nodes.Mesh.geometry}
        material={materials["Physically Based"]}
      />
      <mesh
        geometry={nodes.Mesh_1.geometry}
        material={materials.diffuse_0_0_0_255}
      />
    </group>
  );
}

// Generic Strap Component for other types
function GenericStrap({ type, ...props }) {
  const config = STRAP_TYPES[type.toUpperCase()];
  const { nodes, materials } = useGLTF(config.modelPath);

  return (
    <group {...props}>
      {Object.entries(nodes).map(([nodeName, node]) => {
        if (node.type === "Mesh") {
          return (
            <mesh
              key={nodeName}
              geometry={node.geometry}
              material={node.material}
              castShadow
              receiveShadow
            />
          );
        }
        return null;
      })}
    </group>
  );
}

// Strap Component
function Strap({ type, attachmentPoints }) {
  if (type === "none") return null;
  const config = STRAP_TYPES[type.toUpperCase()];
  if (!config || !config.modelPath) return null;

  const props = {
    scale: config.scale || DEFAULT_SCALE,
    rotation: config.rotation || [0, 0, 0],
    position: config.position || [0, 0, 0],
  };

  if (type === "ball" && attachmentPoints && attachmentPoints.length > 0) {
    const attachmentPoint = attachmentPoints[0];
    props.position = [
      attachmentPoint.position[0],
      attachmentPoint.position[1] - 0.8,
      attachmentPoint.position[2],
    ];
    props.rotation = attachmentPoint.rotation;
    return <BallStrap {...props} />;
  }

  if (type === "metal" && attachmentPoints && attachmentPoints.length > 0) {
    const attachmentPoint = attachmentPoints[0];
    props.position = [
      attachmentPoint.position[0],
      attachmentPoint.position[1] - 0.52,
      attachmentPoint.position[2],
    ];
    props.rotation = attachmentPoint.rotation;
    return <GenericStrap type={type} {...props} />;
  }

  if (type === "handle" && attachmentPoints && attachmentPoints.length > 0) {
    const attachmentPoint = attachmentPoints[0];
    props.position = [
      attachmentPoint.position[0],
      attachmentPoint.position[1] - 0.68,
      attachmentPoint.position[2],
    ];
    props.rotation = attachmentPoint.rotation;
    return <GenericStrap type={type} {...props} />;
  }

  return null;
}

function Microopen({ position, scale }) {
  const ref = useRef();
  const snap = useSnapshot(state);
  const { nodes } = useGLTF( "/microopencomp.glb");

  useEffect(() => {
    console.log("Model nodes:", nodes);
  }, [nodes]);

  const handlePartClick = (partName) => {
    state.zoomedPart = partName;
  };

  const handleSphereClick = (sphereName) => {
    console.log(`Sphere ${sphereName} clicked!`);
    // Add your custom logic here
  };

  const attachmentPoints = [
    { name: "Cylinder002", position: [0, 0, 0], rotation: [0, 0, 0] },
  ];


  const concentricSpheres = [
    { name:"actionpoint1",position: [6.4, 8, 0.5] }, // Adjust the position here
    { name:"actionpoint2",position: [-6.4, 0, 0.5] }, // Adjust the position here
    // Add more positions as needed
  ];

  return (
    <group
      ref={ref}
      position={position}
      scale={scale || DEFAULT_SCALE}
      // rotation={[0, 0]}
      dispose={null}
    >
      <mesh
        geometry={nodes.Bottom_Plate.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Bottom_Plate })
        }
        onClick={() => handlePartClick("Bottom_Plate")}
      />
      <mesh
        geometry={nodes.Magnet_Closure.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Magnet_Closure })
        }
        onClick={() => handlePartClick("Magnet_Closure")}
      />
      <mesh
        geometry={nodes.Object003.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Object003 })
        }
        onClick={() => handlePartClick("Object003")}
      />
      <mesh
        geometry={nodes.Cylinder002.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Cylinder002 })
        }
        onClick={() => handlePartClick("Cylinder002")}
      />
      <mesh
        geometry={nodes.Box005.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Box005 })}
        onClick={() => handlePartClick("Box005")}
      />
      <mesh
        geometry={nodes.Shape009.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape009 })
        }
        onClick={() => handlePartClick("Shape009")}
      />
      <mesh
        geometry={nodes.Mirror.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Mirror })}
        onClick={() => handlePartClick("Mirror")}
      />
      <mesh
        geometry={nodes.Outer_Layer.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Outer_Layer })
        }
        onClick={() => handlePartClick("Outer_Layer")}
      />
      <mesh
        geometry={nodes.Inner_Layer.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Inner_Layer })
        }
        onClick={() => handlePartClick("Inner_Layer")}
      />
      <mesh
        geometry={nodes.Middle_Layer.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Middle_Layer })
        }
        onClick={() => handlePartClick("Middle_Layer")}
      />
      <mesh
        geometry={nodes.Object005.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Object005 })
        }
        onClick={() => handlePartClick("Object005")}
      />
      <mesh
        geometry={nodes.Object006.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Object006 })
        }
        onClick={() => handlePartClick("Object006")}
      />
      <mesh
        geometry={nodes.Shape012.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape012 })
        }
        onClick={() => handlePartClick("Shape012")}
      />
      <mesh
        geometry={nodes.Shape014.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape014 })
        }
        onClick={() => handlePartClick("Shape014")}
      />
      <mesh
        geometry={nodes.Shape015.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape015 })
        }
        onClick={() => handlePartClick("Shape015")}
      />
      <mesh
        geometry={nodes.Shape016.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape016 })
        }
        onClick={() => handlePartClick("Shape016")}
      />
      <mesh
        geometry={nodes.Shape017.geometry}
        material={
          new THREE.MeshStandardMaterial({ color: snap.colors.Shape017 })
        }
        onClick={() => handlePartClick("Shape017")}
      />

      {attachmentPoints.map((point, index) => (
        <mesh key={index} position={point.position} scale={[0.05, 0.05, 0.05]}>
          <sphereGeometry args={[1, 32, 32]} />
          <meshStandardMaterial color="red" />
        </mesh>
      ))}


      {concentricSpheres.map((sphere, index) => (
        <ConcentricSpheres
          key={index}
          position={sphere.position}
          innerRadius={0.6}
          outerRadius={1}
          opacity={0.5}
          onClick={() => handleSphereClick(sphere.name)}
        />
      ))}
    </group>
  );
}




function Microcloser({ position, scale }) {
  const ref = useRef();
  const { nodes, materials } = useGLTF("/microclosed.glb");
  const snap = useSnapshot(state);
  // Log the model's hierarchy to the console
  useEffect(() => {
    console.log("Model nodes:", nodes);
  }, [nodes]);

  const handlePartClick = (partName) => {
    state.zoomedPart = partName;
  };

  const handleSphereClick = (sphereName) => {
    console.log(`Sphere ${sphereName} clicked!`);
    // Add your custom logic here
  };

  const attachmentPoints = [
    { name: "Cylinder002", position: [0, 0, 0], rotation: [0, 0, 0] },
  ];


  const concentricSpheres = [
    { name:"actionpoint1close",position: [6.4, 8, 0.5] }, // Adjust the position here
    { name:"actionpoint2close",position: [-6.4, 0, 0.5] }, // Adjust the position here
    // Add more positions as needed
  ];

  return (
    <group
      ref={ref}
      position={position}
      scale={scale || DEFAULT_SCALE} // Use the default scale if not provided
      rotation={[0, 0, 0]}
      dispose={null}
    >
      <mesh
        geometry={nodes.Bottom_Plate.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Bottom_Plate })}
      />
      <mesh
        geometry={nodes.Magnet_Closure.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Magnet_Closure })}
      />
      <mesh geometry={nodes.Object003.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Object003 })} />
      <mesh
        geometry={nodes.Cylinder002.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Cylinder002 })}
      />
      <mesh geometry={nodes.Box005.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Box005 })} />
      <mesh geometry={nodes.Shape009.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Shape009 })} />
      <mesh geometry={nodes.Mirror.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Mirror })} />
      <mesh
        geometry={nodes.Outer_Layer.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Outer_Layer })}
      />
      <mesh
        geometry={nodes.Inner_Layer.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Inner_Layer })}
      />
      <mesh
        geometry={nodes.Middle_Layer.geometry}
        material={new THREE.MeshStandardMaterial({ color: snap.colors.Middle_Layer })}
      />
      <mesh geometry={nodes.Object005.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Object005 })} />
      <mesh geometry={nodes.Object006.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Object005 })} />
      <mesh geometry={nodes.Shape010.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Shape010 })} />
      <mesh geometry={nodes.Shape011.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Shape011 })} />
      <mesh geometry={nodes.Shape012.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Shape012})} />
      <mesh geometry={nodes.Shape013.geometry} material={new THREE.MeshStandardMaterial({ color: snap.colors.Shape013 })} />

      {concentricSpheres.map((sphere, index) => (
        <ConcentricSpheres
          key={index}
          position={sphere.position}
          innerRadius={0.6}
          outerRadius={1}
          opacity={0.5}
          onClick={() => handleSphereClick(sphere.name)}
        />
      ))}    </group>
  );
}

function CameraZoom() {
  const snap = useSnapshot(state);
  const { camera } = useThree();

  useEffect(() => {
    const minZ = 1.5;
    const maxZ = 8;
    const zoomRange = maxZ - minZ;
    const targetZ = maxZ - ((snap.zoomLevel - 10) / 190) * zoomRange;

    const duration = 0.3;
    const currentZ = camera.position.z;

    let startTime = null;
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / (duration * 1000), 1);
      const easeProgress = 1 - Math.pow(1 - progress, 3);
      camera.position.z = currentZ + (targetZ - currentZ) * easeProgress;
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [snap.zoomLevel, camera]);

  return null;
}

// useGLTF.preload("/openwithoutchaincomp.glb");
useGLTF.preload("/modecomp.glb"); // Preload the closed model
Object.values(STRAP_TYPES).forEach((strap) => {
  if (strap.modelPath) {
    useGLTF.preload(strap.modelPath);
  }
});

const BagSingle = () => {
  const [isUserAdjustingSlider, setIsUserAdjustingSlider] = useState(false);
  const snap = useSnapshot(state);
  const controlsRef = useRef();

  const handleStrapChange = (e) => {
    console.log("Strap changed:", e.target.value);
    state.currentStrap = e.target.value;
  };

  const handleZoomChange = (value) => {
    setIsUserAdjustingSlider(true);
    state.zoomLevel = value;
  };

  const syncOrbitControlsZoom = () => {
    if (controlsRef.current && !isUserAdjustingSlider) {
      const distance = controlsRef.current.getDistance();
      const minZ = 1.5;
      const maxZ = 8;
      const zoomRange = maxZ - minZ;
      const calculatedZoom = 10 + 190 * (1 - (distance - minZ) / zoomRange);
      const roundedZoom = Math.round(calculatedZoom);
      if (Math.abs(roundedZoom - state.zoomLevel) > 2) {
        state.zoomLevel = roundedZoom;
      }
    }
  };

  const attachmentPoints = [
    { name: "Cylinder002", position: [0, 0, 0], rotation: [0, 0, 0] },
  ];

  const handleColorChange = (color) => {
    state.colors.Bottom_Plate = color.hex;
    state.colors.Magnet_Closure = color.hex;
    state.colors.Plane009 = color.hex;
    state.colors.Plane011 = color.hex;
    state.colors.Outer_Layer = color.hex;
    state.colors.Inner_Layer = color.hex;
    state.colors.Middle_Layer = color.hex;
    state.colors.Object005 = color.hex;
    state.colors.Object003 = color.hex;
  };

  const toggleModel = () => {
    state.isOpen = !state.isOpen;
  };

  return (
    <div className={styles.container}>
      <div className={styles.controlsContainer}>
        <select
          value={snap.currentStrap}
          onChange={handleStrapChange}
          className={styles.select}
          aria-label="Select strap type"
        >
          {Object.values(STRAP_TYPES).map((strap) => (
            <option key={strap.id} value={strap.id}>
              {strap.name}
            </option>
          ))}
        </select>
        <div className={styles.slider}>
          <Slider
            vertical
            min={10}
            max={200}
            value={snap.zoomLevel}
            onChange={handleZoomChange}
            aria-label="Zoom level"
          />
        </div>
        <button onClick={toggleModel} className={styles.toggleButton}>
          {snap.isOpen ? "Close Model" : "Open Model"}
        </button>
      </div>
      <div className={styles.canvasContainer}>
        <Canvas
          shadows
          camera={{
            position: [0, 0, 4],
            fov: 50,
            near: 0.1,
            far: 1000,
          }}
        >
          <Suspense fallback={null}>
            <ambientLight intensity={0.5} />
            <directionalLight position={[2, 4, 2]} intensity={1} castShadow />
            <ContactShadows
              position={[0, -0.145, 0]}
              opacity={0.4}
              scale={4}
              blur={1.5}
              far={0.4}
            />
            <Environment preset="city" />
            <OrbitControls
              ref={controlsRef}
              enableZoom={true}
              enablePan={true}
              enableRotate={true}
              minDistance={1.5}
              maxDistance={8}
              minPolarAngle={Math.PI / 4}
              maxPolarAngle={(Math.PI * 3) / 4}
              dampingFactor={0.1}
              rotateSpeed={0.7}
              enableDamping={true}
              onChange={syncOrbitControlsZoom}
            />
            {snap.isOpen ? (
              <Microopen position={[0, 0, 0]} scale={snap.scale} />
            ) : (
              <Microcloser position={[0, 0, 0]} scale={snap.scale} />
            )}
            <Strap
              type={snap.currentStrap}
              attachmentPoints={attachmentPoints}
            />
            <CameraZoom />
          </Suspense>
        </Canvas>
      </div>
      <div className={styles.colorPickers}>
        <SketchPicker
          color={snap.colors.Bottom_Plate}
          onChangeComplete={(color) => handleColorChange(color)}
        />
      </div>
    </div>
  );
};

export default BagSingle;
