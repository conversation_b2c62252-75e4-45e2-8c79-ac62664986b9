import { useRef, useState } from "react";
import { useGLTF } from "@react-three/drei";
import { easing } from "maath";
import { useFrame } from "@react-three/fiber";
import { proxy, useSnapshot } from "valtio";
import { Text } from "@react-three/drei";


function ToteScloser({ position, scale, setShowRotationAxes, setOpen }) {
  const ref = useRef();
  const { nodes, materials } = useGLTF("/Tote_S_Closed.glb");
  const bagName = "Tote S";
  const [isHovered, setIsHovered] = useState(false);
  // Log the model's hierarchy to the console

  useFrame((state, delta) => {
    if (isHovered) {
      easing.damp3(
        ref.current.position,
        [position[0], position[1] + 0.05, position[2]],
        0.2,
        delta
      );
      // ref.current.rotation.y -= 0.0025;
      const t = state.clock.getElapsedTime();
      ref.current.rotation.y = Math.sin(t / 4) / 4;
    } else {
      easing.damp3(ref.current.position.set(0,0,0), position, 0.2, delta);
      easing.dampE(ref.current.rotation, [0, 0, 0], 0.2, delta);
    }
  });
  return (
    <>
      <group
        ref={ref}
        position={position}
        scale={scale}
        rotation={[0, 0, 0]}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        onPointerMissed={() => (state.current = null)}
      >
 <mesh geometry={nodes.Node1.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node2.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node3.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node4.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node5.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node6.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node7.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node8.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node9.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node10.geometry} material={materials.x1} />
      <mesh geometry={nodes.Node11.geometry} material={materials.x1} />
    </group>
    {isHovered && (
        <Text
          position={[0, -0.22, 0]} // Adjust the position as needed
          scale={scale}
          fontSize={3}
          color="#000"
          anchorX="center"
          anchorY="bottom"
        >
          {bagName}
        </Text>
      )}
    </>
  );
}
export default ToteScloser;
useGLTF.preload('/Tote_S_Closed.glb')

