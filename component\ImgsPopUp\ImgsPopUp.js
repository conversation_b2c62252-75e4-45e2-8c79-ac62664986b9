import React, { useEffect } from "react";
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";
import styles from "./ImgsPopUp.module.scss";

// Import your images
const images = [
  "https://images.unsplash.com/photo-1596836471905-0f4255f13eb8?q=80&w=2068&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  "https://images.unsplash.com/photo-1517999144091-3d9dca6d1e43?q=80&w=2127&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  "https://plus.unsplash.com/premium_photo-1701069017776-1d6363816001?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  "https://images.unsplash.com/photo-1596836471905-0f4255f13eb8?q=80&w=2068&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  "https://images.unsplash.com/photo-1517999144091-3d9dca6d1e43?q=80&w=2127&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
  "https://plus.unsplash.com/premium_photo-1701069017776-1d6363816001?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
];

const bagImg = {
  micro: Array.from(
    { length: 12 },
    (_, i) => `/homebag/productimg/micro/${i + 1}.jpg`
  ),
  panier: Array.from(
    { length: 15 },
    (_, i) => `/homebag/productimg/panier/${i + 1}.jpg`
  ),
  pouch: Array.from(
    { length: 10 },
    (_, i) => `/homebag/productimg/pouch/${i + 1}.jpg`
  ),
  totes: Array.from(
    { length: 12 },
    (_, i) => `/homebag/productimg/totes/${i + 1}.jpg`
  ),
  totem: Array.from(
    { length: 14 },
    (_, i) => `/homebag/productimg/totem/${i + 1}.jpg`
  ),
  totel: Array.from(
    { length: 4 },
    (_, i) => `/homebag/productimg/totel/${i + 1}.jpg`
  ),
};

const ImgsPopUp = ({ onClose, name }) => {
  useEffect(() => {
    // Prevent body scroll when modal is open (but allow modal content to scroll)
    const originalStyle = window.getComputedStyle(document.body).overflow;
    document.body.style.overflow = "hidden";

    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, []);

  const handleOverlayClick = (e) => {
    // Only close if clicking the overlay itself, not the container
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleContainerClick = (e) => {
    // Prevent event bubbling to overlay
    e.stopPropagation();
  };

  return (
    <div
      className={styles.overlay}
      onClick={handleOverlayClick}
      // Prevent touch events on overlay from interfering with scrolling
      onTouchStart={(e) => e.target === e.currentTarget && e.preventDefault()}
    >
      <div
        className={styles.container}
        onClick={handleContainerClick}
        // Allow scrolling within container
        onTouchStart={(e) => e.stopPropagation()}
        onTouchMove={(e) => e.stopPropagation()}
      >
        <button
          className={styles.closeButton}
          onClick={onClose}
          // Ensure close button works on touch devices
          onTouchEnd={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onClose();
          }}
        >
          &times;
        </button>

        <div className={styles.masonryContainer}>
          <ResponsiveMasonry
            columnsCountBreakPoints={{
              450: 1, // 1 column for mobile
              750: 2, // 2 columns for larger screens
            }}
          >
            <Masonry gutter={10}>
              {bagImg[name].map((src, index) => (
                <div key={index} className={styles.imageContainer}>
                  <img
                    key={index}
                    src={src}
                    alt={`Gallery image ${index + 1}`}
                    style={{
                      width: "100%",
                      display: "block",
                      borderRadius: "8px",
                    }}
                    // Prevent image drag that might interfere with scrolling
                    draggable={false}
                    onTouchStart={(e) => e.stopPropagation()}
                  />
                </div>
              ))}
            </Masonry>
          </ResponsiveMasonry>
        </div>
      </div>
    </div>
  );
};

export default ImgsPopUp;
