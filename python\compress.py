import os
from PIL import Image

# Set the quality of the output image (higher is better quality but larger file size)
quality = 85

# Set the directory where the images are located
input_dir = './public/textures/'

# Function to compress PNG images
def compress_png(image_path):
    image = Image.open(image_path)
    image = image.convert("P", palette=Image.ADAPTIVE, colors=256)
    image.save(image_path, 'PNG', optimize=True, compress_level=9)

# Loop through all the files in the input directory
for filename in os.listdir(input_dir):
    # Check if the file is a PNG image
    if filename.lower().endswith('.png'):
        image_path = os.path.join(input_dir, filename)
        compress_png(image_path)

print('Done!')
