import Header from "@/component/Header/Header";
// import MicroBagProduct from "@/component/SingleProduct/MicroBagProduct";
import React from "react";

import dynamic from "next/dynamic";

const ToteMproduct = dynamic(
  () => {
    return import("@/component/SingleProduct/ToteMproduct");
  },
  { ssr: false }
);

const totem = () => {
  return (
    <>
      <Header />
      <ToteMproduct />
    </>
  );
};

export default totem;
