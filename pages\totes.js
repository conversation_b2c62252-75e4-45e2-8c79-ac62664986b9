import Header from "@/component/Header/Header";
// import MicroBagProduct from "@/component/SingleProduct/MicroBagProduct";
import React from "react";

import dynamic from "next/dynamic";

const ToteSproduct = dynamic(
  () => {
    return import("@/component/SingleProduct/ToteSproduct");
  },
  { ssr: false }
);

const totes = () => {
  return (
    <>
      <Header />
      <ToteSproduct />
    </>
  );
};

export default totes;
