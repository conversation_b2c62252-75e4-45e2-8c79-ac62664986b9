import Header from "@/component/Header/Header";
// import MicroBagProduct from "@/component/SingleProduct/MicroBagProduct";
import React from "react";

import dynamic from "next/dynamic";

const ToteLproduct = dynamic(
  () => {
    return import("@/component/SingleProduct/ToteLproduct");
  },
  { ssr: false }
);

const totel = () => {
  return (
    <>
      <Header />
      <ToteLproduct />
    </>
  );
};

export default totel;
