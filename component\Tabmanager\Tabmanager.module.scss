.tabManagerContainer {
  width: 100%;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  position: fixed;
  bottom: 0;
}

.subCon {
}

.tabNavigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.modelImage {
  width: 90px;
  height: 90px;
  overflow: hidden;
}

.modelImage1 {
  width: 80px;
  height: 80px;
  // overflow: hidden;
}

.modelImage img {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  transform: translateY(0);
}

.modelImage1 img {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  transform: translateY(0);
  transition: all 0.3s ease-in-out;
}

.modelImage1:hover img {
  transform: translateY(-15%);
}

.modelImage:hover img {
  transform: translateY(-15%);
}

.modelImage img.clicked {
  transform: translateY(-15%);
}

.shouderImage {
  width: 20px;
}

.shouderImage img.clicked {
  transform: translateY(-15%);
}

.shouderImage img {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  transform: translateY(0);
}

.shouderImage:hover img {
  transform: translateY(-15%);
}

.navButton {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 24px;
  color: #888;
  padding: 8px;
  display: flex;

  &:hover {
    color: #333;
  }

  &:focus {
    outline: none;
  }
}

.tabContent {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tabsContainer {
  display: flex;
  justify-content: space-between;
  // justify-content: center;
  align-items: center;
  gap: 20px;
  border-top: 1px solid #a49683;
  border-bottom: 1px solid #e5e5e5;
  padding: 0px 15px;
  background-color: white;
  position: relative;
  z-index: 5;
}

.tab {
  display: flex;
  align-items: center;
  cursor: pointer;
  // padding: 0 15px;
  position: relative;
  font-weight: 600;
  justify-content: center;
  // width: 200px;
  // text-align: center;
  /* Anti-flicker fixes */
  min-width: 0; /* Allows proper flexbox behavior */
  transition: all 0.2s ease-in-out; /* Smooth transitions */

  &:hover {
    .tabName {
      color: #333;
    }
  }

  &.active {
    .tabNumber,
    .tabName,
    .tabIcon {
      color: #808080;
    }
  }
}

.tabNumber {
  margin-right: 5px;
  color: #888;
  /* Anti-flicker fixes */
  flex-shrink: 0; /* Prevents number from shrinking */
  white-space: nowrap; /* Prevents wrapping */
}

.tabName {
  color: #888;
  // margin-right: 5px;
  /* Anti-flicker fixes - IMPORTANT CHANGES */
  white-space: nowrap; /* Prevents text wrapping */
  min-width: max-content; /* Ensures minimum width for content */
  text-align: left; /* Consistent alignment */
  transition: color 0.2s ease-in-out; /* Smooth color transitions only */
  width: 150px;
  text-align: center;
  text-transform: capitalize;

  /* Fixed width for tab with dynamic content (tab id 2) */
  &.dynamic-tab {
    min-width: 100px; /* Adjust this value based on your longest text */
    display: inline-block;
  }

  /* Alternative: Set specific widths for different tabs */
  &.tab-straps {
    min-width: 100px; /* Accommodates both "STRAPS" and "All Straps" */
  }

  &.tab-colour {
    min-width: 60px;
  }

  &.tab-shoulder {
    min-width: 120px;
  }

  &.tab-models {
    min-width: 70px;
  }

  &.tab-cart {
    min-width: 100px;
  }
}

.tabIcon {
  color: #888;
  /* Anti-flicker fixes */
  flex-shrink: 0; /* Prevents icon from shrinking */
}

/* Hover state container to prevent flickering */
.tab-hover-container {
  position: relative;
  display: inline-block;
}

/* For tabs with dynamic content, add a stabilizing wrapper */
.tab-content-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  min-height: 1.2em; /* Prevents height changes */
}

/* Invisible spacer for consistent width (alternative solution) */
.tab-spacer {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  white-space: nowrap;
  font-weight: 600;
  top: 0;
  left: 0;
  z-index: -1;
}

.modelsContent {
  width: 100%;
}

.modelOptions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.modelOption {
  cursor: pointer;
}

.shoulderOption {
  display: flex;
  justify-content: center;
  gap: 50px;
  margin-bottom: -20px;
}

.strapsContent {
  width: 100%;
}

.strapOptions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.strapOption {
  cursor: pointer;
}

.scrollContainer {
  position: relative;
  background-color: white;
}
.tabMainCon {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.cartBtn {
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff3e2;
  color: #6e6e6e;
  font-size: 12px;
  cursor: pointer;
  width: 105px;
  font-weight: 600;
  width: 100px;
  // margin-right: 52px;

  &:hover {
    background-color: #ccc;
  }
}

.addcartBtn {
  position: fixed;
  bottom: 150px;
  right: 50%;
  transform: translateX(50%);
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff3e2;
  color: #6e6e6e;
  font-size: 12px;
  cursor: pointer;
  width: 105px;
  font-weight: 600;
  width: 100px;
  // margin-right: 52px;

  &:hover {
    background-color: #ccc;
  }
}
.configText {
  font-size: 12px;
  color: #6e6e6e;
  flex-shrink: 0;
}

@media only screen and (max-width: 1024px) {
  .shouderImage {
    height: 115px;
  }

  .modelImage {
    width: 80px;
    height: 70px;
  }

  .modelImage {
    width: 60px;
    height: 60px;
  }

  /* Adjust tab name widths for tablet */
  .tabName {
    &.tab-straps {
      min-width: 80px;
    }

    &.tab-shoulder {
      min-width: 100px;
    }
  }
}

@media only screen and (max-width: 768px) {
  .scrollContainer {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-top: 1px solid #a49683;
  }
  .scrollContainer::-webkit-scrollbar {
    display: none;
  }

  .tabsContainer {
    // justify-content: center;
    border: none;

    gap: 50px;
  }
  .cartBtn {
    flex-shrink: 0;
  }

  .modelImage {
    width: 50px;
    height: 50px;
  }

  .modelImage1 {
    width: 45px;
    height: 47px;
  }

  .modelOptions {
    gap: 6px;
  }

  .shouderImage {
    width: 18px;
    height: 80px;
  }

  .shoulderOption {
    gap: 20px;
  }

  .tab {
    font-size: 14px;
    padding: 0 10px; /* Reduced padding for mobile */
  }

  .navButton {
    padding: 5px;
  }

  /* Adjust tab name widths for mobile */
  .tabName {
    font-size: 14px;
    width: auto;

    &.tab-straps {
      min-width: 70px;
    }

    &.tab-colour {
      min-width: 50px;
    }

    &.tab-shoulder {
      min-width: 80px;
    }

    &.tab-models {
      min-width: 60px;
    }

    &.tab-cart {
      min-width: 70px;
    }
  }

  .addcartBtn {
    bottom: 180px;
    left: 15px;
    transform: translateX(0);
    width: 105px;
    font-size: 10px;
  }
}

/* Container for loop/unloop buttons - positioned above Add to Cart */
.loopUnloopButtons {
  position: fixed;
  bottom: 200px; /* 50px above the Add to Cart button (which is at 150px) */
  right: 50%;
  transform: translateX(50%);
}

/* Loop button styles - size matched to Add to Cart button, original colors preserved */
.loopButton {
  padding: 3px 6px; /* Same size as addcartBtn */
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: rgba(158, 158, 158, 0.1); /* Original light transparent grey */
  color: #4a4a4a; /* Original color */
  font-size: 12px; /* Same size as addcartBtn */
  font-weight: 500; /* Original weight */
  cursor: pointer;
  width: 100px; /* Same width as addcartBtn */
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  outline: none;
  box-shadow: none;
  transition: none;
}

/* Optional active state - original styling */
.activeLoopButton {
  background-color: rgba(158, 158, 158, 0.3); /* Original slightly darker transparent grey */
  color: #2e2e2e;
}

/* Responsive design for loop button */
// @media (max-width: 768px) {
//   .loopUnloopButtons {
//     bottom: 230px; /* Adjust for mobile, keeping it above Add to Cart */
//     left: 50%; /* Change from 45% to 50% for perfect centering */
//     transform: translateX(-50%); /* Center the button */
//     position: absolute; /* Ensure positioning context */
//     margin: 0; /* Remove any default margins */
//     padding: 0; /* Remove any default padding */
//   }

//   .loopButton {
//     padding: 3px 6px; /* Same size as mobile addcartBtn */
//     font-size: 10px; /* Match mobile addcartBtn font size */
//     width: 105px; /* Match mobile addcartBtn width */
//     margin: 0; /* Remove any default margins */
//     box-sizing: border-box; /* Include padding/border in width calculation */
//   }
// }

// @media (max-width: 480px) {
//   .loopUnloopButtons {
//     bottom: 230px;
//     left: 50%; /* Keep at 50% */
//     transform: translateX(-50%);
//     position: absolute; /* Make sure positioning context is correct */
//     width: auto; /* Don't constrain width */
//     margin: 0; /* Remove any default margins */
//     padding: 0; /* Remove any container padding */
//     box-sizing: border-box; /* Include padding/border in width calculation */
//   }
   
//   .loopButton {
//     padding: 3px 6px;
//     font-size: 10px;
//     width: 105px;
//     margin: 0; /* Remove any default margins */
//     box-sizing: border-box; /* Include padding/border in width calculation */
//     display: inline-block; /* Ensure proper display */
//   }
// }